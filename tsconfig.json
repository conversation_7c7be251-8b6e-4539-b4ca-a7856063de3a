{
  "compilerOptions": {
    "target": "ES6",
    "lib": ["dom", "dom.iterable", "esnext"],
    "types": [
      "vite/client", // add asset imports (such as scss)
      "node",
      "vitest/globals",
      "@testing-library/jest-dom"
    ],
    "skipLibCheck": true,
    "baseUrl": "./src",
    "checkJs": true,
    "moduleResolution": "Bundler",
    "module": "Preserve",
    "resolveJsonModule": true,
    "jsx": "react",
    "noEmit": true,
    "allowImportingTsExtensions": true,
    "allowJs": true,
    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "paths": {
      "@helpers/*": ["./helpers/*"],
      "@fermions": ["./components/fermions/index.ts"],
      "@atoms/*": ["./components/atoms/*"],
      "@molecules/*": ["./components/molecules/*"],
      "@organisms/*": ["./components/organisms/*"],
      "@templates/*": ["./components/templates/*"],
      "@components/*": ["./components/*"],
      "@pages/*": ["./pages/*"],
      "@src/*": ["./*"],
      "@assets/*": ["../assets/*"]
    }
  },
  "build": {
    "sourceMap": true
  },
  "include": ["src/**/*"],
  "exclude": ["./node_modules", "./dist", "**/*.test.ts", "**/*.test.tsx"]
}
