package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ConcatTest {

    private val fn = Concat

    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(fn.functionName, fn.evaluatorFunction)
        return jsonataExpression
    }

    data class Spec(
        val input: String,
        val expected: Any,
    )

    fun provider(): Stream<Spec> {
        val functionName = fn.functionName
        return Stream.of(
            Spec("$$functionName('Hello', 'World')", "HelloWorld"),
            Spec("$$functionName('', '')", ""),
            Spec("$$functionName(0, 1)", "01"),
            Spec(
                "$$functionName([['A', 'B'],['C','D']],[['E', 'F'],['G','H']])",
                listOf(listOf("AE", "BF"), listOf("CG", "DH"))
            ),
            Spec(
                "$$functionName([['A'],['B']],'C')",
                listOf(listOf("AC"), listOf("BC"))
            ),
        )
    }

    @ParameterizedTest
    @MethodSource("provider")
    fun `equals test`(spec: Spec) {
        val jsonataExpression = jsonataWithFunctionRegistered(spec.input)
        val result = jsonataExpression.evaluate(null)
        result.shouldBe(spec.expected)
    }

}