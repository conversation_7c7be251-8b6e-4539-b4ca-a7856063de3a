package services.oneteam.ai.flow.execution

import kotlinx.serialization.json.*
import services.oneteam.ai.flow.execution.VariableDefinition.Variable

/**
 * Each map builder supports converting a particular variable type to a json map.
 */
interface MapBuilder {
    suspend fun match(variable: Variable): Boolean
    suspend fun handle(variable: Variable, cacheKey: String? = null): JsonElement
}

class DefaultMapBuilder : MapBuilder {
    override suspend fun match(variable: Variable): Boolean {
        return true
    }

    override suspend fun handle(variable: Variable, cacheKey: String?): JsonElement {
        return variable.toProperType() // put the correctly typed value into the map
    }
}
