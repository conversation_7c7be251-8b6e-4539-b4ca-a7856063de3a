package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import services.oneteam.ai.flow.support.pairwise.Pairwise
import services.oneteam.ai.flow.support.pairwise.PairwiseOperation
import services.oneteam.ai.flow.support.pairwise.PairwiseValue

@Serializable
internal object Concat : ComputeFunction {

    override val key = "concat"
    override val category = "string"
    override val icon = Icon("function")
    override val functionName = "CONCAT"
    override val description = "Concatenate values into a string"

    override val syntax = Syntax(
        "${functionName}(any, ... )", listOf(
            Argument("any", "Any (or Tensors) to compare", "any"),
            Argument("...", "Additional any (or Tensors) to compare", "any"),
        )
    )

    override val notes = """
        A Tensor can be a single value (scalar), an array of values (vector), or a matrix of values.
        Where the dimension of the inputs differ, the lower dimension applies to everything in the dimension above.
        If the length of arrays differs, the remaining elements of the shorter are treated as ‘nil’ (aka empty).
        The result will the same dimension as the highest Tensor.
        """.trimIndent()

    override val examples = listOf(
        Example(
            "$${functionName}('Hello', 'World') = 'HelloWorld'",
            "Concatenate values",
        )
    )

    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        return@JFunction Pairwise(PairwiseOperation.CONCATENATE).pairwise(args.map { PairwiseValue.of(it ?: "") }).value
    }, "<x+:x>")
}

