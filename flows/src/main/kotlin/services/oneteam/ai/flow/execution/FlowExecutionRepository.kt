package services.oneteam.ai.flow.execution

import org.jetbrains.exposed.sql.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.domains.NotFoundException
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.SortableFields
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.extensions.paginate

var checks = Checks()

class FlowExecutionRepository {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    companion object {
        val SORTABLE_FIELDS = SortableFields(
            mapOf(
                "id" to FlowExecutions.id,
                "startedAt" to FlowExecutions.startedAt,
                "finishedAt" to FlowExecutions.finishedAt,
                "status" to FlowExecutions.status,
                "isCanceled" to FlowExecutions.isCanceled,
                "result" to FlowExecutions.result,
                "createdAt" to FlowExecutions.createdAt,
                "flowConfigurationName" to FlowExecutions.flowConfigurationName
            )
        )

        val DEFAULT_SORTABLE_FIELDS = listOf("createdAt,desc")
    }

    fun create(
        tenant: Tenant,
        workspaceId: Workspace.Id,
        createFlowExecutionRequestBody: CreateFlowExecutionRequestBody,
        documentId: FlowExecution.DocumentId,
        flowConfigurationName: FlowConfiguration.Name
    ): FlowExecutionEntity {
        return FlowExecutionEntity.new {
            this.workspaceId = workspaceId.value
            this.tenantId = tenant.id
            this.status = FlowExecution.Status.PENDING
            this.result = FlowExecution.Result.PENDING
            this.documentId = documentId.value
            this.isCanceled = false
            this.flowConfigurationId = createFlowExecutionRequestBody.flowConfigurationId.value!!
            this.workspaceVersionId = createFlowExecutionRequestBody.workspaceVersionId.value
            this.flowConfigurationName = flowConfigurationName.value
            this.properties = FlowExecution.Properties(
                event = FlowExecution.Properties.TriggerEvent(
                    key = createFlowExecutionRequestBody.event.eventProperties.key,
                    eventProperties = createFlowExecutionRequestBody.event.eventProperties,
                ),
                variables = createFlowExecutionRequestBody.variables,
                settings = createFlowExecutionRequestBody.settings,
                eventGroupId = createFlowExecutionRequestBody.event.eventGroupId
            )
        }
    }

    fun search(
        pageRequest: PageRequest,
        searchTerm: String?,
        workspaceId: Long,
        status: FlowExecution.Status?,
        result: FlowExecution.Result?
    ): Page<FlowExecutionEntity> {
        val query = FlowExecutions
            .selectAll()
            .andWhere { FlowExecutions.workspaceId eq workspaceId }

        if (searchTerm?.isNotBlank() == true) {
            query.andWhere { FlowExecutions.flowConfigurationName.lowerCase() like "%${searchTerm.lowercase()}%" }
        }

        if (status != null) {
            query.andWhere { FlowExecutions.status eq status }
        }

        if (result != null) {
            query.andWhere { FlowExecutions.result eq result }
        }

        return query.paginate(pageRequest, SORTABLE_FIELDS) {
            FlowExecutionEntity.wrapRow(it)
        }
    }

    fun get(predicate: SqlExpressionBuilder.() -> Op<Boolean>): FlowExecutionEntity? {
        return FlowExecutionEntity.find { SqlExpressionBuilder.predicate() }.firstOrNull()
    }

    fun updateStatus(id: Long, status: FlowExecution.Status): FlowExecutionEntity {
        val entity = FlowExecutionEntity.findByIdAndUpdate(id) {
            it.status = status
        }
        return checks.exists(entity) { "FlowExecution $id not found" }
    }

    fun update(
        id: Long,
        updatePredicate: FlowExecutionEntity.(FlowExecutionEntity) -> Unit
    ): FlowExecutionEntity {
        val entity = FlowExecutionEntity.findById(id) ?: throw NotFoundException()
        entity.apply { updatePredicate(entity) }
        return entity
    }

    fun update(
        id: Long,
        flowMetadata: Metadata,
        flowStatus: FlowExecution.Status,
        flowResult: FlowExecution.Result
    ): FlowExecutionEntity {
        val entity = FlowExecutionEntity.findByIdAndUpdate(id) {
            it.startedAt = flowMetadata.startedAt
            it.finishedAt = flowMetadata.finishedAt
            it.status = flowStatus
            it.result = flowResult
        }
        return checks.exists(entity) { "FlowExecution $id not found" }
    }

}
