package services.oneteam.ai.flow.execution

import kotlinx.coroutines.withTimeoutOrNull
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.listeners.FlowRunListener
import services.oneteam.ai.flow.execution.listeners.FlowStepListener
import services.oneteam.ai.flow.execution.listeners.ListenerFactory
import services.oneteam.ai.flow.execution.step.ExecutionStepFactory
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import java.time.Instant

class FlowRunner(
    private val flowExecutionId: FlowExecution.Id,
    private val context: FlowContext,
    private val flow: FlowExecution.ForJson,
    private val listenerFactory: ListenerFactory,
    private val flowListeners: List<FlowRunListener>,
    private val stepListeners: List<FlowStepListener>,
    val flowExecutionService: FlowExecutionService,
    val timeoutMins: Long,
    val executionStepFactory: ExecutionStepFactory,
    private var currentFlowGetter: (rootFlow: FlowExecution.ForJson) -> FlowExecution.ForJson = { it }
) {
    private val logger: Logger = LoggerFactory.getLogger(javaClass)
    private val stepTrace = mutableListOf<FlowExecution.Step>()

    private fun setNewCurrentFlow(stepId: FlowExecution.Step.Id) {
        //this function chain can be applied to any FlowExecution.ForJson object,
        // and return the 'current' sub-flow at any depth (assuming the root you pass in actually has it at that path)
        val parentFlowGetter = currentFlowGetter
        currentFlowGetter = { rootFlow ->
            try {
                parentFlowGetter(rootFlow).steps[stepId]!!.subFlows[stepId.value]!!

            } catch (e: Exception) {
                throw FlowRunnerException(
                    "Error setting new current flow for step $stepId. The likely cause is that the flow passed in was not the root flow",
                    e
                )
            }
        }
    }

    fun spawnNestedFlowRunner(
        context: FlowContext, flow: FlowExecution.ForJson, path: String
    ): FlowRunner {
        val listenerFactory = listenerFactory.clone(path)
        return FlowRunner(
            flowExecutionId,
            context,
            flow,
            listenerFactory,
            listenerFactory.createFlowListenersForSubFlow(),
            listenerFactory.createStepListeners(),
            flowExecutionService,
            timeoutMins,
            executionStepFactory,
            currentFlowGetter
        )
    }

    fun updateSubFlowListenerFactory(path: String): ListenerFactory {
        return listenerFactory.clone(path)
    }

    private suspend fun startSteps() {
        var isFlowCancelled = false

        // do everything inside here so errors will fail the flow
        try {
            loadStartingVariables()
            var next = flow.start
            if (next?.value.isNullOrEmpty()) {
                logger.error("Flow has no start task ${context.global.flowConfigurationName} / ${context.global.flowConfigurationId}")
                throw IllegalArgumentException("Flow has no start task")
            }

            do {
                val flowExecution = flowExecutionService.get(flowExecutionId)
                if (flowExecution?.isCanceled == true) {
                    isFlowCancelled = true
                    flow.state.message = "Flow execution was cancelled by user"
                    logger.info("Cancelling Flow execution")
                    break
                }

                val step = flow.steps[next] ?: throw FlowRunnerException("Task $next not found in flow")

                // check for loops
                if (stepTrace.contains(step)) {
                    throw FlowRunnerException("Task $next is already in the trace, possible loop in the flow")
                }
                stepTrace.add(step)

                // prepare step
                val localContext = prepareStep(step, context)
                // execute
                next = executeStep(step, localContext)

            } while (!next?.value.isNullOrEmpty())

            flow.state.result = FlowExecution.Result.SUCCESS

        } catch (e: Exception) {
            logger.error("Error executing flow", e)
            flow.state.result = FlowExecution.Result.FAILED
            flow.state.message = "Flow execution failed due to error: ${e.message}"
        } finally {
            flow.state.status = FlowExecution.Status.COMPLETED
            flow.state.metadata.finishedAt = Instant.now()
            if (isFlowCancelled) {
                flow.state.result = FlowExecution.Result.CANCELLED
            }

            logger.info("Flow Metrics {}", flow.generateMetrics().toString())
        }
    }

    suspend fun start() {
        var wasTimedOut = false

        try {

            flow.state.status = FlowExecution.Status.RUNNING
            flow.state.result = FlowExecution.Result.PENDING
            flow.state.metadata.startedAt = Instant.now()

            flowListeners.forEach { it.onFlowStarted(flow) }

            val result = withTimeoutOrNull(timeMillis = timeoutMins * 1000 * 60) {
                logger.trace("Flow {} STARTED with timeout of {} minutes", flow.name, timeoutMins)
                flow.state.message = "Flow execution completed normally"
                startSteps()
                return@withTimeoutOrNull true
            }

            if (result == null) {
                logger.error("Flow execution timed out after waiting for $timeoutMins minutes")
                wasTimedOut = true
            }

            logger.trace("Flow {} FINISHED with timeout of {} minutes", flow.name, timeoutMins)

        } finally {
            flow.state.status = FlowExecution.Status.COMPLETED
            var cancellation: FlowExecution.Properties.Cancellation? = null;
            if (wasTimedOut) {
                flow.state.result = FlowExecution.Result.CANCELLED
                flow.state.message = "Flow execution was cancelled due to timeout"

                cancellation = FlowExecution.Properties.Cancellation(
                    FlowExecution.CancelTriggerType.TIME_LIMIT,
                    triggeredAt = Instant.now(),
                    properties = FlowExecution.Properties.Cancellation.Properties(
                        reason = "Exceeded execution time limit of $timeoutMins minutes",
                        timeoutMins = timeoutMins,
                    )
                )
            }
            flowListeners.forEach {
                it.onFlowFinished(
                    flow, cancellation
                )
            }
        }
    }

    private suspend fun loadStartingVariables() {
        flow.startingVariables?.forEach { value ->
            val existingVariable = context.variables[value.identifier]
            if (existingVariable != null) {
                context.set(
                    VariableDefinition.Variable(
                        existingVariable.value,
                        value.type,
                        value.identifier,
                    )
                )
            } else {
                context.set(
                    VariableDefinition.Variable(
                        TypeToJsonElementConverter.defaultByType(value.type),
                        value.type,
                        value.identifier,
                    )
                )
            }
        }
    }

    private fun prepareStep(
        step: FlowExecution.Step, context: FlowContext
    ): FlowContextWithLocalStep {

        val ctx = FlowContextWithLocalStep(step.id, context)
        return ctx
    }

    private suspend fun executeStep(
        step: FlowExecution.Step, context: FlowContextWithLocalStep
    ): FlowExecution.Step.Id? {

        val stepState = FlowExecution.State.Step(
            FlowExecution.State.Step.Id(step.id.value),
            metadata = Metadata(
                startedAt = Instant.now(), finishedAt = null
            ),
            FlowExecution.State.Step.Status.RUNNING,
        )
        flow.state.steps?.add(stepState)

        logger.trace("Step {} STARTED", step.id.value)

        try {
            stepListeners.forEach { it.onStepStarted(flow, step, stepState, context) }

            val executionStep = executionStepFactory.createStep(step, this)

            // preprocess any variables
            logger.trace("Populating step {} with context `{}` ", step.id.value, context)
            executionStep.populate(context)
            logger.trace("Populated step {} with context `{}` ", step.id.value, context)
            stepListeners.forEach { it.onStepUpdated(step) }

            logger.trace("Executing step {}", step.id.value)
            val next = executionStep.execute(context)

            stepState.result = FlowExecution.State.Step.Result.SUCCESS
            return next

        } catch (e: Exception) {
            logger.error("Error executing step ${step.id.value}", e)
            stepState.result = FlowExecution.State.Step.Result.FAILED
            stepState.message.add(e.message ?: "Error executing step ${step.id.value}")
            throw FlowRunnerException(
                "Error executing step '${step.name}' (${step.id.value}) in flow '${context.flowContext.global.flowConfigurationName.value}' (${context.flowContext.global.flowConfigurationId.value}) for flow execution ${flowExecutionId.value}",
                e
            )
        } finally {
            logger.trace("Step {} FINISHED", step.id.value)

            stepState.status = FlowExecution.State.Step.Status.COMPLETED
            stepState.metadata.finishedAt = Instant.now()
            stepListeners.forEach { it.onStepFinished(flow, step, stepState, context) }
        }

    }

}

