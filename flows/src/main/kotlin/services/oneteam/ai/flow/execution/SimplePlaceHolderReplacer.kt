package services.oneteam.ai.flow.execution

import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonPrimitive
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.expression.JsonPathFromJsonObjectExpressionEvaluator
import services.oneteam.ai.flow.expression.RegExTokenizer

/**
 * A class that replaces jsonPath placeholders in a string with values from a JsonObject.
 */
class SimplePlaceHolderReplacer(val noValueStrategy: NoValueStrategy = NoValueStrategy.BLANK) {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    /**
     * A tokenizer that identifies quoted placeholders in the format "{{key}}".
     * It uses a regular expression to match the pattern.
     */
    val quotedTokenizer = RegExTokenizer("\"{{", "}}\"")
    val unquotedTokenizer = RegExTokenizer("{{", "}}")

    /**
     * Replaces placeholders in the input string with corresponding values from the map.
     *
     * @param input The input string containing placeholders in the format {{key}}.
     * @param placeholderValues A map where keys are placeholder names and values are the corresponding replacement values.
     * @return The input string with placeholders replaced by their corresponding values.
     */
    suspend fun replacePlaceholders(input: String, valueProvider: PlaceholderValueProvider): String {
        // find tokens to replace
        val tokens = unquotedTokenizer.tokenize(input)

        val values = valueProvider.valuesForPlaceholders(tokens.toSet())
        val expressionEvaluator = JsonPathFromJsonObjectExpressionEvaluator(values)

        // replace each token with quoted and unquoted in that order
        var result = input
        for (token in tokens) {

            // derive value to use for the token
            var value = expressionEvaluator.evaluate(token)
            if (value == null) {
                logger.trace("No value found for token {} using blank string", token)
                value = noValueStrategy.producer()
            }
            // use json representation of the value to maintain compatibility with json
            val valueAsString = Json.encodeToString(value)

            /*
             * replace those values where the whole token is quoted
             * eg `"lhs": "{{CountOfAssignee}}"` -> `"lhs": 1` - (number is unquoted)
             * eg `"lhs": "{{name}}"` -> `"lhs": "Bob"`
             */
            result = quotedTokenizer.replace(result, token, valueAsString)
            /*
             * replace those values where the token is not the whole value using an unquoted value
             * eg "type": "form.{{formConfigurationId}}" -> "type": "form.aqt"
             */
            result = unquotedTokenizer.replace(result, token, valueAsString.trim('\"'))
        }
        return result
    }

}

enum class NoValueStrategy(val producer: () -> JsonElement) {
    BLANK({ JsonPrimitive("") }),
    NULL({ JsonNull }),
}