package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import services.oneteam.ai.flow.support.pairwise.Pairwise
import services.oneteam.ai.flow.support.pairwise.PairwiseOperation
import services.oneteam.ai.flow.support.pairwise.PairwiseValue
import services.oneteam.ai.shared.extensions.convertToNiceBigDecimal
import services.oneteam.ai.shared.extensions.toBigDecimalList
import java.math.BigDecimal

/**
 * $SUM
 * https://help.hcl-software.com/dom_designer/12.0.0/basic/H_OPERATIONS_ON_LISTS.html
 *
 *
 * if many single numbers given, add them all together
 *
 * 1,2,3 => 6
 *
 * if a single array of numbers given, add them together to return a single number
 *
 * [1,2,3] => 6
 *
 * if multiple arrays of numbers given, zip-add them together to return an array of numbers
 *
 * [1,2,3],[1,2,3] => [2,4,6]
 *
 * if arrays of arrays of numbers given, zip and add together
 *
 * [[1,2,3],[1,2,3]], [[1,2,3],[1,2,3]] → [[2,4,6],[2,4,6]],[[2,4,6],[2,4,6]])
 *
 * If the dimensions of an input differs, the lower dimension applies to everything in the dimension above
 *
 * [1,2,3], 2 => [2,4,5]
 * [[1,2],[3,4]], 1
 * → [[1,2]+1, [3,4]+1]
 * → [[2,3], [4,5]
 * [[1,2],[3,4]], [1,2]
 * → [[1,2]+[1,2] , [3,4]+[1,2]]
 * → [[2,4], [4,6]]
 *
 * If the length of arrays differs, the remaining elements of the shorter are treated as ‘nil’ (aka empty)
 * [1,2,3], [1,2] => [2,4,3]
 *
 * final result is reflective of the highest dimension where things are applied left-to-right
 * [1,2,3], 4, [[5,6]]
 * → [1+4+[5,6], 2+4+[0,0], 3+4+[0,0]]
 * → [5+[5,6], [6+[0,0], 7+[0,0]]
 * → [[5+5, 5+6], [6+0,6+0], [7+0, 7+0]]
 * → [[10,11], [6,6], [7,7]]
 */
@Serializable
internal object Sum : ComputeFunction {
    override val key = "sum"
    override val syntax = Syntax(
        "SUM(number, ... )", listOf(
            Argument("number", "Numbers (or Tensors) to sum", "number"),
            Argument("...", "Additional numbers (or Tensors) to sum", "number"),
        )
    )
    override val description = "Sums the Numbers (or Tensors) together"
    override val notes = """
        A Tensor can be a single number (scalar), an array of numbers (vector), or a matrix of numbers.
        Where the dimension of the inputs differ, the lower dimension applies to everything in the dimension above.
        If the length of arrays differs, the remaining elements of the shorter are treated as ‘nil’ (aka empty).
        The result will the same dimension as the highest Tensor.
        """.trimIndent()
    override val examples = listOf(
        Example(
            "\$SUM(1,2,3) => 6",
            "Sums all the numbers together",
        ),
        Example(
            "\$SUM([1,2,3],[1,2,3]) => [2,4,6]",
            "Pairwise sums two lists of numbers together",
        ),
        Example(
            "\$SUM([1,2,3], 5) => [6,7,8]",
            "Lower dimension is uplifted (5 -> [5,5,5]) then pairwise added",
        ),
        Example(
            "\$SUM([1,2,3], [4,5]) => [5,7,3]",
            "Pairwise sums two lists of numbers together, shorter list is padded with 0",
        ),
        Example(
            "\$SUM([1, 2], [[3, 4, 5], [6, 7, 8]])",
            """
                Dimension uplift and padded then pairwise addition.
                [1,2] + [[3,4,5], [6,7,8]] => [[4,6,5], [7,9,8]]
            """.trimIndent(),
        ),
    )
    override val category = "math"
    override val icon = Icon("function")
    override val functionName = "SUM"

    val implementations: List<SumFunction> = listOf(
        FuzzySum(),
        FuzzyListSum(),
        SumPairwise()
    )

    @Transient
    override val evaluatorFunction = JFunction({ _, args ->

        return@JFunction implementations.first { it.match(args) }.let {
            return@JFunction (it.sum(args))
        }.getOrElse { throw IllegalArgumentException("Invalid arguments for sum function") }

    }, "<x+:x>")

}

interface SumFunction {
    fun match(args: List<*>): Boolean
    fun sum(args: List<*>): Any
}

class FuzzyListSum : SumFunction {
    override fun match(args: List<*>): Boolean {
        return args.size == 1 && args[0] is List<*>
    }

    override fun sum(args: List<*>): Number {
        var res = BigDecimal.ZERO
        (args[0] as List<*>).toBigDecimalList().forEach { res = res.add(it) }
        return res.convertToNiceBigDecimal()
    }
}

class FuzzySum : SumFunction {
    override fun match(args: List<*>): Boolean {
        return args.all { it !is List<*> }
    }

    override fun sum(args: List<*>): Number {
        var res = BigDecimal.ZERO
        args.toBigDecimalList().forEach { res = res.add(it) }
        return res.convertToNiceBigDecimal()
    }
}

/**
 * if multiple arrays of numbers given, assume we are doing pairwise addition
 */
class SumPairwise : SumFunction {

    override fun match(args: List<*>): Boolean {
        return true // act as a catch-all
    }

    override fun sum(args: List<*>): Any {
        return Pairwise(PairwiseOperation.ADD).pairwise(args.map { PairwiseValue.of(it ?: 0) }).value
    }

}
