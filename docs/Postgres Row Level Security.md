## Introduction to Row Level Security (RLS)

## Intellij

You can run the following SQL commands via Intellij.

When inside an sql block press Command + Enter to execute the SQL commands, or right click and select "Execute".
There will be a tool bar at the top that lets you select the datasource and schema.

Because you need to run multiple SQL commands (first to set the connection parameters, and second to run the actual SQL
to get the data you want),
you will need to switch transactions to manual mode.

> Remember to roll back or commit your changes at the end since leaving the transaction open will lock the database
> and other processes (like your server) will not be able to access the database.

> Right click on the file tab and choose "Attach datasource" to attach the datasource to the markdown file.

![Attach datasource](images/Attach%20datasource%20to%20markdown%20file.png)

> You can run SQL commands in markdown files in Intellij by right clicking on the SQL and choosing "Execute" or
> Command + Enter.

![Intellij Markdown SQL](images/Running%20sql%20in%20intellij%20markdown%20file.png)

## Row Level Security (RLS) in Postgres

We have policies on tables that restrict access to rows based on the current tenant and user.
This is done using PostgreSQL's Row Level Security (RLS) feature.

See [DatabaseVerification.kt](../shared/src/main/kotlin/services/oneteam/ai/shared/DatabaseVerification.kt)
and [DatabaseUtils.kt](../shared/src/main/kotlin/services/oneteam/ai/shared/database/DatabaseUtils.kt) for the
implementation of RLS in our application.

> Note: You can bypass RLS by using the superuser role - connecting with the `otai_superuser` user.

For tenant isolation, we have a `tenant_id` column in each table that is used to filter rows based on the current
tenant.

```sql
CREATE POLICY tenant_isolation_policy ON forms
    AS PERMISSIVE
    FOR ALL
    USING (tenant_id = (CURRENT_SETTING('app.current_tenant_id'::TEXT, TRUE))::INTEGER);
```

And for workspace isolation, we have a `workspace_id` column in each table that is used to filter rows based on the
the relationships between user and workspaces defined in the workspace_users table.

```sql
DROP POLICY IF EXISTS workspace_isolation_policy_all ON forms;


CREATE POLICY workspace_isolation_policy_all ON forms
    AS RESTRICTIVE
    FOR ALL
    USING (((CURRENT_SETTING('app.is_system_user'::TEXT, TRUE))::BOOLEAN = TRUE) OR
           (workspace_id IN (SELECT workspace_users.workspace_id
                             FROM workspace_users
                             WHERE ((workspace_users.status = 'ACTIVE'::TEXT) AND (workspace_users.user_id = (COALESCE(
                                     NULLIF(CURRENT_SETTING('app.current_principal_id'::TEXT, TRUE), ''::TEXT),
                                     '-1'::TEXT))::INTEGER)))));
```

### Users

Find a valid user id by using the following SQL commands:

```sql
SELECT *
FROM users;

SELECT *
FROM users
WHERE email = '<EMAIL>';
```

### Connection parameters

For Row Level security to work, you need to set the following connection parameters in your SQL client:

```sql
SELECT SET_CONFIG('app.current_tenant_id', 1::TEXT, TRUE),
       SET_CONFIG('app.current_principal_id', 1::TEXT, TRUE),
       SET_CONFIG('app.is_system_user', FALSE::TEXT, TRUE);
```

Change the values to match your current tenant, user and whether the user is a system user or not.
If you haven't set these parameters, you will probably get an error like this:

```text
[22P02] ERROR: invalid input syntax for type integer: ""
```

### Example SQL commands

Use the following SQL commands to test RLS in your database.
See how the results change based on the connection parameters you set.

```sql
SELECT *
FROM workspaces;
```

```sql
SELECT *
FROM forms;
```

```sql
SELECT *
FROM forms
WHERE workspace_id = ?;
```

