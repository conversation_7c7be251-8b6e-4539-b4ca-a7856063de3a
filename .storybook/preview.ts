import "@oneteam/onetheme/index.css";
import type { Preview } from "@storybook/react";
import { initialize } from "msw-storybook-addon";

import "../src/App.tsx";

// Initialize MSW
initialize({
  onUnhandledRequest: "bypass"
});

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i
      }
    }
  },
  tags: ["autodocs"]
};

export default preview;
