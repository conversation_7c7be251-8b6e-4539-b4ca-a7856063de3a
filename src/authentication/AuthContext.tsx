import { createContext } from "react";

export type AuthUser = {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  image?: string;
  tenantId: number;
  hostId: number;
  autoLogoutIdleTimer: {
    enabled: boolean;
    value: number;
  };
};

export type UserLastActiveTabs = { [key: string]: string };

interface AuthContextType {
  user: AuthUser | null;
  loadedInitially: boolean;
  redirectUrl: string | null;
  getAuthRedirectUrl: (redirectUrl: string | null) => string;
  lastActiveTabs: UserLastActiveTabs;
  updateLastActiveTabs: (lastActiveTabs: UserLastActiveTabs) => void;
}

export const AuthContext = createContext<AuthContextType>({
  user: null,
  loadedInitially: false,
  redirectUrl: null,
  getAuthRedirectUrl: () => "",
  lastActiveTabs: {},
  updateLastActiveTabs: () => {}
});
