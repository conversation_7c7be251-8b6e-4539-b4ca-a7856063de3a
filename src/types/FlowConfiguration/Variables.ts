import { IconType } from "@oneteam/onetheme";

import { QuestionTypes, questionTypeIcon } from "../Question";
import { commonIcons } from "./../../constants/iconConstants";
import { Condition } from "./Condition";
import { FlowStepVariant } from "./FlowStep/FlowStep";

export enum VariableTypes {
  FORM = "form",
  FOUNDATION = "foundation",
  FORM_CONFIGURATION = "formConfiguration",
  FOUNDATION_CONFIGURATION = "foundationConfiguration",
  QUESTION_CONFIGURATION = "questionConfiguration",
  SERIES_INTERVAL = "seriesInterval",
  SERIES_CONFIGURATION = "seriesConfiguration"
}

export enum CustomTypes {
  FORM_MINIMAL = "form.minimal",
  FOUNDATION_MINIMAL = "foundation.minimal",
  QUESTION = "question"
}

export enum CustomDynamicTypes {
  FORM = "form.{{formConfigurationId}}",
  FOUNDATION = "foundation.{{foundationConfigurationId}}"
}

export type VariableType =
  | `${VariableTypes}`
  | `${QuestionTypes}`
  | `${CustomTypes}`
  | `${CustomDynamicTypes}`
  | string;

export enum VariableTypeVariant {
  STANDARD = "standard",
  CUSTOM = "custom",
  CUSTOM_DYNAMIC = "customDynamic"
}

export enum TableVariableColumn {
  ADD = "ADD",
  DELETE = "DELETE",
  UPDATE = "UPDATE",
  REORDER = "REORDER"
}

export const variableTypeIcons: {
  [key in VariableType]: IconType;
} = {
  ...questionTypeIcon,
  [CustomTypes.FORM_MINIMAL]: commonIcons.forms,
  [CustomDynamicTypes.FORM]: commonIcons.forms,
  [CustomTypes.FOUNDATION_MINIMAL]: commonIcons.foundations,
  [CustomDynamicTypes.FOUNDATION]: commonIcons.foundations,
  [VariableTypes.SERIES_INTERVAL]: commonIcons.series,
  [VariableTypes.SERIES_CONFIGURATION]: {
    name: "settings"
  },
  [VariableTypes.FORM_CONFIGURATION]: {
    name: "settings"
  },
  [VariableTypes.FOUNDATION_CONFIGURATION]: {
    name: "settings"
  },
  [VariableTypes.QUESTION_CONFIGURATION]: {
    name: "settings"
  }
  // list: { name: "data_array" }
};

export type VariableValue = string | number | boolean | unknown | null;
export type VariableIdentifier = string;
// Almost same as our question configuration
export type VariableConfiguration = {
  type: string; // QuestionType or CustomType
  id?: string;
  identifier: VariableIdentifier;
  description?: string;
  properties?: {
    columns?: VariableConfiguration[];
    items?: VariableConfiguration[];
    hidden?: Condition | boolean;
    required?: Condition | boolean;
    // same properties as QuestionConfiguration properties for each type
  } & { [key: string]: unknown };
  // For starting variables
  text?: string;
};

export type Variable = VariableConfiguration & {
  value?: VariableValue;
};

/**
 * User editable fields
 * This type allows you to dynamically access and assign values
 * to the type, identifier, and value properties using a key of type {@link VariableFieldKeys}.
 * see handleSaveSetVariablesField in {@link ConfigurationFlowStepFields}
 */
export type VariableFieldKeys = keyof typeof VARIABLE_FIELD_KEYS;

export enum VARIABLE_FIELD_KEYS {
  type = "type",
  identifier = "identifier",
  value = "value",
  "properties.operation" = "properties.operation",
  "properties.columns" = "properties.columns",
  "properties.columnIdentifier" = "properties.columnIdentifier",
  "properties.rowIdentifier" = "properties.rowIdentifier",
  "properties.rowIndex" = "properties.rowIndex",
  "properties.listOperation" = "properties.listOperation",
  "properties.itemIndex" = "properties.itemIndex",
  "properties.fileOperation" = "properties.fileOperation",
  "properties.fileIndex" = "properties.fileIndex"
}

export const setVariablesTypeOptions = [
  QuestionTypes.TEXT,
  QuestionTypes.NUMBER,
  QuestionTypes.BOOLEAN,
  QuestionTypes.DATE,
  QuestionTypes.TABLE,
  QuestionTypes.JSON,
  QuestionTypes.LIST,
  QuestionTypes.FILES
];

// Used for the mock variables in the FE -> never saved to document / backend
export type VariableTypeDefinition = {
  // Path that is copied (defaults to the current path to item)
  __path?: string;
  // Type of variable
  __type?: VariableType;
  // Display a description for the user
  __description?: string;
  // "Name" of the variable
  __identifier?: string;
  // Configuration -> for more detailed typing
  __configuration?: VariableConfiguration;
  __sourceStepId?: string;
  __availableFromStepId?: string;
  __sourceStepVariant?: `${FlowStepVariant}`;
  // For iterator starting variables
  __iteratorParentId?: string;
  __isAggregateOutput?: boolean;
} & { [key: string]: string | VariableTypeDefinition | unknown | undefined };
