import { z } from "zod";

import {
  MAX_KEY_LENGTH,
  MIN_KEY_LENGTH,
  commonErrors
} from "@src/constants/errorMessages.ts";
import { Dictionary } from "@src/hooks/useDictionary.tsx";

import { entityMetadataSchema } from "./EntityMetadata";

export const newFoundationSchema = (d: Dictionary) =>
  z.object({
    name: z.string().trim().min(1),
    key: z
      .string()
      .trim()
      .min(2, {
        message: d(commonErrors.length, {
          name: d("ui.common.key"),
          min: MIN_KEY_LENGTH,
          max: MAX_KEY_LENGTH
        })
      })
      .max(20, {
        message: d(commonErrors.length, {
          name: d("ui.common.key"),
          min: MIN_KEY_LENGTH,
          max: MAX_KEY_LENGTH
        })
      })
      .regex(/^[a-zA-Z0-9]*$/, {
        message: d(commonErrors.alphanumeric, {
          name: d("ui.common.key"),
          constraint: d("errors.common.constraint")
        })
      }),
    workspaceId: z.number(),
    parentId: z.number().optional().nullable(),
    foundationConfigurationId: z.string()
  });

export const foundationSchema = (d: Dictionary) =>
  z.intersection(
    newFoundationSchema(d),
    z.object({
      id: z.number(),
      metadata: entityMetadataSchema.optional(),
      properties: z.record(z.any()).optional()
    })
  );

export type NewFoundation = z.infer<ReturnType<typeof newFoundationSchema>>;

export type Foundation = z.infer<ReturnType<typeof foundationSchema>>;

export class FoundationForUpdate implements Foundation {
  constructor(
    public id: number,
    public name: string,
    public key: string,
    public workspaceId: number,
    public foundationConfigurationId: string,
    public parentId?: number | undefined | null
  ) {}

  static fromFoundation(foundation: Foundation) {
    return new FoundationForUpdate(
      foundation.id,
      foundation.name,
      foundation.key,
      foundation.workspaceId,
      foundation.foundationConfigurationId,
      foundation.parentId
    );
  }
}

export class FoundationForCreate implements NewFoundation {
  constructor(
    public name: string,
    public key: string,
    public workspaceId: number,
    public foundationConfigurationId: string,
    public parentId?: number | undefined | null
  ) {}

  static fromFoundation(foundation: Foundation) {
    return new FoundationForCreate(
      foundation.name,
      foundation.key,
      foundation.workspaceId,
      foundation.foundationConfigurationId,
      foundation.parentId
    );
  }
}
