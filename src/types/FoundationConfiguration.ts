import { z } from "zod";

import { ErrorFieldKeyMap } from "@helpers/DocumentErrorHelper.ts";
import { valueUniqueness } from "@helpers/stringHelper.ts";

import {
  MAX_NAME_LENGTH,
  MIN_NAME_LENGTH,
  commonErrors
} from "@src/constants/errorMessages.ts";
import { Dictionary } from "@src/hooks/useDictionary.tsx";

export enum FoundationRelationship {
  ONE_TO_ONE = "OneToOne",
  ONE_TO_MANY = "OneToMany"
}

export interface FoundationFormType {
  foundations: FoundationConfiguration[];
}

export const newFoundationConfigurationSchema = (d: Dictionary) =>
  z.object({
    name: z
      .string()
      .trim()
      .min(2, {
        message: d(commonErrors.length, {
          name: d("ui.common.name"),
          min: MIN_NAME_LENGTH,
          max: MAX_NAME_LENGTH
        })
      })
      .max(100, {
        message: d(commonErrors.length, {
          name: d("ui.common.name"),
          min: MIN_NAME_LENGTH,
          max: MAX_NAME_LENGTH
        })
      }),
    description: z.string().optional().nullable(),
    relationship: z.nativeEnum(FoundationRelationship).optional(),
    metadata: z
      .object({
        createdAt: z.string(),
        updatedAt: z.string()
      })
      .optional()
  });

export const foundationConfigurationSchema = (d: Dictionary) =>
  z.intersection(
    newFoundationConfigurationSchema(d),
    z.object({
      identifier: z.string(),
      id: z.string()
    })
  );

export type FoundationConfiguration = z.infer<
  ReturnType<typeof foundationConfigurationSchema>
> & {
  errors?: ErrorFieldKeyMap[] | null;
};

export const foundationConfigurationListSchema = (
  d: Dictionary,
  locale: string
) =>
  z.object({
    foundations: z
      .array(foundationConfigurationSchema(d))
      .superRefine((items, ctx) => {
        /*
    Check for duplicate names

    This will add an error to those fields that have duplicate names - but when we are editing we can use trigger(path) to display
    the error message on only the field being edited.
    */

        // build a map of field values and their count
        const valueCount = new Map<string, number>();
        items
          .map(i => i.identifier)
          .forEach(identifier => {
            const lowerIdentifier = identifier.toLocaleLowerCase(locale);
            valueCount.set(
              lowerIdentifier,
              (valueCount.get(lowerIdentifier) ?? 0) + 1
            );
          });

        // add error for each field that has a duplicate name (count > 1)
        items.forEach((item, index) => {
          const lowerIdentifier = valueUniqueness(item.identifier, locale);
          const count = valueCount.get(lowerIdentifier) ?? 0;
          if (count > 1) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: d(commonErrors.duplicate, {
                name: d("ui.common.identifier")
              }),
              path: [index, "identifier"]
            });
          } else {
            valueCount.set(lowerIdentifier, 1);
          }
        });
        return true;
      })
  });
