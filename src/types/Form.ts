import { z } from "zod";

import { Dictionary } from "@src/hooks/useDictionary.tsx";
import { entityMetadataSchema } from "@src/types/EntityMetadata.ts";
import { foundationSchema } from "@src/types/Foundation.ts";

// https://github.com/colinhacks/zod/discussions/1789

export type FormPath = Array<string | number>;

export const newFormSchema = (d: Dictionary) =>
  z
    .object({
      foundationId: z.number(),
      formConfigurationId: z
        .string({
          message: d(`errors.common.required`, {
            name: d("ui.terminology.form")
          })
        })
        .trim()
        .min(
          1,
          d(`errors.common.required`, {
            name: d("ui.terminology.form")
          })
        ),
      seriesId: z.string().optional(), // series id needs to be set based on the form configuration
      intervalId: z.string().optional().nullable()
    })
    .superRefine((val, ctx) => {
      // if a chosen form is associated with a series, intervalId is required
      if (val.seriesId && !val.intervalId) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: d("errors.common.required", {
            name: d("ui.terminology.interval")
          }),
          path: ["intervalId"]
        });
      }
    });

export const formSchema = (d: Dictionary) =>
  z.intersection(
    newFormSchema(d),
    z.object({
      id: z.number(),
      documentId: z.string(),
      annotationDocumentId: z.string(),
      metadata: entityMetadataSchema.optional(),
      foundation: foundationSchema(d),
      properties: z.record(z.any()).optional()
    })
  );

export type NewForm = z.infer<ReturnType<typeof newFormSchema>>;

export type ExistingForm = z.infer<ReturnType<typeof formSchema>>; // avoids name clash with onetheme Form
