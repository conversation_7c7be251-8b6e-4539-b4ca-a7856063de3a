export enum AnnotationLocationVariant {
  QUESTION = "question",
  ANSWER = "answer",
  HIGHLIGHT = "highlight",
  WHITEBOARD = "whiteboard"
}

export type BaseQuestionLocation = {
  questionId: string;
};

export type TableColumnLocation = BaseQuestionLocation & {
  // BaseQuestionLocation questionId is table question
  columnId: string; // table column question id
};

export type TableRowLocation = BaseQuestionLocation & {
  // BaseQuestionLocation questionId is table question
  rowId: string; // table rowId
};

export type TableCellLocation = BaseQuestionLocation &
  // BaseQuestionLocation questionId is table question
  TableRowLocation &
  TableColumnLocation;

export type JsonOrListItemLocation = BaseQuestionLocation & {
  itemId: string;
};

// Ignore for now
// export type SectionLocation = {
//   sectionId: string;
// };

export type QuestionLocation = { variant: "question" } & (
  | BaseQuestionLocation
  | TableColumnLocation
  | TableRowLocation
  | TableCellLocation
  | JsonOrListItemLocation
);

export type SectionLocation = {
  variant: "section";
  sectionId: string;
};

// For comment that comes with adding an answer
export type AnswerLocation = {
  variant: "answer";
  answer?: {
    // TODO: add some answer specific info
    documentVersionId: string;
  };
} & (
  | BaseQuestionLocation
  | TableColumnLocation
  | TableRowLocation
  | TableCellLocation
  | JsonOrListItemLocation
);

// export type HighlightLocation = {
//   variant: "highlight"; // todo: fix to add "spot"
//   selection: {
//     // start: number;
//     // end: number;
//     // TODO: better name?
//     spot: "text" | "description" | "answer";
//   };
// } & (
//   | BaseQuestionLocation
//   | TableColumnLocation
//   | TableRowLocation
//   | TableCellLocation
// );

export enum HighlightColor {
  COLOR_1 = "1",
  COLOR_2 = "2",
  COLOR_3 = "3",
  COLOR_4 = "4",
  COLOR_5 = "5"
}

export type WhiteboardCoordinateLocation = {
  variant: "whiteboard";
  x: number;
  y: number;
  // TODO: perhaps relative to question location, section location, etc.
  // relativeTo?: {
  //   variant: "question" | "section" | "column" | "row" | "cell" | "step";
  //   // can then be `step-${stepId}`, `section-${sectionId}` (html element ID)
  //   id: string;
  // };
};

export type AnnotationLocation =
  | undefined
  | SectionLocation
  | QuestionLocation
  | AnswerLocation
  // Not yet supported
  // | HighlightLocation
  | WhiteboardCoordinateLocation;
