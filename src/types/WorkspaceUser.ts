import { EntityMetadata } from "./EntityMetadata";

export enum WorkspaceUserStatus {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE"
}

export enum WorkspaceAccessLevel {
  COLLECTION = "COLLECTION",
  CONFIGURATION = "CONFIGURATION",
  SETTINGS = "SETTINGS"
}

export interface WorkspaceUserSearch {
  id: number;
  workspaceId: number;
  user: {
    id: number;
    email: string;
    tenantId: number;
    properties: {
      firstName: string;
      lastName: string;
    };
  };
  accessLevel: Array<`${WorkspaceAccessLevel}`>;
  status: `${WorkspaceUserStatus}`;
  metadata: {
    createdAt: string;
    updatedAt: string;
  };
}

export type WorkspaceUser = {
  id: number;
  userId: number;
  firstName: string;
  lastName: string;
  email: string;
  image?: string;
  isOwner?: boolean;
  status: `${WorkspaceUserStatus}`;
  levels: Array<`${WorkspaceAccessLevel}`>;
  metadata?: EntityMetadata & {
    lastActive?: string; // ISO string for last active time
  };
};

export type NewWorkspaceUser = {
  workspaceId: number;
  userId: number;
  accessLevel: Array<`${WorkspaceAccessLevel}`>;
  status: `${WorkspaceUserStatus}`;
};

export type WorkspaceUserForUpdate = {
  id: number;
  workspaceId: number;
  userId: number;
  accessLevel?: Array<`${WorkspaceAccessLevel}`>;
  status?: `${WorkspaceUserStatus}`;
};

export const buildQueryParamsForWorkspaceUserSearch = (
  searchFilter?: string | undefined,
  queryParams?: URLSearchParams
): URLSearchParams => {
  const params = new URLSearchParams();
  if (searchFilter) {
    params.set("search", searchFilter ?? "");
  }

  queryParams?.forEach((value, key) => {
    params.append(key, value);
  });

  return params;
};
