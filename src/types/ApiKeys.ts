import { z } from "zod";

import { MAX_NAME_LENGTH, MIN_NAME_LENGTH } from "@src/constants/errorMessages";
import { Dictionary } from "@src/hooks/useDictionary";

type IsoDateString = string; // ISO 8601 date string

export type ApiKey = {
  id: number;
  name: string;
  description?: string;
  // scope: string[]
  metadata?: {
    createdAt: IsoDateString;
    updatedAt: IsoDateString;
  };
};

export type RevealedApiKey = ApiKey & {
  apiKey: string;
};

export const getApiKeyForCreateSchema = (d: Dictionary) =>
  z.object({
    name: z
      .string()
      .trim()
      .min(2, {
        message: d("errors.common.length", {
          name: d("ui.common.name"),
          min: MIN_NAME_LENGTH,
          max: MAX_NAME_LENGTH
        })
      })
      .max(100, {
        message: d("errors.common.length", {
          name: d("ui.common.name"),
          min: MIN_NAME_LENGTH,
          max: MAX_NAME_LENGTH
        })
      })
      .regex(/^[a-zA-Z][a-zA-Z_0-9]*$/, {
        message: d("errors.common.alphanumeric", {
          name: d("ui.common.name"),
          constraint: d("errors.common.constraint")
        })
      }),
    description: z
      .string()
      .trim()
      .max(500, {
        message: d("errors.common.maxLength", {
          name: d("ui.common.description"),
          length: 500
        })
      })
      .optional()
  });

export type ApiKeyForCreate = z.infer<
  ReturnType<typeof getApiKeyForCreateSchema>
>;
