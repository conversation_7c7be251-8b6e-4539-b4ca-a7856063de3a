import { AnyDocumentId, Repo } from "@automerge/automerge-repo";

import {
  CollaborationDocument,
  CollaborationDocumentType,
  ValidationResult,
  Workspace
} from "./documentTypes.js";
import { Logger } from "./helpers/logger.ts";

const log = Logger("otai:server:documentValidation");

/**
 * Currently we only validate workspace documents. If we start to validate other types of documents
 * we can make this more generic.
 */
export class DocumentValidation {
  private readonly token: string;
  private readonly repo: Repo;
  private readonly postData: (
    endpoint: string,
    body: object,
    token: string
  ) => Promise<ValidationResult>;

  constructor(
    token: string,
    repo: Repo,
    postData: (
      endpoint: string,
      body: object,
      token: string
    ) => Promise<ValidationResult>
  ) {
    this.token = token;
    this.repo = repo;
    this.postData = postData;
  }

  async validate(documentId: AnyDocumentId) {
    const handle = await this.repo.find<CollaborationDocument>(documentId);

    const collaborationDocument = handle.doc();
    if (!collaborationDocument) {
      log.warn("[Validation] Document not found for ID:", documentId);
      return;
    }

    try {
      if (
        collaborationDocument.type ===
        CollaborationDocumentType.WORKSPACE_CONFIGURATION
      ) {
        await this.validateWorkspace(documentId, collaborationDocument);
      } else {
        log.warn(
          `[storage document update] validation skipped for ${documentId}`
        );
      }
    } catch (e) {
      log.error("[Validation] Couldn't validate document", e);
    }
  }

  async validateWorkspace(
    documentId: AnyDocumentId,
    collaborationDocument: CollaborationDocument
  ) {
    const doc = collaborationDocument as Workspace;
    const payload = await this.postData("/validate", doc, this.token);
    if (JSON.stringify(payload.errors) === JSON.stringify(doc.errors)) {
      log.info("[storage document validate] NO CHANGES");
      return;
    }
    const docHandle = await this.repo.find<Workspace>(documentId);
    docHandle.change(doc => {
      doc.errors = payload.errors;
    });
  }
}
