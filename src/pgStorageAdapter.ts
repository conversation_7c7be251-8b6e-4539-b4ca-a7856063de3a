import type {
  Chunk,
  StorageAdapterInterface,
  StorageKey
} from "@automerge/automerge-repo";
import { EventEmitter } from "events";
import path from "path";
import pg from "pg";

import { appConfig } from "./helpers/appConfig.ts";
import { Logger } from "./helpers/logger.ts";

const { Pool } = pg;

const log = Logger("otai:pgStorageAdapter");

const getKey = (key: string[]) => path.join(...key);
export class PgStorageAdapter implements StorageAdapterInterface {
  pool: pg.Pool;
  private readonly eventEmitter: EventEmitter;

  constructor(
    config?: pg.PoolConfig,
    private readonly tenantId: number = appConfig.tenantId,
    private readonly tableName: string = "automerge_data"
  ) {
    this.pool = new Pool(config);
    this.pool.on("error", (err: Error) => {
      log.error("[pool error]", err);
    });
    this.eventEmitter = new EventEmitter();
  }

  async query(statement: string, values: unknown[]) {
    log.info("[query]", statement);
    let client: pg.PoolClient | undefined;
    try {
      log.info(
        `pool size: ${this.pool.totalCount}, idle: ${this.pool.idleCount}, waiting: ${this.pool.waitingCount}`
      );
      client = await this.pool.connect();
      await client.query("BEGIN");
      const tenant = `SELECT set_config('app.current_tenant_id', $1::text, TRUE)`;
      await client.query(tenant, [`${this.tenantId}`]);
      const result = await client.query(statement, values);
      await client.query("COMMIT");
      return result;
    } catch (e) {
      log.error("[query] error", e);
      await client?.query("ROLLBACK");
      throw e;
    } finally {
      log.info("[query] done");
      client?.release();
    }
  }

  async load(key: StorageKey): Promise<Uint8Array | undefined> {
    const myKey = getKey(key);
    log.info("[load]", myKey);
    const res = await this.query(
      `SELECT length(data) as length, data
       FROM ${this.tableName}
       WHERE id = $1::text`,
      [myKey]
    );
    if (res.rowCount == 0) {
      log.warn("[load] not found");
      return undefined;
    }
    log.info("[load] found");
    const buffer = res.rows?.[0]?.data as Buffer;
    log.info(
      "[load] result size vs buffer size",
      res.rows?.[0]?.length,
      buffer?.length
    );
    return new Uint8Array(buffer);
  }

  async save(key: StorageKey, data: Uint8Array): Promise<void> {
    const myKey = getKey(key);
    log.info("[save]", myKey);
    const buffer = Buffer.from(data);
    await this.query(
      `INSERT INTO ${this.tableName} (id, data, tenant_id)
       VALUES ($1::text, $2, $3)
       ON CONFLICT (id) DO UPDATE SET data = $2`,
      [myKey, buffer, this.tenantId]
    );
    this.eventEmitter.emit("update", { documentId: key[0] });
    log.info("[save] done");
  }

  async remove(key: StorageKey): Promise<void> {
    const myKey = getKey(key);
    log.info("[remove]", myKey);
    await this.query(
      `DELETE
       FROM ${this.tableName}
       WHERE id = $1::text`,
      [myKey]
    );
    log.info("[remove] done");
  }

  async loadRange(keyPrefix: StorageKey): Promise<Chunk[]> {
    const myKey = getKey(keyPrefix) + "%";
    log.info("[loadRange]", myKey);
    const res = await this.query(
      `SELECT id, data
       FROM ${this.tableName}
       WHERE id LIKE $1::text`,
      [myKey]
    );
    log.info(`[loadRange]: ${res.rowCount}`);
    return res.rows.map(({ id, data }: { id: string; data: Buffer }) => ({
      key: id.split("/"),
      data: new Uint8Array(data)
    }));
  }

  async removeRange(keyPrefix: StorageKey): Promise<void> {
    const myKey = getKey(keyPrefix) + "%";
    log.info("[removeRange]", myKey);
    await this.query(`DELETE ${this.tableName} WHERE id LIKE $1::text`, [
      myKey
    ]);
    log.info("[removeRange] done");
  }

  on(event: "update" | "remove", listener: (data: unknown) => void) {
    this.eventEmitter.on(event, listener);
  }
}
