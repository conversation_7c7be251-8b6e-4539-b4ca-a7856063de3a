// original code: MIT License
import * as automerge from "@automerge/automerge";
import {
  AnyDocumentId,
  DocumentId,
  NetworkAdapterInterface,
  PeerId,
  Repo,
  RepoConfig
} from "@automerge/automerge-repo";
import { NodeWSServerAdapter } from "@automerge/automerge-repo-network-websocket";
import { useAzureMonitor } from "@azure/monitor-opentelemetry";
import cookieParser from "cookie-parser";
import dotenv from "dotenv";
import express from "express";
import jwt from "jsonwebtoken";
import _ from "lodash";
import os from "os";
import path, { dirname } from "path";
import { fileURLToPath } from "url";
import { WebSocketServer } from "ws";

import { DocumentValidation } from "./DocumentValidation.js";
import type {
  ExecutionStep,
  FlowExecutionDocument,
  Workspace
} from "./documentTypes.ts";
import { appConfig } from "./helpers/appConfig.ts";
import { upsertDocAtPosition } from "./helpers/getValueAtPath.ts";
import { Logger } from "./helpers/logger.ts";
import { postData } from "./helpers/postData.ts";
import {
  AuthenticatedRequest,
  getUser,
  sessionAuthentication
} from "./middleware/SessionCookie.ts";
import eh from "./middleware/exceptionHandler.ts";
import { PgStorageAdapter } from "./pgStorageAdapter.ts";
import answerRoutes from "./routes/answerRoutes.ts";
import documentRoutes from "./routes/documentRoutes.ts";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const log = Logger("otai:server");
dotenv.config();

if (process.env.APPLICATIONINSIGHTS_CONNECTION_STRING) {
  try {
    useAzureMonitor();
  } catch (e) {
    log.error("[useAzureMonitor] could not start up:", e);
  }
}

const jwtToken = appConfig.jwtToken;
const jwtPublicKey = appConfig.jwtPublicKey;
const dbConfig = appConfig.dbConfig;

log.info(
  "[DB_CONFIG]",
  `maxPoolSize: ${dbConfig.maxPoolSize}, idleTimeoutMillis: ${dbConfig.idleTimeoutMillis}, connectionTimeoutMillis: ${dbConfig.connectionTimeoutMillis}`
);

export class Server {
  private readonly socket: WebSocketServer;
  private readonly server: ReturnType<import("express").Express["listen"]>;
  public readonly repo: Repo;
  constructor() {
    const hostname = os.hostname();
    log.info("[constructor]", hostname);

    this.socket = new WebSocketServer({ noServer: true });

    const PORT = appConfig.port;
    const app = express();

    // Increase the request size limit
    app.use(express.json({ limit: "1mb" })); // Adjust the limit as needed

    app.use(express.text());
    app.use(cookieParser());

    const peerId = `storage-server-${hostname}` as PeerId;
    const tenantId = appConfig.tenantId;
    const storage: PgStorageAdapter = new PgStorageAdapter(
      {
        max: dbConfig.maxPoolSize,
        idleTimeoutMillis: dbConfig.idleTimeoutMillis,
        connectionTimeoutMillis: dbConfig.connectionTimeoutMillis
      },
      tenantId
    );
    const config: RepoConfig = {
      network: [
        new NodeWSServerAdapter(
          // this.socket is a (NodeJS) WebSocketServer instance, but the constructor expects an isomorphic-ws:WebSocketServer which differs slightly.
          // The API (that's used) is the same, but the types are different so we tell TSC to ignore the type error
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          this.socket as any
        ) as unknown as NetworkAdapterInterface
      ],
      storage,
      peerId,
      sharePolicy: async () => false
    };
    this.repo = new Repo(config);

    storage.on("update", async (data: unknown) => {
      const documentId = (data as { documentId: AnyDocumentId }).documentId;
      log.info("[document update]", documentId);
      await new DocumentValidation(jwtToken, this.repo, postData).validate(
        documentId
      );
    });

    app.get("/", (_req, res) => {
      log.info("OK");
      res.send("OK");
    });

    app.get("/build", (_, res) =>
      res.sendFile(path.join(__dirname, "./build.json"))
    );

    app.post(
      "/create/:tenantId",
      eh(async (req, res) => {
        const tenantId = parseInt(req.params.tenantId, 10);
        log.info("[create]", tenantId);
        const workspace = req.body as Workspace;
        const docHandle = this.repo.create<Workspace>(workspace);
        await docHandle.whenReady();
        res.json({ documentId: docHandle.documentId });
      })
    );

    app.get(
      "/show/:id",
      sessionAuthentication,
      eh(async (req, res) => {
        log.info("[show]", req.session);
        const documentId = req.params.id as DocumentId;
        log.info("[show]", documentId);
        const docHandle = await this.repo.find<Workspace>(documentId);
        const doc = docHandle.doc();
        if (doc) {
          res.json(automerge.toJS(doc));
        } else {
          res.status(404).json({ message: "Document not found" });
        }
      })
    );

    app.use("/answer", answerRoutes);

    app.use("/document", documentRoutes);

    app.put(
      "/upsert/:documentId/:position",
      sessionAuthentication,
      eh(async (req, res) => {
        const documentId = req.params.documentId as DocumentId;
        const position = req.params.position;
        const body = req.body.value;

        const docHandle =
          await this.repo.find<FlowExecutionDocument>(documentId);

        docHandle.change(doc => {
          upsertDocAtPosition(doc, position, body);
        });

        const step = req.body.value as ExecutionStep;
        log.info("[document/step PUT]", documentId, step);
        res.json({
          documentId: docHandle.documentId,
          message: "Upsert processed successfully"
        });
      })
    );

    app.put(
      "/replace/:documentId",
      sessionAuthentication,
      eh(async (req, res) => {
        const documentId = req.params.documentId as DocumentId;
        const path = req.body.path;
        const body = req.body.value as Workspace;

        const docHandle = await this.repo.find<Workspace>(documentId);

        docHandle.change(doc => {
          if (!path) {
            Object.assign(doc, body);
          } else {
            _.set(doc, path, body);
          }
        });

        log.info("[document/replace PUT]", documentId);
        res.json({
          documentId: docHandle.documentId,
          message: "Document replaced successfully"
        });
      })
    );

    this.server = app.listen(PORT, () => {
      log.info(`Listening on port ${PORT}`);
    });

    this.server.on("upgrade", (request, socket, head) => {
      log.info("[on:upgrade]");
      try {
        let user = getUser(request.headers.cookie);

        if (!user) {
          const auth = request.headers.authorization;
          if (!auth?.startsWith("Bearer ")) {
            throw new Error("Missing or invalid Authorization header");
          }

          const token = auth.replace(/^Bearer\s+/, "");
          user = jwt.verify(token, jwtPublicKey) as User;
        }

        (request as AuthenticatedRequest).user = user;

        this.socket.handleUpgrade(request, socket, head, ws => {
          log.info("[handleUpgrade]");
          this.socket.emit("connection", ws, request);
        });
      } catch (e) {
        log.error("[on:upgrade] error", e);
        socket.write("HTTP/1.1 401 Unauthorized\r\n\r\n");
        socket.destroy();
      }
    });
  }
}
