import {
  Question,
  QuestionTypeOptions,
  QuestionTypes
} from "@src/types/Question";
import {
  DateQuestionValue,
  JSONQuestionProperties
} from "@src/types/QuestionProperties.ts";
import { QuestionAnswers } from "@src/types/collection/CollectionForm";

const JsonSchemaTypesMap: { [key in QuestionTypes]?: string } = {
  [QuestionTypes.TEXT]: "string",
  [QuestionTypes.DATE]: "string",
  [QuestionTypes.NUMBER]: "string",
  [QuestionTypes.BOOLEAN]: "boolean"
  // QuestionTypes.JSON = "object" // TODO: add support for nested json
};

const getJsonDefault = (item: Question) => {
  switch (item.type) {
    case QuestionTypes.BOOLEAN:
      return false;
    case QuestionTypes.NUMBER:
      return 0;
    case QuestionTypes.JSON:
      return {
        ...((item.properties as JSONQuestionProperties)?.items || []).reduce(
          (acc, curr) => {
            acc[curr.identifier] = getJsonDefault(curr);
            return acc;
          },
          {} as Record<string, unknown>
        )
      };
    case QuestionTypes.LIST:
      return [];
    case QuestionTypes.TEXT:
    case QuestionTypes.DATE:
    case QuestionTypes.SELECT:
    default:
      return "";
  }
};

export const getAnswersAsJson = (
  properties: JSONQuestionProperties | undefined,
  answers: QuestionAnswers | undefined
): object => {
  if (!properties?.items) {
    return {};
  }

  const entries = properties.items
    .filter(item => !item.properties?.hidden)
    .map((item, index) => {
      const id = item.id;
      const matchingAnswer = answers?.[id];
      const identifier = item.identifier ?? `identifier_${index}`;

      const value =
        matchingAnswer?.value === ""
          ? ""
          : (matchingAnswer?.value ?? getJsonDefault(item));
      if (item.type === QuestionTypes.NUMBER) {
        return [identifier, value.toString()];
      }

      if (item.type === QuestionTypes.JSON) {
        // Return key-value pair for nested JSON
        return [
          identifier,
          getAnswersAsJson(
            item.properties as JSONQuestionProperties,
            matchingAnswer?.value as QuestionAnswers
          )
        ];
      }

      if (item.type === QuestionTypes.LIST) {
        if (!matchingAnswer?.value) {
          return [identifier, []];
        }

        const listValue = matchingAnswer.value as {
          entities: {
            [key: string]: {
              id: string;
              item: {
                [key: string]: {
                  value: string;
                };
              };
            };
          };
          order: string[];
        };

        const values = listValue.order.map(id => {
          const entity = listValue.entities[id];
          // Get first value from item object
          const firstQuestionId = Object.keys(entity.item)[0];
          return entity.item[firstQuestionId]?.value ?? "";
        });

        return [identifier, values];
      }

      return [identifier, value];
    });

  return Object.fromEntries(entries);
};

export const generateSchema = (question: Question<JSONQuestionProperties>) => {
  const properties: { [key: string]: object } = {};
  const required: string[] = [];
  question.properties?.items.forEach(
    (item: {
      identifier: string;
      type: string;
      properties?: {
        maxLength?: number;
        minLength?: number;
        min?: string | number;
        max?: string | number;
        defaultValue?:
          | string
          | number
          | boolean
          | (string | number)[]
          | DateQuestionValue;
        required?: boolean;
      };
    }) => {
      if (item.type === QuestionTypes.JSON) {
        // Handle nested JSON schemas recursively
        properties[item.identifier] = generateSchema(
          item as Question<JSONQuestionProperties>
        );
      } else {
        properties[item.identifier] = {
          type: JsonSchemaTypesMap[
            item.type as keyof typeof JsonSchemaTypesMap
          ],
          ...(item.type === "date" && { format: "date" }),

          ...(item.properties?.maxLength && {
            maxLength: item.properties.maxLength
          }),
          ...(item.properties?.minLength && {
            minLength: item.properties.minLength
          }),
          ...(item.properties?.min && { minimum: item.properties.min }),
          ...(item.properties?.max && { maximum: item.properties.max }),
          ...(item.properties?.defaultValue && {
            default: item.properties.defaultValue
          }),
          ...(item.properties?.required && {
            required: item.properties.required
          })
        };
        if (item.properties?.required) {
          required.push(item.identifier);
        }
      }
    }
  );
  return {
    type: "object",
    properties: properties,
    ...(required.length > 0 && { required }),
    additionalProperties: false
  };
};

export const isValidQuestionValue = (
  itemType: QuestionTypeOptions,
  itemValue: string | number | boolean,
  required: boolean
): boolean => {
  switch (itemType) {
    case QuestionTypes.BOOLEAN:
    case QuestionTypes.NUMBER:
    case QuestionTypes.TEXT:
      return typeof itemValue === JsonSchemaTypesMap[itemType];
    case QuestionTypes.JSON:
      return typeof itemValue === "object" && itemValue !== null;

    case QuestionTypes.DATE:
      if (itemValue) {
        return !isNaN(Date.parse(itemValue as string));
      } else if (!required) {
        // if no date is selected, item value will be ""
        return true;
      }
  }
  return false;
};
