import { SelectOptionType } from "@oneteam/onetheme";

import { ConfigurationSectionWrapper } from "@helpers/ConfigurationSectionWrapper.ts";
import { mapOverResource } from "@helpers/OrderedMapNoState.ts";
import {
  FlowUtils,
  FormUtils,
  FoundationUtils,
  IntervalUtils,
  SeriesUtils
} from "@helpers/forms/selectOptionsUtils.ts";

import { Dictionary } from "@src/hooks/useDictionary.tsx";
import { ConfigurationFormType } from "@src/types/FormConfiguration.ts";
import { FoundationConfiguration } from "@src/types/FoundationConfiguration.ts";
import { Question } from "@src/types/Question";
import { Interval, SeriesConfig } from "@src/types/Series.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

export class WorkspaceConfigurationHelper {
  constructor(readonly config: WorkspaceDocument) {}

  getFormOrThrow(id: string): ConfigurationFormType {
    const form = this.getForm(id);
    if (form) {
      return form;
    } else {
      throw new Error(`Form with id ${id} not found`);
    }
  }

  getForm(id?: string): ConfigurationFormType | undefined {
    if (!id) {
      console.error("WorkspaceConfigurationHelper.findForm: id is undefined");
      return undefined;
    }
    const form = Object.values(this.config.forms).find(it => it.id === id);
    if (!form) {
      console.error(
        `WorkspaceConfigurationHelper.findForm: form with id ${id} not found`
      );
    }
    return form;
  }

  listFoundationsAsOptions(): SelectOptionType[] {
    return mapOverResource(this.config.foundations, foundation =>
      FoundationUtils.mapToSelectOptionType(foundation)
    );
  }

  listFormsAsOptions(
    foundationConfigurationId?: string,
    useKeyAsValue: boolean = false
  ): SelectOptionType[] {
    return Object.values(this.config.forms ?? {})
      .filter((form: ConfigurationFormType) => form.id !== undefined)
      .filter(form => {
        if (!foundationConfigurationId) {
          return true;
        }
        return form.foundationId === foundationConfigurationId;
      })
      .map(form => FormUtils.mapToSelectOptionType(form, useKeyAsValue));
  }

  listFormQuestionsAsOptions(
    formConfigurationId?: string,
    d: Dictionary = (value => value) as Dictionary
  ): SelectOptionType[] {
    if (!formConfigurationId) {
      return [];
    }

    const form = this.getForm(formConfigurationId);
    if (!form) {
      return [];
    }
    return new ConfigurationSectionWrapper(form.content)
      .getQuestions(true)
      .map((question: Question) => {
        const questionType = d
          ? d(`ui.configuration.forms.question.type.${question.type}.label`)
          : question.type;
        return {
          label: question.text,
          value: question.id,
          description: `${question.identifier} - ${typeof questionType === "string" ? questionType : question.type}`
        };
      });
  }

  getFoundationConfiguration(id: string): FoundationConfiguration {
    return this.config.foundations.entities[id];
  }

  getPrevFoundationConfiguration(id: string) {
    const foundations = this.config.foundations.order.map(
      it => this.config.foundations.entities[it]
    );

    const selectedIndex = this.config.foundations.order.indexOf(id);
    if (selectedIndex > 0) {
      return foundations[selectedIndex - 1] ?? undefined;
    }
    return undefined;
  }

  // for later - use ordered map
  getNextFoundationConfiguration(id: string) {
    const foundations = this.config.foundations.order.map(
      it => this.config.foundations.entities[it]
    );

    const selectedIndex = this.config.foundations.order.indexOf(id);
    if (selectedIndex < foundations.length - 1) {
      return foundations[selectedIndex + 1] ?? undefined;
    }
    return undefined;
  }

  findSeriesConfig(id: string): SeriesConfig | undefined {
    const series = this.config.series[id];
    if (!series) {
      console.error(
        `WorkspaceConfigurationHelper.findSeriesConfig: series with id ${id} not found`
      );
    }
    return series;
  }

  listSeriesAsOptions(): SelectOptionType[] {
    return Object.values(this.config.series ?? {}).map(
      SeriesUtils.mapToSelectOptionType
    );
  }

  listFormIntervalsAsOptions(formConfigurationId?: string): SelectOptionType[] {
    if (!formConfigurationId) {
      return [];
    }

    const form = this.getForm(formConfigurationId);
    return this.listSeriesIntervalsAsOptions(form?.seriesId);
  }

  listSeriesIntervalsAsOptions(
    seriesConfigurationId?: string
  ): SelectOptionType[] {
    if (!seriesConfigurationId) {
      return [];
    }

    const series = this.findSeriesConfig(seriesConfigurationId);
    const intervals = series?.intervals;
    if (series && intervals !== undefined) {
      return mapOverResource(intervals, IntervalUtils.mapToSelectOptionType);
    }
    return [] as SelectOptionType[];
  }

  getIntervalById(intervalId: string): Interval | undefined {
    // iterate over all series and find the interval
    for (const seriesId in this.config.series ?? {}) {
      const series = this.config.series[seriesId];
      if (series.intervals?.entities[intervalId]) {
        return series.intervals.entities[intervalId];
      }
    }
    return undefined;
  }

  getSeriesByIntervalId(intervalId: string): SeriesConfig | undefined {
    for (const seriesId in this.config.series ?? {}) {
      const series = this.config.series[seriesId];
      if (series.intervals?.entities[intervalId]) {
        return series;
      }
    }
    return undefined;
  }

  isLastFoundationLevel(id: string) {
    return this.getNextFoundationConfiguration(id) === undefined;
  }

  listFlowsAsOptions(): SelectOptionType[] {
    return mapOverResource(this.config.flows, FlowUtils.mapToSelectOptionType);
  }
}
