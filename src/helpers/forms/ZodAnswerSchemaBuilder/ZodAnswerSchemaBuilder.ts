import { Zod<PERSON><PERSON>ber, ZodType, z } from "zod";

import { ConfigurationSectionWrapper } from "@helpers/ConfigurationSectionWrapper.ts";
import { getPercentageValue } from "@helpers/forms/numberHelpers.ts";

import { Dictionary } from "@src/hooks/useDictionary.tsx";
import { Section } from "@src/types/FormConfiguration.ts";
import { Question, QuestionTypes } from "@src/types/Question.ts";
import {
  BooleanQuestionProperties,
  DateQuestionProperties,
  FilesQuestionProperties,
  JSONQuestionProperties,
  ListQuestionProperties,
  NumberQuestionProperties,
  SelectQuestionProperties,
  TableQuestionProperties,
  TextQuestionProperties
} from "@src/types/QuestionProperties.ts";

type SchemaType = z.ZodObject<SchemaRecord>;
type SchemaRecord = Record<
  string,
  z.ZodType<Partial<Record<string, z.ZodType>>>
>;

export class ZodAnswerSchemaBuilder {
  private readonly content: Section["content"];
  private readonly validateRequiredFields: boolean;
  private readonly schema: SchemaType;
  private readonly structureCallback: (schema: ZodType) => ZodType;
  private readonly d: Dictionary;

  constructor(
    content: Section["content"],
    d: Dictionary,
    structureCallback?: (schema: z.ZodType) => z.ZodType,
    validateRequiredFields?: boolean
  ) {
    this.content = content;
    this.d = d;
    this.validateRequiredFields = validateRequiredFields ?? true;
    this.structureCallback = structureCallback ?? (schema => schema);
    this.schema = this.buildSchema();
  }

  get generatedSchema(): SchemaType {
    return this.schema;
  }

  /**
   * Generates a Zod schema based on the provided content.
   *
   * This method flattens the content using the WorkspaceDocumentWrapper
   * and puts the answer schema from generateZodSchema into a Zod object.
   *
   * @return A Zod schema for the answer of a given question.
   *
   * Example input:
   * ```json
   * {
   *   properties: {
   *     required: true,
   *     minLength: 2,
   *     maxLength: 100
   *   },
   *   ... // other question properties
   * },
   * ```
   *
   * Example output:
   * ```json
   * {
   *     "_def": {
   *         "checks": [
   *             { "kind": "trim" },
   *             { "kind": "min", "value": 2 },
   *             { "kind": "max", "value": 100 }
   *         ],
   *         "typeName": "ZodString",
   *     }
   * }
   * ```
   */
  buildSchema(): SchemaType {
    const workspaceDocumentWrapper = new ConfigurationSectionWrapper(
      this.content
    );
    const questions = workspaceDocumentWrapper
      .getQuestions()
      .filter(q => !q.properties?.disabled && !q.properties?.hidden);
    return z.object(
      this.buildQuestionMapSchema(questions, this.structureCallback)
    );
  }

  /**
   * Builds a schema record for the given questions.
   *
   * This method iterates over the questions and generates a Zod schema for each question.
   * The schema is then added to a record/map with the question id as the key.
   * The structureCallback is used to modify the schema before adding it to the record.
   * The structureCallback is useful when we want to add additional properties to the schema.
   * For example, we can add a "value" property to the schema for JSON and Table questions.
   * Outside this function, we can easily structure the schema in any way we want.
   * i.e. We can place the return value in any object, array or Record in the schema
   * See tableSchema in generateZodSchema for an example.
   *
   * @param questions
   * @param structureCallback
   * @private
   * @return A record/map containing the question id as the key and the Zod schema as the value.
   */
  private buildQuestionMapSchema(
    questions: Question[],
    structureCallback?: (schema: z.ZodType) => z.ZodType
  ): SchemaRecord {
    return questions.reduce(
      (acc, question) => {
        const schema = this.generateZodSchema(question);
        acc[question.id] = structureCallback?.(schema) ?? schema;
        return acc;
      },
      {} as Record<string, z.ZodType>
    );
  }
  /**
   * Generates a Zod schema based on the provided question.
   *
   * This method inspects the type of the question and generates a corresponding
   * Zod schema for validation. Currently, it supports text questions and can be
   * extended to support other question types.
   *
   * @param question The question object containing the type and properties for validation.
   * @return A Zod schema for the answer of given question.
   */
  generateZodSchema(question: Question): z.ZodType {
    switch (question.type) {
      case QuestionTypes.TEXT: {
        const textQuestion = question as Question<TextQuestionProperties>;
        const properties = textQuestion?.properties;
        let schema = z.string().trim();

        if (properties?.minLength || properties?.minLength === 0) {
          schema = schema.min(properties.minLength);
        }
        if (properties?.maxLength || properties?.maxLength === 0) {
          schema = schema.max(properties.maxLength);
        }
        if (properties?.regex !== undefined) {
          schema = schema.regex(properties.regex);
        }
        if (properties?.required && this.validateRequiredFields) {
          if (properties?.minLength === undefined) {
            return schema.min(1); // required field with no min length allows "" empty string in zod
          }
          return schema;
        }

        /**
         * z.literal("") is needed in case we want an optional field (!required)
         * with min and max length constraints
         */
        return schema.optional().or(z.literal(""));
      }
      case QuestionTypes.NUMBER: {
        const numberQuestion = question as Question<NumberQuestionProperties>;
        const properties = numberQuestion?.properties;
        if (!properties) {
          return z.any();
        }
        let schema: z.ZodNumber = z.coerce.number();

        if (properties?.min || properties?.min === 0) {
          schema = schema.min(
            properties.min,
            this.d("errors.common.greaterThanOrEqual", {
              name: this.d("ui.configuration.forms.question.type.number.label"),
              value: getPercentageValue(
                properties.type,
                properties.min,
                properties.decimalPlaces
              )
            })
          );
        }
        if (properties?.max || properties?.max === 0) {
          schema = schema.max(
            properties.max,
            this.d("errors.common.lessThanOrEqual", {
              name: this.d("ui.configuration.forms.question.type.number.label"),
              value: getPercentageValue(
                properties.type,
                properties.max,
                properties.decimalPlaces
              )
            })
          );
        }
        const schemaAfterRefine: z.ZodEffects<ZodNumber> = schema.refine(
          value => {
            if (!properties?.decimalPlaces && properties?.decimalPlaces !== 0) {
              return true;
            }
            const decimalPlaces = Math.pow(10, properties.decimalPlaces);
            const roundedValue =
              Math.round(value * decimalPlaces) / decimalPlaces;
            return Math.abs(value - roundedValue) < Number.EPSILON;
          }
          // add message from dictionary if needed
        );

        if (properties?.required && this.validateRequiredFields) {
          return schemaAfterRefine.or(z.literal(""));
        }

        return schemaAfterRefine.optional().or(z.literal(""));
      }
      case QuestionTypes.SELECT: {
        const selectQuestion = question as Question<SelectQuestionProperties>;
        const properties = selectQuestion?.properties;
        if (!properties) {
          return z.any();
        }

        if (!properties.options || properties.options.length === 0) {
          // fail-safe if options are empty
          return z.any();
        }

        if (properties.isMultiSelect) {
          const schema = z.array(z.string().or(z.number()));
          const options = properties.options.map(option => option.value);
          const schemaAfterRefine = schema.refine(values =>
            values.every(value => options.includes(value))
          );
          if (properties.required && this.validateRequiredFields) {
            return schemaAfterRefine;
          }
          return schemaAfterRefine.optional();
        } else {
          const schema = z.string().or(z.number());
          const options = properties.options.map(option => option.value);
          const schemaAfterRefine = schema.refine(
            value => !value || options.includes(value)
          );
          if (properties.required && this.validateRequiredFields) {
            return schemaAfterRefine;
          }
          return schemaAfterRefine.optional();
        }
      }
      case QuestionTypes.DATE: {
        const dateQuestion = question as Question<DateQuestionProperties>;
        const properties = dateQuestion?.properties;
        if (!properties) {
          return z.any();
        }
        const schemaAfterRefine = z.string().refine(value => {
          if (!value) {
            return true;
          }
          const dateValue = new Date(value);
          let result = true;
          if (properties.min) {
            const minDate = new Date(properties.min);
            result &&= dateValue >= minDate;
          }
          if (properties.max) {
            const maxDate = new Date(properties.max);
            result &&= dateValue <= maxDate;
          }
          return result;
        });
        if (properties.required && this.validateRequiredFields) {
          return schemaAfterRefine;
        }
        return schemaAfterRefine.optional();
      }
      case QuestionTypes.BOOLEAN: {
        const booleanQuestion = question as Question<BooleanQuestionProperties>;
        const properties = booleanQuestion?.properties;
        if (!properties) {
          return z.any();
        }
        if (properties.required && this.validateRequiredFields) {
          return z.boolean();
        }
        return z.boolean().optional();
      }
      case QuestionTypes.FILES: {
        const filesQuestion = question as Question<FilesQuestionProperties>;
        const properties = filesQuestion?.properties;
        if (!properties) {
          return z.any();
        }
        return z.any(); // TODO: to be implemented
      }
      case QuestionTypes.JSON: {
        const jsonQuestion = question as Question<JSONQuestionProperties>;
        const properties = jsonQuestion?.properties;
        if (!properties) {
          return z.any();
        }
        // call recursively for each item in items and use buildSchema
        const jsonSchema = z.object(
          this.buildQuestionMapSchema(
            properties.items?.filter(item => !item.properties?.hidden),
            schema => {
              return z.object({
                value: schema
              });
            }
          )
        );
        return jsonSchema;
      }
      case QuestionTypes.TABLE: {
        const tableQuestion = question as Question<TableQuestionProperties>;
        const properties = tableQuestion?.properties;
        if (!properties) {
          return z.any();
        }

        // call recursively for each item in columns and use buildQuestionMapSchema
        const tableSchema = z.object({
          order: z.array(z.string()),
          entities: z.record(
            z.string(),
            z.object({
              columns: z.object(
                this.buildQuestionMapSchema(properties.columns, schema => {
                  return z
                    .object({
                      value: schema
                    })
                    .optional();
                })
              )
            })
          )
        });
        // use for logging: JSON.stringify(zodToJsonSchema(tableSchema), null, 2)
        return tableSchema;
      }
      case QuestionTypes.LIST: {
        const listQuestion = question as Question<ListQuestionProperties>;
        const properties = listQuestion?.properties;
        if (!properties) {
          return z.any();
        }

        const orderSchema = z
          .array(z.string())
          .refine(
            arr => !(properties.minLength && arr.length < properties.minLength),
            {
              message: this.d(
                "errors.configurationForm.question.list.minLength",
                { min: properties.minLength }
              )
            }
          )
          .refine(
            arr => !(properties.maxLength && arr.length > properties.maxLength),
            {
              message: this.d(
                "errors.configurationForm.question.list.maxLength",
                { max: properties.maxLength }
              )
            }
          );

        // call recursively for each item and use buildQuestionMapSchema
        const listSchema = z.object({
          order: orderSchema,
          entities: z.record(
            z.string(),
            z.object({
              item: z.object(
                this.buildQuestionMapSchema(properties.items, schema => {
                  return z
                    .object({
                      value: schema
                    })
                    .optional();
                })
              )
            })
          )
        });
        return listSchema;
      }
      default:
        return z.any();
    }
  }
}
