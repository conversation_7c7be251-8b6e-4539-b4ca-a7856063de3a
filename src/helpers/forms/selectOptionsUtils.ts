import { SelectOptionType } from "@oneteam/onetheme";

import { FlowConfiguration } from "@src/types/FlowConfiguration/FlowConfiguration";
import { ConfigurationFormType } from "@src/types/FormConfiguration.ts";
import { FoundationConfiguration } from "@src/types/FoundationConfiguration.ts";
import { Question } from "@src/types/Question.ts";
import { Interval, SeriesConfig } from "@src/types/Series.ts";

export class FlowUtils {
  static readonly mapToSelectOptionType = (
    flow: FlowConfiguration
  ): SelectOptionType => ({
    label: flow.name,
    value: flow.id,
    description: flow.description
  });
}

export class FormUtils {
  static readonly mapToSelectOptionType = (
    form: ConfigurationFormType,
    useKeyAsValue: boolean
  ): SelectOptionType => ({
    value: !useKeyAsValue ? form.id : form.key,
    label: form.name,
    description: form.description
  });
}

export class FormQuestionUtils {
  static readonly mapToSelectOptionType = (
    question: Question
  ): SelectOptionType => ({
    label: question.text,
    value: question.id
  });
}

export class SeriesUtils {
  static readonly mapToSelectOptionType = (
    series: SeriesConfig
  ): SelectOptionType => ({
    label: series.name,
    value: series.id,
    description: series.description
  });
}

export class IntervalUtils {
  static readonly mapToSelectOptionType = (
    interval: Interval
  ): SelectOptionType => ({
    label: interval.name,
    value: interval.id
  });
}

export class FoundationUtils {
  static readonly mapToSelectOptionType = (
    foundation: FoundationConfiguration
  ): SelectOptionType => ({
    label: foundation.name,
    value: foundation.id,
    description: foundation.description ? foundation.description : undefined
  });
}
