import { describe, expect, it, test } from "vitest";

import {
  FlowConfiguration,
  FlowConfigurationStatusType
} from "../../../src/types/FlowConfiguration/FlowConfiguration";
import {
  getOrderedStepIds,
  getStepIdPathToStepId,
  getStepIdToParentStepId,
  populateRealValuesUsingMockFlowContext
} from "./flowHelpers";

const testFlowConfiguration: FlowConfiguration = {
  start: "step1",
  triggers: {
    trigger1: {
      id: "trigger1",
      name: "trigger",
      variant: "trigger",
      properties: {},
      metadata: {
        id: "id",
        name: "name"
      }
    }
  },
  steps: {
    step1: {
      id: "step1",
      name: "step",
      variant: "action",
      properties: {},
      next: "step2"
    },
    step2: {
      id: "step2",
      name: "step",
      variant: "condition",
      properties: {
        branches: [
          {
            condition: {},
            next: "step2-branch1-step1"
          }
        ]
      },
      next: "step2-branch2-step1"
    },
    step4: {
      id: "step4",
      name: "step",
      variant: "action",
      properties: {},
      next: null
    },
    "step2-branch1-step1": {
      id: "step2-branch1-step1",
      name: "step",
      variant: "action",
      properties: {},
      next: "step2-branch1-step2"
    },
    "step2-branch1-step2": {
      id: "step2-branch1-step2",
      name: "step",
      variant: "condition",
      properties: {
        branches: [
          {
            condition: {},
            next: "step2-branch1-step2-branch1-step1"
          }
        ]
      },
      next: null
    },
    "step2-branch2-step1": {
      id: "step2-branch2-step1",
      name: "step",
      variant: "action",
      properties: {},
      next: null
    },
    "step2-branch1-step2-branch1-step1": {
      id: "step2-branch1-step2-branch1-step1",
      name: "step",
      variant: "action",
      properties: {},
      next: "step2-branch1-step2-branch1-step2"
    },
    "step2-branch1-step2-branch1-step2": {
      id: "step2-branch1-step2-branch1-step2",
      name: "step",
      variant: "condition",
      properties: {
        branches: [
          {
            condition: {},
            next: null
          }
        ]
      },
      next: null
    }
  }
};

const testIteratorFlowConfiguration: FlowConfiguration = {
  description: "",
  endingVariables: [],
  id: "agiONPNB24qC1tfTSlUQo",
  labels: [],
  metadata: {
    createdAt: "2025-05-15T04:10:23.665Z",
    updatedAt: "2025-05-15T04:10:23.665Z"
  },
  name: "flow1",
  start: "initialGDPStepId",
  startingVariables: [
    {
      identifier: "foundation",
      properties: {
        required: true
      },
      type: "foundation.arWyEI3FvkcUtcSVepQiF"
    }
  ],
  status: "active" as FlowConfigurationStatusType,
  steps: {
    iteratorStepId: {
      id: "iteratorStepId",
      metadata: {
        createdAt: "2025-05-15T04:17:06.589Z",
        updatedAt: "2025-05-15T04:17:06.589Z"
      },
      name: "Filter",
      next: "resultStringStepId",
      properties: {
        configuration: {
          start: "populationStepId",
          steps: {
            populationStepId: {
              id: "populationStepId",
              metadata: {
                createdAt: "2025-05-15T04:57:45.257Z",
                updatedAt: "2025-05-15T04:57:45.257Z"
              },
              name: "Population",
              next: "perCapitaStepId",
              properties: {
                variables: [
                  {
                    identifier: "population",
                    type: "number",
                    value: ""
                  }
                ]
              },
              variant: "setVariables"
            },
            perCapitaStepId: {
              id: "perCapitaStepId",
              metadata: {
                createdAt: "2025-05-15T05:06:24.227Z",
                updatedAt: "2025-05-15T05:06:24.227Z"
              },
              name: "GDP Per Capita",
              next: "",
              properties: {
                variables: [
                  {
                    identifier: "gdp_per_capita",
                    type: "text",
                    value: ""
                  }
                ]
              },
              variant: "setVariables"
            }
          }
        },
        inputs: {
          itemValueType: "json",
          itemVariableName: "item_iteratorStepId",
          resultVariableName: "filtered__step_iteratorStepId"
        },
        typePrimaryIdentifier: "iteratorFilter"
      },
      variant: "iterator"
    },
    resultStringStepId: {
      id: "resultStringStepId",
      metadata: {
        createdAt: "2025-05-15T05:06:45.154Z",
        updatedAt: "2025-05-15T05:06:45.154Z"
      },
      name: "Get Result String",
      next: "",
      properties: {
        variables: [
          {
            identifier: "result_string",
            type: "text",
            value: ""
          }
        ]
      },
      variant: "setVariables"
    },
    initialGDPStepId: {
      id: "initialGDPStepId",
      metadata: {
        createdAt: "2025-05-15T04:56:09.237Z",
        updatedAt: "2025-05-15T04:56:09.237Z"
      },
      name: "Initial GDP",
      next: "iteratorStepId",
      properties: {
        variables: [
          {
            identifier: "gdp",
            type: "number",
            value: "1000"
          }
        ]
      },
      variant: "setVariables"
    }
  },
  triggers: {
    triggerStepId: {
      id: "triggerStepId",
      metadata: {
        createdAt: "2025-05-15T04:10:27.695Z",
        updatedAt: "2025-05-15T04:10:27.695Z"
      },
      name: "Manually trigger from a foundation",
      next: "",
      properties: {
        inputs: {
          buttonLabel: "Run flow1",
          foundationConfigurationId: "arWyEI3FvkcUtcSVepQiF",
          foundationVariableName: "foundation"
        },
        typePrimaryIdentifier: "manualTriggerFromFoundation"
      },
      variant: "trigger"
    }
  }
};

const flowConfigurationWithActions: FlowConfiguration = {
  description: "flow1",
  endingVariables: [],
  id: "acJsqRt6ilrrouGJqKdn6",
  labels: [],
  metadata: {
    createdAt: "2025-05-23T01:21:51.995Z",
    updatedAt: "2025-05-23T01:21:51.995Z"
  },
  name: "flow1",
  start: "variable1StepId",
  startingVariables: [
    {
      identifier: "foundation",
      properties: {
        required: true
      },
      type: "foundation.aJYoNprUEiRNqEfsEnGxq"
    }
  ],
  steps: {
    forEachStepId: {
      id: "forEachStepId",
      metadata: {
        createdAt: "2025-05-23T01:22:26.350Z",
        updatedAt: "2025-05-23T01:22:26.350Z"
      },
      name: "For each",
      next: "checkOutputVariableStepId",
      properties: {
        configuration: {
          labels: [],
          start: "innerSelectFormStepId",
          steps: {
            innerSelectFormStepId: {
              id: "innerSelectFormStepId",
              metadata: {
                createdAt: "2025-05-23T02:17:43.908Z",
                updatedAt: "2025-05-23T02:17:43.908Z"
              },
              name: "Select a form",
              next: "innerSetVariableStepId",
              properties: {
                inputs: {
                  continueFlowIfNotFound: "true",
                  formConfigurationId: "aLa5Aq7YI0FuUW4YnjLqx",
                  formVariableName: "inner_iterator_form",
                  foundationId: "{{foundation.id}}",
                  groupIdentifier: "",
                  questionId: ""
                },
                typePrimaryIdentifier: "selectForm"
              },
              variant: "action"
            },
            innerSetVariableStepId: {
              id: "innerSetVariableStepId",
              metadata: {
                createdAt: "2025-05-23T01:53:18.436Z",
                updatedAt: "2025-05-23T01:53:18.436Z"
              },
              name: "Set variable(s)",
              next: "",
              properties: {
                variables: [
                  {
                    identifier: "variable_3",
                    type: "text",
                    value: ""
                  }
                ]
              },
              variant: "setVariables"
            }
          }
        },
        inputs: {
          isReturn: "false",
          itemValueType: "json",
          itemVariableName: "employee",
          list: "{{form.aSkz266gwp.answer}}",
          resultVariableName: "it_output",
          resultVariableType: "number",
          transformedValueType: "number"
        },
        typePrimaryIdentifier: "iteratorForEach"
      },
      variant: "iterator"
    },
    outerFormStepId: {
      id: "outerFormStepId",
      metadata: {
        createdAt: "2025-05-23T01:22:19.234Z",
        updatedAt: "2025-05-23T01:22:19.234Z"
      },
      name: "Select a form",
      next: "forEachStepId",
      properties: {
        inputs: {
          continueFlowIfNotFound: "true",
          formConfigurationId: "aLa5Aq7YI0FuUW4YnjLqx",
          formVariableName: "form",
          foundationId: "{{foundation.id}}"
        },
        typePrimaryIdentifier: "selectForm"
      },
      variant: "action"
    },
    checkOutputVariableStepId: {
      id: "checkOutputVariableStepId",
      metadata: {
        createdAt: "2025-05-23T01:53:15.207Z",
        updatedAt: "2025-05-23T01:53:15.207Z"
      },
      name: "Check Output Variable",
      next: "",
      properties: {
        variables: [
          {
            identifier: "variable_2",
            type: "text",
            value: ""
          }
        ]
      },
      variant: "setVariables"
    },
    variable1StepId: {
      id: "variable1StepId",
      metadata: {
        createdAt: "2025-05-23T01:22:05.730Z",
        updatedAt: "2025-05-23T01:22:05.730Z"
      },
      name: "Set variable(s)",
      next: "outerFormStepId",
      properties: {
        variables: [
          {
            identifier: "variable_1",
            type: "text",
            value: ""
          }
        ]
      },
      variant: "setVariables"
    }
  },
  triggers: {
    triggerStepId: {
      id: "triggerStepId",
      metadata: {
        createdAt: "2025-05-23T01:21:53.794Z",
        updatedAt: "2025-05-23T01:21:53.794Z"
      },
      name: "Manually trigger from a foundation",
      next: "",
      properties: {
        inputs: {
          buttonLabel: "Global",
          foundationConfigurationId: "aJYoNprUEiRNqEfsEnGxq",
          foundationVariableName: "foundation"
        },
        typePrimaryIdentifier: "manualTriggerFromFoundation"
      },
      variant: "trigger"
    }
  }
};
describe("Flow helpers", () => {
  it("getOrderedSteps", () => {
    const result = getOrderedStepIds({
      configurationFlow: testFlowConfiguration
    });
    expect(result).toEqual([
      "trigger1",
      "step1",
      "step2",
      "step2-branch2-step1",
      "step2-branch1-step1",
      "step2-branch1-step2",
      "step2-branch1-step2-branch1-step1",
      "step2-branch1-step2-branch1-step2"
    ]);
  });

  describe("populateRealValuesUsingMockFlowContext - parameterized", () => {
    const cases = [
      {
        name: "should replace single curly braces with values from flow context",
        input: "{{foundation.id}}",
        context: { foundation: { id: "testFoundationId" } },
        expected: "testFoundationId"
      },
      {
        name: "should return empty string if value is undefined and replaceWithEmptyStringIfUndefined is true",
        input: "{{undefinedValue}}",
        context: {},
        expected: "",
        replaceWithEmptyStringIfUndefined: true
      },
      {
        name: "should return the original string if no placeholders are found",
        input: "No placeholders here",
        context: {},
        expected: "No placeholders here"
      },
      {
        name: "should handle nested objects and arrays",
        input: "{{foundation.details.name}}",
        context: { foundation: { details: { name: "Test Foundation" } } },
        expected: "Test Foundation"
      },
      {
        name: "should handle objects",
        input: "{{foundation}}",
        context: {
          foundation: { id: "testFoundationId", name: "Test Foundation" }
        },
        expected: { id: "testFoundationId", name: "Test Foundation" }
      },
      {
        name: "should handle array operations",
        input: "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}",
        context: {
          thisStep: {
            answers: [
              { questionTypeWithOperation: "text" },
              { questionTypeWithOperation: "number" }
            ]
          },
          thisIndex: 0
        },
        expected: "text"
      },
      {
        name: "should handle array operations with missing index",
        input: "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}",
        context: {
          thisStep: {
            answers: [
              { questionTypeWithOperation: "text" },
              { questionTypeWithOperation: "number" }
            ]
          }
        },
        expected:
          "{{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}"
      },
      {
        name: "should handle array operations in string",
        input:
          "abc {{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}",
        context: {
          thisStep: {
            answers: [
              { questionTypeWithOperation: "text" },
              { questionTypeWithOperation: "number" }
            ]
          },
          thisIndex: 0
        },
        expected: "abc text"
      },
      {
        name: "should handle array operations in string with missing index",
        input:
          "abc {{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}",
        context: {
          thisStep: {
            answers: [
              { questionTypeWithOperation: "text" },
              { questionTypeWithOperation: "number" }
            ]
          }
        },
        expected:
          "abc {{thisStep.answers[{{thisIndex}}].questionTypeWithOperation}}"
      },
      {
        name: "should handle array of objects with placeholders",
        input: [{ label: "{{user.name}}", value: "{{user.id}}" }],
        context: { user: { name: "Bob", id: 7 } },
        expected: [{ label: "Bob", value: 7 }]
      },
      {
        name: "should handle multiple placeholders in a string",
        input: "Foundation ID: {{foundation.id}}, Name: {{foundation.name}}",
        context: {
          foundation: { id: "testFoundationId", name: "Test Foundation" }
        },
        expected: "Foundation ID: testFoundationId, Name: Test Foundation"
      },
      {
        name: "should replace placeholders in between other text",
        input: "form.{{thisStep.formConfigurationId}}",
        context: {
          thisStep: { formConfigurationId: "aLa5Aq7YI0FuUW4YnjLqx" }
        },
        expected: "form.aLa5Aq7YI0FuUW4YnjLqx"
      },
      {
        name: "should replace undefined placeholders in between other text",
        input: "form.{{thisStep.formConfigurationId}}",
        context: { thisStep: {} },
        expected: "form.",
        replaceWithEmptyStringIfUndefined: true
      },
      {
        name: "should handle nested form object, () => string",
        input: {
          type: "form.{{thisStep.formConfigurationId}}",
          identifier: "{{thisStep.formVariableName}}",
          value: "{{event.eventProperties.form.id}}",
          properties: null
        },
        context: {
          thisStep: {
            formConfigurationId: "aLa5Aq7YI0FuUW4YnjLqx",
            formVariableName: "testFormVariable"
          },
          event: {
            eventProperties: {
              form: {
                id: "testFormId"
              }
            }
          }
        },
        expected: {
          type: "form.aLa5Aq7YI0FuUW4YnjLqx",
          identifier: "testFormVariable",
          value: "testFormId",
          properties: null
        }
      },
      {
        name: "should handle nested form object if undefined, () => string",
        input: {
          type: "form.{{thisStep.formConfigurationId}}",
          identifier: "{{thisStep.formVariableName}}",
          value: "{{event.eventProperties.form.id}}",
          properties: null
        },
        context: {
          thisStep: {
            formVariableName: "testFormVariable"
          },
          event: {
            eventProperties: {}
          }
        },
        expected: {
          type: "form.{{thisStep.formConfigurationId}}",
          identifier: "testFormVariable",
          value: "{{event.eventProperties.form.id}}",
          properties: null
        }
      },
      {
        name: "should handle deeply nested placeholders",
        input: "{{a.b[{{c.d}}].e}}",
        context: {
          a: { b: [{ e: "zero" }, { e: "one" }] },
          c: { d: 1 }
        },
        expected: "one"
      },
      {
        name: "should handle array of strings with placeholders",
        input: ["Hello {{user.name}}", "ID: {{user.id}}"],
        context: {
          user: { name: "Alice", id: 42 }
        },
        expected: ["Hello Alice", "ID: 42"]
      }
    ];

    cases.forEach(
      ({
        name,
        input,
        context,
        expected,
        replaceWithEmptyStringIfUndefined
      }) => {
        it(name, () => {
          const result = populateRealValuesUsingMockFlowContext(
            input,
            context,
            replaceWithEmptyStringIfUndefined
          );
          expect(result).toEqual(expected);
        });
      }
    );

    describe("populateRealValuesUsingMockFlowContext - infinite loop protection", () => {
      it("should throw an error for self-referential placeholders (infinite loop) in string", () => {
        const input = "test {{a}}";
        const context = { a: "{{b}}", b: "{{a}}" };
        expect(() =>
          populateRealValuesUsingMockFlowContext(input, context)
        ).toThrow(/infinite placeholder resolution loop/i);
      });

      it("should throw an error for self-referential placeholders (infinite loop)", () => {
        const input = "{{{{a}}}}";
        const context = { a: "{{b}}", b: "{{a}}" };
        expect(() =>
          populateRealValuesUsingMockFlowContext(input, context)
        ).toThrow(/infinite placeholder resolution loop/i);
      });
    });
  });

  describe("Calculation helpers", () => {
    test("getStepIdToParentStepId works for iterators", () => {
      const result = getStepIdToParentStepId({
        configurationFlow: testIteratorFlowConfiguration
      });
      expect(result).toEqual({
        perCapitaStepId: "populationStepId",
        resultStringStepId: "iteratorStepId",
        populationStepId: "iteratorStepId",
        iteratorStepId: "initialGDPStepId"
      });
    });

    test("getStepIdToParentStepId works with action steps", () => {
      const result = getStepIdToParentStepId({
        configurationFlow: flowConfigurationWithActions
      });
      expect(result).toEqual({
        outerFormStepId: "variable1StepId",
        forEachStepId: "outerFormStepId",
        innerSelectFormStepId: "forEachStepId",
        checkOutputVariableStepId: "forEachStepId",
        innerSetVariableStepId: "innerSelectFormStepId"
      });
    });

    test("getStepIdPathToStepId works with action steps, proper start step", () => {
      const result = getStepIdPathToStepId({
        stepId: "innerSelectFormStepId",
        configurationFlow: flowConfigurationWithActions
      });
      expect(result).toEqual([
        "triggerStepId",
        "variable1StepId",
        "outerFormStepId",
        "forEachStepId"
      ]);
    });

    test("getStepIdPathToStepId works with action steps inside iterator steps", () => {
      const result = getStepIdPathToStepId({
        stepId: "innerSetVariableStepId",
        configurationFlow: flowConfigurationWithActions
      });
      expect(result).toEqual([
        "triggerStepId",
        "variable1StepId",
        "outerFormStepId",
        "forEachStepId",
        "innerSelectFormStepId"
      ]);
    });

    test("getStepIdPathToStepId works for step after iterator", () => {
      const result = getStepIdPathToStepId({
        stepId: "resultStringStepId",
        configurationFlow: testIteratorFlowConfiguration
      });
      expect(result).toEqual([
        "triggerStepId",
        "initialGDPStepId",
        "iteratorStepId"
      ]);
    });

    test("getStepIdPathToStepId works for first step inside iterator", () => {
      const result = getStepIdPathToStepId({
        stepId: "populationStepId",
        configurationFlow: testIteratorFlowConfiguration
      });
      expect(result).toEqual([
        "triggerStepId",
        "initialGDPStepId",
        "iteratorStepId"
      ]);
    });

    test("getStepIdPathToStepId works for second step inside iterator", () => {
      const result = getStepIdPathToStepId({
        stepId: "perCapitaStepId",
        configurationFlow: testIteratorFlowConfiguration
      });
      expect(result).toEqual([
        "triggerStepId",
        "initialGDPStepId",
        "iteratorStepId",
        "populationStepId"
      ]);
    });
  });
});
