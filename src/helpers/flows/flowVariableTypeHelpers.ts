import { Doc } from "@automerge/automerge-repo";

import { ConfigurationSectionWrapper } from "@helpers/ConfigurationSectionWrapper";

import { Dictionary } from "@src/hooks/useDictionary";
import { MockVariable } from "@src/types/FlowConfiguration/FlowConfiguration";
import {
  VariableTypeDefinition,
  VariableTypes
} from "@src/types/FlowConfiguration/Variables";
import { ConfigurationFormType } from "@src/types/FormConfiguration";
import { FoundationConfiguration } from "@src/types/FoundationConfiguration";
import {
  Question,
  QuestionTypes,
  multiLevelQuestionTypes
} from "@src/types/Question";
import {
  JSONQuestionProperties,
  ListQuestionProperties,
  TableQuestionProperties
} from "@src/types/QuestionProperties";
import { SeriesConfig } from "@src/types/Series";
import { WorkspaceDocument } from "@src/types/documentTypes";

import { getVariableTypeDetails } from "./flowVariableHelpers";

export const getVariableTypeDefinition = ({
  variable,
  document,
  d
}: {
  variable: MockVariable;
  document: Doc<WorkspaceDocument>;
  d: Dictionary;
}): VariableTypeDefinition => {
  const { properties } = getVariableTypeDetails({ type: variable.type });

  if (!variable.type) {
    variable.type = "unknown";
  }
  if (variable.type.startsWith(VariableTypes.FOUNDATION)) {
    return getFoundationDefinition({
      to: variable.identifier,
      document,
      foundationConfigurationId: properties?.foundationConfigurationId,
      d
    });
  } else if (variable.type.startsWith(VariableTypes.FORM)) {
    const formConfigurationId = properties?.formConfigurationId;
    return getFormDefinition({
      to: variable.identifier,
      document,
      formConfigurationId,
      d
    });
  } else if (variable.type === "table") {
    return {
      __path: `${variable.identifier}`,
      __identifier: variable.identifier,
      __type: "table",
      __configuration: {
        identifier: variable?.identifier,
        type: "table",
        properties: {
          columns: [
            {
              id: "_rowIndex",
              identifier: "RowIndex",
              text: "Row Index",
              type: "number"
            },
            ...(variable.properties?.columns?.map(column => {
              return {
                ...(column.id ? { id: column.id } : {}),
                identifier: column.identifier,
                type: column.type
              };
            }) ?? [])
          ]
        }
      },
      ...Object.fromEntries(
        // Fix columns
        ((variable.properties?.columns as Question[]) ?? [])?.map(
          (item: Question) => {
            return [
              item.identifier,
              {
                __path: `${variable.identifier}.${item.identifier}`,
                __identifier: item.identifier,
                __description: `${item.type} column`,
                __type: "list",
                __configuration: {
                  type: "list",
                  items: [
                    {
                      type: item.type
                    }
                  ]
                }
              }
            ];
          }
        )
      )
      // TODO: add columns from variable?.properties?.columns
    };
  } else if (variable.type === "list") {
    const listOf = variable.properties?.items?.[0];
    return {
      __path: variable.identifier,
      __identifier: variable.identifier,
      __type: "list",
      __configuration: {
        identifier: variable?.identifier,
        type: "list",
        properties: {
          items: [
            {
              identifier: listOf?.identifier ?? `${variable.identifier}.item`,
              type: listOf?.type ?? "unknown",
              properties: listOf?.properties
            }
          ]
        }
      },
      ...Object.fromEntries(
        (Array.isArray(variable.properties?.items)
          ? (variable.properties.items as Question[])
          : []
        ).map(item => {
          return [
            item.identifier,
            {
              __path: `${variable.identifier}.${item.id ?? item.identifier}`,
              __identifier: item.identifier ?? item.id,
              __type: "list",
              __description: `${item.type} item`,
              __configuration: {
                type: "list",
                items: [
                  {
                    type: item.type,
                    properties: item.properties
                  }
                ]
              }
            }
          ];
        })
      )
    };
  } else if (variable.type === "json") {
    const processJsonVariableItem = (
      item: Question,
      parentPath: string
    ): [string, object] => {
      const itemPath = `${parentPath}.${item.id ?? item.identifier}`;

      if (item.type === "json") {
        // Recursively process nested JSON
        return [
          item.identifier,
          {
            __path: itemPath,
            __identifier: item.identifier ?? item.id,
            __type: "json",
            __configuration: item,
            ...Object.fromEntries(
              (
                ((item.properties as JSONQuestionProperties)
                  ?.items as Question[]) ?? []
              ).map(nestedItem => processJsonVariableItem(nestedItem, itemPath))
            )
          }
        ];
      }

      // Handle non-JSON types
      return [
        item.identifier,
        {
          __path: itemPath,
          __identifier: item.identifier ?? item.id,
          __type: item.type,
          __description: `${item.type} item`,
          __configuration: [
            {
              type: item.type
            }
          ]
        }
      ];
    };

    return {
      __path: `${variable.identifier}`,
      __identifier: variable.identifier,
      __type: "json",
      ...Object.fromEntries(
        (Array.isArray(variable.properties?.items)
          ? (variable.properties.items as Question[])
          : []
        ).map(item => processJsonVariableItem(item, variable.identifier))
      )
    };
  } else if (variable.type.startsWith(VariableTypes.SERIES_INTERVAL)) {
    return getSeriesIntervalDefinition({
      to: variable.identifier,
      identifier: variable.identifier,
      seriesConfigurationId: properties?.seriesConfigurationId,
      document,
      d
    });
  }

  return {
    __path: variable.identifier,
    __identifier: variable.identifier,
    __type: variable.type
  };
};

const getFoundationDefinition = ({
  to,
  document,
  foundationConfigurationId,
  d
}: {
  to: string;
  document: Doc<WorkspaceDocument>;
  foundationConfigurationId?: string;
  d: Dictionary;
}) => {
  let foundationConfiguration;
  let parentFoundationConfiguration;
  let childFoundationConfiguration;

  if (foundationConfigurationId) {
    foundationConfiguration =
      document.foundations.entities[foundationConfigurationId];

    const foundationOrder = document.foundations.order.indexOf(
      foundationConfigurationId
    );

    if (foundationOrder > 0) {
      parentFoundationConfiguration =
        document.foundations.entities[
          document.foundations.order[foundationOrder - 1]
        ];
    }

    if (foundationOrder < document.foundations.order.length - 1) {
      childFoundationConfiguration =
        document.foundations.entities[
          document.foundations.order[foundationOrder + 1]
        ];
    }
  }

  return {
    __path: to,
    __type: "foundation",
    __description: foundationConfiguration
      ? `${foundationConfiguration?.name} ${d("ui.terminology.foundation")}`
      : "",
    id: {
      __path: `${to}.id`,
      __identifier: "id",
      __type: "number"
    },
    key: {
      __path: `${to}.key`,
      __identifier: "key",
      __type: "text"
    },
    name: {
      __path: `${to}.name`,
      __identifier: "name",
      __type: "text"
    },
    properties: {
      __path: `${to}.properties`,
      __type: "json",
      __identifier: "properties",
      __configuration: {
        identifier: "properties",
        type: "json"
      }
      // TODO: foundationConfiguration to contain the list of questions that are the "properties" ? Similarr to formConfiguration
    },
    foundationConfiguration: getFoundationConfigurationDefinition({
      to: `${to}.foundationConfiguration`,
      foundationConfiguration,
      parentFoundationConfiguration,
      childFoundationConfiguration
    }),
    ...(!foundationConfiguration || parentFoundationConfiguration
      ? {
          parentId: {
            __path: `${to}.parentId`,
            __type: "text",
            __description: parentFoundationConfiguration
              ? `${parentFoundationConfiguration?.name} Foundation`
              : "Unknown foundation level (could be undefined)"
          }
        }
      : {})
  };
};

const getFoundationConfigurationDefinition = ({
  to,
  foundationConfiguration,
  parentFoundationConfiguration,
  childFoundationConfiguration
}: {
  to: string;
  foundationConfiguration?: FoundationConfiguration;
  parentFoundationConfiguration?: FoundationConfiguration;
  childFoundationConfiguration?: FoundationConfiguration;
}) => {
  return {
    __path: to,
    __type: "foundationConfiguration",
    __identifier: "foundationLevel",
    __description: foundationConfiguration
      ? `${foundationConfiguration?.name}`
      : "",
    id: {
      __path: `${to}.id`,
      __identifier: "id",
      __type: "text"
    },
    name: {
      __path: `${to}.name`,
      __identifier: "name",
      __type: "text"
    },
    key: {
      __path: `${to}.key`,
      __identifier: "key",
      __type: "text"
    },
    ...(!foundationConfiguration ||
    (foundationConfiguration && parentFoundationConfiguration)
      ? {
          parentId: {
            __path: `${to}.parentId`,
            __identifier: "parentId",
            __type: "text",
            __description: parentFoundationConfiguration
              ? parentFoundationConfiguration?.name
              : "Unknown foundation level (could be undefined)"
          }
        }
      : {}),
    ...(!foundationConfiguration ||
    (foundationConfiguration && childFoundationConfiguration)
      ? {
          childId: {
            __path: `${to}.childId`,
            __identifier: "childId",
            __type: "text",
            __description: childFoundationConfiguration
              ? childFoundationConfiguration?.name
              : "Unknown foundation level (could be undefined)"
          }
        }
      : {})
  };
};
const getFormDefinition = ({
  to,
  document,
  formConfigurationId,
  d
}: {
  to?: string;
  document: Doc<WorkspaceDocument>;
  formConfigurationId?: string;
  d: Dictionary;
}) => {
  let formConfiguration;
  let series;
  let questions: Question[] = [];

  if (formConfigurationId) {
    formConfiguration = document.forms?.[formConfigurationId];
    if (formConfiguration?.content) {
      const formConfigurationWrapper = new ConfigurationSectionWrapper(
        formConfiguration?.content
      );
      questions = formConfigurationWrapper.getQuestions();
    }
    series = formConfiguration?.seriesId
      ? document.series[formConfiguration?.seriesId]
      : undefined;
  }
  return {
    __type: "form",
    __path: to,
    __description: formConfiguration
      ? `${formConfiguration?.name} - ${formConfiguration?.key}`
      : undefined,
    id: {
      __path: `${to}.id`,
      __identifier: "id",
      __type: "number"
    },
    foundation: getFoundationDefinition({
      to: `${to}.foundation`,
      document,
      foundationConfigurationId: formConfiguration?.foundationId,
      d
    }),
    properties: {
      __path: `${to}.properties`,
      __type: "json",
      __identifier: "properties",
      __configuration: {
        identifier: "properties",
        type: "json"
      }
      // TODO: formConfiguration to contain the list of questions that are the "properties"
    },
    // Not for user
    // documentId: {
    //   __type: "text"
    // },
    ...(formConfiguration?.seriesId && series?.name
      ? {
          [series?.name.replaceAll(" ", "")]: getSeriesIntervalDefinition({
            to: `${to}.seriesInterval`,
            identifier: series.name.replaceAll(" ", ""),
            seriesConfigurationId: series?.id,
            document,
            d
          })
        }
      : {}),
    formConfiguration: getFormConfigurationDefinition({
      // TODO: fix type and path
      to: `${to}.formConfiguration`,
      formConfiguration,
      series,
      document,
      d
    }),
    ...Object.fromEntries(
      (questions ?? []).map(q => {
        return [
          q.identifier,
          getQuestionDefinition({
            question: q,
            to: `${to}.${q.id ?? q.identifier}`
          })
        ];
      })
    )
  };
};

const getSeriesIntervalDefinition = ({
  to,
  identifier,
  seriesConfigurationId,
  document,
  d
}: {
  to: string;
  identifier?: string;
  seriesConfigurationId: string | undefined;
  document: Doc<WorkspaceDocument>;
  d: Dictionary;
}) => {
  const series = seriesConfigurationId
    ? document.series[seriesConfigurationId]
    : undefined;
  return {
    __path: `${to}.id`,
    __identifier: identifier,
    __type: "seriesInterval",
    __description: series?.name
      ? `${d("ui.terminology.series")}: ${series?.name}`
      : `${d("ui.terminology.series")} unavailable`,
    id: {
      __path: `${to}.id`,
      __type: "text"
    },
    name: {
      __path: `${to}.name`,
      __type: "text"
    },
    next: {
      __path: `${to}.next.id`,
      __type: "seriesInterval",
      __description: `${series?.name} (next)`,
      id: {
        __path: `${to}.next.id`,
        __type: "text"
      },
      name: {
        __path: `${to}.next.name`,
        __type: "text"
      }
    },
    previous: {
      __path: `${to}.previous.id`,
      __type: "seriesInterval",
      __description: `${series?.name} (previous)`,
      id: {
        __path: `${to}.previous.id`,
        __type: "text"
      },
      name: {
        __path: `${to}.previous.name`,
        __type: "text"
      }
    },
    seriesConfiguration: {
      __path: `${to}.seriesConfiguration.id`,
      __type: "seriesConfiguration",
      __description: `${d("ui.terminology.series")} configuration (${series?.name})`,
      id: {
        __path: `${to}.seriesConfiguration.id`,
        __type: "text"
      },
      name: {
        __path: `${to}.seriesConfiguration.name`,
        __type: "text"
      },
      intervals: {
        __path: `${to}.seriesConfiguration.intervals`,
        __type: "list",
        __description: "List of intervals",
        __configuration: {
          identifier: "intervals",
          type: "list",
          properties: {
            items: [
              {
                type: "json",
                properties: {
                  items: [
                    {
                      type: "text",
                      identifier: "id"
                    },
                    {
                      type: "text",
                      identifier: "name"
                    }
                  ]
                }
              }
            ]
          }
        }
      }
    }
  };
};

const getFormConfigurationDefinition = ({
  to,
  formConfiguration,
  series,
  document,
  d
}: {
  to: string;
  formConfiguration?: ConfigurationFormType;
  series?: SeriesConfig;
  document: Doc<WorkspaceDocument>;
  d: Dictionary;
}) => {
  return {
    __path: `${to}.id`,
    __identifier: "formConfiguration",
    __type: "formConfiguration",
    id: {
      __type: "text",
      __identifier: "id",
      __path: `${to}.id`
    },
    key: {
      __type: "text",
      __identifier: "key",
      __path: `${to}.key`,
      __description: formConfiguration?.key
    },
    name: {
      __type: "text",
      __identifier: "name",
      __path: `${to}.name`,
      __description: formConfiguration?.name
    },
    ...(formConfiguration?.seriesId || series
      ? {
          seriesId: {
            __path: `${to}.seriesId`,
            __type: "text",
            __identifier: "seriesId",
            __description: series?.name
          },
          seriesInterval: getSeriesIntervalDefinition({
            to: `${to}.seriesInterval`,
            identifier: series?.name.replaceAll(" ", ""),
            seriesConfigurationId: series?.id,
            document,
            d
          })
        }
      : {})
  };
};

// TODO: change to only get the id, text, type
const getQuestionDefinition = ({
  to,
  identifier,
  question,
  description = "Question"
}: {
  to: string;
  identifier?: string;
  question?: Question;
  description?: string;
}): VariableTypeDefinition => {
  return {
    ...(identifier ? { __identifier: identifier } : {}),
    __type: question?.type ?? "question",
    __description: description,
    __path: `${to}.answer`,
    __configuration: question,
    configuration: {
      __type: "questionConfiguration",
      __path: `${to}.id`,
      id: {
        __path: `${to}.id`,
        __identifier: "id",
        __type: "text"
      },
      text: {
        __path: `${to}.text`,
        __identifier: "Text",
        __type: "text",
        __description: question?.text
      },
      type: {
        __path: `${to}.type`,
        __identifier: "Type",
        __type: "text",
        __description: question?.type
      }
    },
    ...getQuestionAnswerAndMultiLevelParts({
      question,
      to
    })
  } as VariableTypeDefinition;
};

const getQuestionAnswerAndMultiLevelParts = ({
  question,
  to
}: {
  question?: Question;
  to: string;
}) => {
  if (!question || !multiLevelQuestionTypes.includes(question.type)) {
    return {
      answer: {
        __path: `${to}.answer`,
        __identifier: "answer",
        __type: question?.type ?? "unknown"
      }
    };
  }

  // JSON
  if (question.type === QuestionTypes.JSON) {
    const processJsonQuestionItem = (
      item: Question,
      basePath: string
    ): [string, object] => {
      if (item.type === QuestionTypes.JSON) {
        // Handle nested JSON
        return [
          item.identifier,
          {
            __path: `${basePath}.${item.id ?? item.identifier}`,
            __identifier: item.identifier ?? item.id,
            __description: item.text,
            __type: item.type,
            __configuration: {
              identifier: item.identifier,
              type: item.type,
              properties: {
                items:
                  (item.properties as JSONQuestionProperties)?.items?.map(
                    (nestedItem: Question) => ({
                      identifier: nestedItem.identifier,
                      type: nestedItem.type
                    })
                  ) ?? []
              }
            },
            ...Object.fromEntries(
              (item.properties as JSONQuestionProperties)?.items?.map(
                nestedItem =>
                  processJsonQuestionItem(
                    nestedItem,
                    `${basePath}.${item.id ?? item.identifier}`
                  )
              ) ?? []
            )
          }
        ];
      }

      // Handle non-JSON types
      return [
        item.identifier,
        {
          __path: `${basePath}.${item.id ?? item.identifier}`,
          __identifier: item.identifier ?? item.id,
          __description: item.text,
          __type: item.type,
          __configuration: {
            identifier: item.identifier,
            type: item.type,
            properties: {
              items: [
                {
                  type: item.type ?? "unknown"
                }
              ]
            }
          }
        }
      ];
    };

    return {
      answer: {
        __path: `${to}.answer`,
        __identifier: "answer",
        __type: "json",
        __configuration: {
          identifier: question?.identifier,
          type: "json",
          properties: {
            items:
              (question.properties as JSONQuestionProperties)?.items?.map(
                (item: Question) => ({
                  identifier: item.identifier,
                  type: item.type
                })
              ) ?? []
          }
        },
        ...Object.fromEntries(
          (question.properties as JSONQuestionProperties)?.items?.map(item =>
            processJsonQuestionItem(item, `${to}.answer`)
          ) ?? []
        )
      },
      ...Object.fromEntries(
        (question.properties as JSONQuestionProperties)?.items?.map(item =>
          processJsonQuestionItem(item, `${to}.items`)
        ) ?? []
      )
    };
  }

  // List
  if (question.type === QuestionTypes.LIST) {
    const listOfQuestion = (question?.properties as ListQuestionProperties)
      ?.items[0];
    return {
      answer: {
        __path: `${to}.answer`,
        __identifier: "answer",
        __type: "list",
        __description: `List items`, // TODO: list of type
        __configuration: {
          identifier: question?.identifier,
          type: "list",
          properties: {
            items: [
              {
                type: listOfQuestion?.type ?? "unknown",
                properties: listOfQuestion?.properties
              }
            ]
          }
        }
      }
    };
  }

  // Table
  if (question.type === QuestionTypes.TABLE) {
    return {
      answer: {
        __path: `${to}.answer`,
        __identifier: "answer",
        __type: "table",
        __description: `Rows`,
        __configuration: {
          identifier: question?.identifier,
          type: "table",
          properties: {
            columns: [
              {
                id: "_rowId",
                identifier: "RowId",
                text: "Row ID",
                type: "text"
              },
              {
                id: "_rowIndex",
                identifier: "RowIndex",
                text: "Row Index",
                type: "number"
              },
              ...((
                question.properties as TableQuestionProperties
              )?.columns?.map((column: Question) => {
                return {
                  ...(column.id ? { id: column.id } : {}),
                  identifier: column.identifier,
                  type: column.type
                };
              }) ?? [])
            ]
          }
        }
      },
      ...Object?.fromEntries(
        (question.properties as TableQuestionProperties)?.columns?.map(
          (item: Question) => {
            return [
              item.identifier,
              {
                __type: "list", // item.type,
                __configuration: {
                  identifier: item.identifier,
                  type: item.type,
                  properties: {
                    items: [
                      {
                        type: item.type ?? "unknown"
                      }
                    ]
                  }
                },
                ...getQuestionDefinition({
                  question: item,
                  to: `${to}.columns.${item.id ?? item.identifier}`,
                  description: `Column`
                }),
                answer: {
                  __path: `${to}.columns.${item.id ?? item.identifier}.answer`,
                  __type: "list",
                  __description: `Column values (list of ${item.type})`,
                  __configuration: {
                    identifier: item.identifier,
                    type: "list",
                    properties: {
                      items: [
                        {
                          type: item.type ?? "unknown"
                        }
                      ]
                    }
                  }
                }
              }
            ];
          }
        ) ?? []
      )
    };
  }
};
