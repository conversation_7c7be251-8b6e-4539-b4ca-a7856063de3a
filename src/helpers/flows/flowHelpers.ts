import get from "lodash/get";

import {
  FlowConfiguration,
  MockFlowContext,
  MockFlowContextWithLocalStep
} from "@src/types/FlowConfiguration/FlowConfiguration";
import {
  FlowStep,
  FlowStepId,
  FlowStepVariant
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import {
  CommonTypedStepProperties,
  ConditionStepProperties,
  IteratorStepProperties
} from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";
import { Workspace } from "@src/types/workspace";

export const getStepIdToParentStepId = ({
  configurationFlow
}: {
  configurationFlow:
    | FlowConfiguration
    | IteratorStepProperties["configuration"];
}) => {
  let stepIdToParentStepId: {
    [stepId: FlowStepId]: FlowStepId;
  } = {};

  Object.entries(configurationFlow.steps).forEach(([stepId, step]) => {
    const nextStep = configurationFlow.steps[step.next ?? ""];
    if (nextStep?.variant === FlowStepVariant.ITERATOR) {
      const iteratorStartStep = (nextStep as FlowStep<IteratorStepProperties>)
        .properties.configuration.start;
      if (iteratorStartStep) {
        stepIdToParentStepId[iteratorStartStep] = nextStep.id;
      }
    }

    if (step.variant === FlowStepVariant.CONDITION) {
      for (const branch of (step.properties as ConditionStepProperties)
        .branches) {
        if (!branch.next) {
          continue;
        }
        stepIdToParentStepId[branch.next] = stepId;
        const nextStep = configurationFlow.steps[branch.next];

        if (nextStep?.variant !== FlowStepVariant.ITERATOR) {
          continue;
        }

        const iteratorStartStep = (nextStep as FlowStep<IteratorStepProperties>)
          .properties.configuration.start;
        if (!iteratorStartStep) {
          continue;
        }
        stepIdToParentStepId[iteratorStartStep] = branch.next;
      }
    } else if (step.variant === FlowStepVariant.ITERATOR) {
      const iteratorStepIdsToParentStep = getStepIdToParentStepId({
        configurationFlow: (step.properties as IteratorStepProperties)
          .configuration
      });
      stepIdToParentStepId = {
        ...stepIdToParentStepId,
        ...iteratorStepIdsToParentStep
      };
    }
    if (step.next) {
      stepIdToParentStepId[step.next] = stepId;
    }
  });

  return stepIdToParentStepId;
};

export const getOrderedStepIds = ({
  configurationFlow
}: {
  configurationFlow: FlowConfiguration;
}) => {
  const stepIds = Object.keys(configurationFlow.triggers ?? {});

  const pathsToCover: FlowStepId[] = [];
  if (configurationFlow.start) {
    pathsToCover.push(configurationFlow.start);
  }

  for (let i = 0; i < pathsToCover.length; i++) {
    let step = configurationFlow.steps[pathsToCover[i]];
    while (step?.next) {
      if (step.variant === FlowStepVariant.CONDITION) {
        pathsToCover.push(
          ...((step.properties as ConditionStepProperties).branches
            .map(branch => branch.next)
            .filter(e => !!e) as FlowStepId[])
        );
      }
      if (step.variant === FlowStepVariant.ITERATOR) {
        for (const childStepId of Object.keys(
          (
            configurationFlow?.steps[step.id]
              ?.properties as IteratorStepProperties
          )?.configuration?.steps ?? {}
        )) {
          stepIds.push(childStepId);
        }
      }
      stepIds.push(step.id);
      step = configurationFlow.steps[step?.next];
    }

    if (step.variant === FlowStepVariant.CONDITION) {
      pathsToCover.push(
        ...((step.properties as ConditionStepProperties).branches
          .map(branch => branch.next)
          .filter(e => !!e) as FlowStepId[])
      );
    }
    stepIds.push(step.id);
  }

  return stepIds;
};

export const getStepIdPathToStepId = ({
  stepId,
  configurationFlow
}: {
  stepId: FlowStepId;
  configurationFlow: FlowConfiguration;
}): FlowStepId[] => {
  const stepIdToParentStepId = getStepIdToParentStepId({
    configurationFlow
  });

  const stepIds = [];

  let previousStepId = stepIdToParentStepId[stepId];
  while (previousStepId) {
    stepIds.push(previousStepId);
    previousStepId = stepIdToParentStepId[previousStepId];
  }

  return [
    ...Object.keys(configurationFlow.triggers ?? {}),
    ...stepIds.reverse()
  ];
};

export const populateRealValuesUsingMockFlowContext = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  itemWithDoubleCurlyBraces: any,
  flowContext: MockFlowContext | MockFlowContextWithLocalStep,
  replaceWithEmptyStringIfUndefined = false
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): any => {
  type JSONValue =
    | string
    | number
    | boolean
    | null
    | JSONValue[]
    | { [key: string]: JSONValue };

  function resolveNestedPlaceholders(str: string): string {
    let prev: string;
    let curr = str;
    let iterations = 0;
    const MAX_ITERATIONS = 35; // Prevent infinite loops
    // Keep replacing until no more changes (handles nested placeholders)
    do {
      prev = curr;
      curr = curr.replace(/{{([^{}]*)}}/g, (match, p1) => {
        // Recursively resolve inner placeholders in the key
        const resolvedKey = resolveNestedPlaceholders(p1);
        const value = get(flowContext, resolvedKey);
        if (value === undefined) {
          return replaceWithEmptyStringIfUndefined ? "" : match;
        }
        // If value is an object/array, keep as JSON string
        if (typeof value === "object") {
          return JSON.stringify(value);
        }
        return String(value);
      });
      iterations++;
      if (iterations > MAX_ITERATIONS) {
        throw new Error(
          "Possible infinite placeholder resolution loop detected"
        );
      }
    } while (curr !== prev && /{{([^{}]*)}}/.test(curr));
    return curr;
  }

  function replacePlaceholders(obj: JSONValue): JSONValue {
    if (typeof obj === "string") {
      // Check if the string is exactly a single placeholder
      const singlePlaceholderMatch = /^{{([^{}]*)}}$/.exec(obj);
      if (singlePlaceholderMatch) {
        const resolvedKey = resolveNestedPlaceholders(
          singlePlaceholderMatch[1]
        );
        const value = get(flowContext, resolvedKey);
        if (value === undefined) {
          return replaceWithEmptyStringIfUndefined ? "" : obj;
        }
        return JSON.parse(JSON.stringify(value)); // can be automerge proxy object
      }
      // Replace all placeholders, including nested
      return resolveNestedPlaceholders(obj);
    } else if (Array.isArray(obj)) {
      return obj.map(replacePlaceholders);
    } else if (obj && typeof obj === "object") {
      const result: JSONValue = {};
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          const keyResult = replacePlaceholders(obj[key]);
          result[key] = keyResult;
        }
      }
      return result;
    }
    // For numbers, booleans, null, undefined, etc.
    return obj;
  }

  return replacePlaceholders(itemWithDoubleCurlyBraces);
};

export const getInitialMockFlowContext = ({
  workspace,
  configurationFlow
}: {
  workspace: Workspace;
  configurationFlow: FlowConfiguration;
}): MockFlowContext => {
  return {
    global: {
      workspaceId: workspace.id,
      workspaceVersion: 0,
      tenantId: "mock-tenant-id",
      flowConfigurationName: configurationFlow.name,
      flowConfigurationId: configurationFlow.id,
      tenantOrigin: window.location.origin // This variable is only used for Documentation
    },
    variables: {},
    // TODO: once starting variables are resolved properly from a trigger add this back
    // variables: Object.fromEntries(
    //   configurationFlow.startingVariables?.map(variable => [
    //     variable.identifier,
    //     {
    //       ...variable,
    //       sourceStepId: undefined,
    //       isStartingVariable: true,
    //       isEndingVariable: false
    //     }
    //   ]) ?? []
    // ),
    stepOutputVariables: [],
    event: {
      eventProperties: {},
      key: "mock-key",
      workspaceId: workspace.id,
      workspaceVersion: 0,
      tenantId: "mock-tenant-id",
      createdAt: new Date().toISOString(),
      createdBy: "mock-user-id"
    }
  };
};

export const getLocalStepContext = ({
  step,
  flowContext,
  index
}: {
  step: FlowStep;
  flowContext: MockFlowContext;
  index?: number;
}): MockFlowContextWithLocalStep => {
  return {
    ...flowContext,
    thisStep: {
      id: step.id,
      ...((step.properties as CommonTypedStepProperties) ?? {}).inputs
    },
    thisIndex: index
  };
};
