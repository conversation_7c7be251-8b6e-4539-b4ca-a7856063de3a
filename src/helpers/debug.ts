const debugHostnames = ["localhost", "ot.innovation.dev.oneteam.services"];

export function logLinkToDebugPage(documentId: string, documentType: string) {
  if (debugHostnames.indexOf(window.location.hostname) > -1) {
    console.log(
      `View the ${documentType} document at ${window.location.origin}/ai/api/debug/#automerge:${documentId}`
    );
  }
  return `${window.location.origin}/ai/api/debug/#automerge:${documentId}`;
}

export function getLinkToDebugPage(documentId: string) {
  return `${window.location.origin}/ai/api/debug/#automerge:${documentId}`;
}

export function getLinkToFEDDebugPage(
  workspaceId: string,
  flowExecutionId: string
) {
  return `${window.location.origin}/ai/api/workspaces/${workspaceId}/flowExecutions/${flowExecutionId}/view`;
}
