import { FormAnswer } from "src/documentTypes.ts";
import { QuestionDepthType } from "src/types/questionType.ts";

import { appConfig } from "./appConfig.ts";

export const handleFetchResponse = async (response: Response) => {
  if (!response.ok) {
    const error = await response.text();
    throw new Error(error);
  }
  return await response.json();
};

export function fetchDocPath(
  ans: {
    [id: string]: unknown;
  },
  path: string[],
  rowId?: string
): Record<string, unknown> | undefined {
  let obj: unknown = ans;
  for (let i = 1; i < path.length - 1; i++) {
    if (rowId && path[i] === "rowId") {
      obj = (obj as Record<string, unknown>)[rowId];
    } else {
      obj = (obj as Record<string, unknown>)[path[i]];
    }
  }
  return obj as Record<string, unknown>;
}

export function checkIfJsonAnswer(
  ans: {
    [id: string]: FormAnswer;
  },
  path: string[]
) {
  const jsonPath = path.slice(0, 2);
  const jsonAnswer = fetchDocPath(ans, jsonPath) as {
    [id: string]: FormAnswer;
  };
  return jsonAnswer?.[jsonPath[1]]?.type === "json";
}

export function fetchAnswerValue(ans: FormAnswer, path: string[]): FormAnswer {
  let obj: unknown = ans;
  for (const key of path) {
    obj = (obj as Record<string, unknown>)[key];
  }
  return obj as FormAnswer;
}

export function getJwtToken(): string {
  const token = appConfig.jwtToken;
  if (!token) {
    throw new Error("Token not found");
  }
  return token;
}

export const answerPath = (path: string[]) => {
  return {
    getQuestionId: () => {
      return path[QuestionDepthType.QUESTION];
    },
    getColumnId: () => {
      return path[QuestionDepthType.COLUMN];
    },
    getRowId: () => {
      return path[QuestionDepthType.ROW];
    },
    getQuestionDepth: () => {
      return path.length - 1;
    },
    getPathToQuestion: () => {
      return path.slice(0, QuestionDepthType.QUESTION + 1);
    },
    getPathToRow: () => {
      return path.slice(0, QuestionDepthType.ROW + 1);
    },
    getPathToColumn: () => {
      return path.slice(0, QuestionDepthType.COLUMN + 1);
    }
  };
};
