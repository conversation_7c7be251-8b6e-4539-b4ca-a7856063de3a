import { useMemo } from "react";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { useDictionary } from "@src/hooks/useDictionary";
import { Page } from "@src/types/Page";
import { User } from "@src/types/User";
import {
  NewWorkspaceUser,
  WorkspaceUserForUpdate,
  WorkspaceUserSearch
} from "@src/types/WorkspaceUser";
import { Workspace } from "@src/types/workspace";

import { getData } from "./getData";
import { postData } from "./postData";
import { putData } from "./putData";

export const useUsersInTenantSearch = (
  searchTerm?: string,
  enabled: boolean = true
) => {
  return useQuery<Page<User>>({
    queryKey: ["/users", searchTerm ?? ""],
    queryFn: (): Promise<Page<User>> => {
      return getData(`/users${searchTerm ? `?search=${searchTerm}` : ""}`);
    },
    enabled
  });
};

export const useGetUserById = (userId?: number) => {
  return useQuery<User>({
    queryKey: [`/users/${userId}`],
    queryFn: (): Promise<User> => {
      return getData(`/users/${userId}`);
    },
    enabled: !!userId
  });
};

export const useWorkspaceUsersSearch = (
  workspaceId?: number,
  params?: URLSearchParams,
  enabled: boolean = true
) => {
  return useQuery<Page<WorkspaceUserSearch>>({
    queryKey: [`/workspaces/${workspaceId}/users`, params?.toString()],
    queryFn: (): Promise<Page<WorkspaceUserSearch>> => {
      return getData(`/workspaces/${workspaceId}/users`, params);
    },
    enabled: !!workspaceId && enabled
  });
};

export const useGetWorkspaceUsersByWorkspaceIdAndUserId = (
  workspaceId: number,
  userId?: number
) => {
  const queryKey = useMemo(
    () => [`/workspaces/${workspaceId}/users/${userId}`],
    [workspaceId, userId]
  );
  return useQuery<WorkspaceUserSearch>({
    queryKey: queryKey,
    queryFn: (): Promise<WorkspaceUserSearch> => {
      return getData(`/workspaces/${workspaceId}/users/${userId}`);
    },
    enabled: !!userId && !!workspaceId
  });
};

export const useAddWorkspaceUser = ({
  workspace
}: {
  workspace: Workspace;
}) => {
  const queryClient = useQueryClient();
  const d = useDictionary();
  const { mutate: addWorkspaceUsers } = useMutation({
    mutationFn: (users: NewWorkspaceUser[]) =>
      postData(`/workspaces/${workspace.id}/users`, users),
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: [`/workspaces/${workspace.id}/users`]
      });
    },
    onError: error => {
      alert(`${d("ui.settings.permissions.addUsers.alert")} ${error.message}`);
    }
  });

  return {
    addWorkspaceUsers
  };
};

export const useUpdateWorkspaceUser = ({
  workspace
}: {
  workspace: Workspace;
}) => {
  const d = useDictionary();
  const queryClient = useQueryClient();
  const { mutate: updateWorkspaceUser } = useMutation({
    mutationFn: (user: WorkspaceUserForUpdate) =>
      putData(`/workspaces/${workspace.id}/users`, user),
    onSuccess: async updatedUser => {
      await queryClient.invalidateQueries({
        queryKey: [`/workspaces/${workspace.id}/users`]
      });
      await queryClient.invalidateQueries({
        queryKey: [`/workspaces/${workspace.id}/users/${updatedUser.user.id}`]
      });
    },
    onError: error => {
      alert(
        `${d("ui.settings.permissions.updateUser.alert")} ${error.message}`
      );
    }
  });

  return {
    updateWorkspaceUser
  };
};
