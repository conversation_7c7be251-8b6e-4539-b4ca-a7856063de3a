import debug from "debug";

export const Logger = (namespace: string) => {
  return new LoggerClass(namespace);
};

const createDebugLogger = (
  namespace: string,
  logLevel: "trace" | "debug" | "info" | "warn" | "error"
) => {
  const log = debug(namespace);
  log.log = console[logLevel].bind(console);
  return log;
};

/**
 * Logger helpers using the debug library
 * @see https://www.npmjs.com/package/debug
 */
class LoggerClass {
  private traceLogger?: debug.Debugger;
  private debugLogger?: debug.Debugger;
  private infoLogger?: debug.Debugger;
  private warnLogger?: debug.Debugger;
  private errorLogger?: debug.Debugger;

  constructor(readonly namespace: string) {}

  trace(format: string, ...args: unknown[]) {
    this.getLogger("trace").apply(console, [format, ...args]);
  }
  debug(format: string, ...args: unknown[]) {
    this.getLogger("debug").apply(console, [format, ...args]);
  }
  info(format: string, ...args: unknown[]) {
    this.getLogger("info").apply(console, [format, ...args]);
  }
  warn(format: string, ...args: unknown[]) {
    this.getLogger("warn").apply(console, [format, ...args]);
  }
  error(format: string, ...args: unknown[]) {
    this.getLogger("error").apply(console, [format, ...args]);
  }

  private getLogger(level: "trace" | "debug" | "info" | "warn" | "error") {
    switch (level) {
      case "trace":
        this.traceLogger ??= createDebugLogger(this.namespace, "trace");
        return this.traceLogger;
      case "debug":
        this.debugLogger ??= createDebugLogger(this.namespace, "debug");
        return this.debugLogger;
      case "info":
        this.infoLogger ??= createDebugLogger(this.namespace, "info");
        return this.infoLogger;
      case "warn":
        this.warnLogger ??= createDebugLogger(this.namespace, "warn");
        return this.warnLogger;
      case "error":
        this.errorLogger ??= createDebugLogger(this.namespace, "error");
        return this.errorLogger;
      default:
        throw new Error(`Unknown log level: ${level}`);
    }
  }
}
