import { BreadcrumbsItemType } from "@oneteam/onetheme";
import { generatePath } from "react-router-dom";

import { routeConstants } from "../constants/routeConstants.ts";

type BreadcrumbConstants = {
  [route: string]: Partial<BreadcrumbsItemType> & {
    key?: string;
    href: string;
    checkIsInList: (
      matchPath: (path: string, properties?: { exact?: boolean }) => boolean
    ) => boolean;
  };
};

export enum BreadcrumbDynamicNames {
  FORM_CONFIGURATION = "formConfiguration",
  FLOW_CONFIGURATION = "flowConfiguration"
}

export type DynamicBreadcrumbLookup = {
  [key in BreadcrumbDynamicNames]?: {
    text: BreadcrumbsItemType["text"];
  };
};

export const breadcrumbConstants = ({
  replacements,
  dynamicBreadcrumbLookup,
  onClickWorkspaceHome
}: {
  replacements?: { [key: string]: string | undefined };
  dynamicBreadcrumbLookup?: DynamicBreadcrumbLookup;
  onClickWorkspaceHome: (path: string) => void;
}) =>
  ({
    [routeConstants.home]: {
      key: replacements?.workspaceKey ?? "ui.collection.title",
      href: generatePath(routeConstants.home, replacements),
      // Always visible in a workspace
      checkIsInList: matchPath =>
        matchPath(routeConstants.home, { exact: false }),
      onClick: () =>
        onClickWorkspaceHome(generatePath(routeConstants.home, replacements))
    },
    [routeConstants.flowRunner]: {
      key: "ui.flow.title",
      href: generatePath(routeConstants.flowRunner, replacements),
      checkIsInList: matchPath =>
        matchPath(routeConstants.flowRunner, { exact: false }) &&
        !matchPath(routeConstants.configuration, { exact: false }) &&
        !matchPath(routeConstants.settings, { exact: false })
    },
    [routeConstants.configuration]: {
      key: "ui.configuration.title",
      href: generatePath(routeConstants.configuration, replacements),
      checkIsInList: matchPath => matchPath(routeConstants.configuration)
    },
    [routeConstants.configurationFlowList]: {
      key: "ui.configuration.flows.title",
      href: generatePath(routeConstants.configurationFlowList, replacements),
      checkIsInList: matchPath =>
        matchPath(routeConstants.configurationFlowList)
    },
    [routeConstants.configurationFormList]: {
      key: "ui.configuration.forms.title",
      href: generatePath(routeConstants.configurationFormList, replacements),
      checkIsInList: matchPath =>
        matchPath(routeConstants.configurationFormList)
    },
    [routeConstants.configurationFoundation]: {
      key: "ui.configuration.foundations.title",
      href: generatePath(routeConstants.configurationFoundation, replacements),
      checkIsInList: matchPath =>
        matchPath(routeConstants.configurationFoundation)
    },
    [routeConstants.configurationForm]: {
      text: dynamicBreadcrumbLookup?.formConfiguration?.text,
      href: replacements?.configurationFormKey
        ? generatePath(routeConstants.configurationForm, replacements)
        : "/",
      checkIsInList: matchPath => matchPath(routeConstants.configurationForm)
    },
    [routeConstants.settings]: {
      key: "ui.settings.title",
      href: generatePath(routeConstants.settings, replacements),
      checkIsInList: matchPath => matchPath(routeConstants.settings)
    },
    [routeConstants.settingsPermissions]: {
      key: "ui.settings.permissions.title",
      href: generatePath(routeConstants.settingsPermissions, replacements),
      checkIsInList: matchPath => matchPath(routeConstants.settingsPermissions)
    },
    [routeConstants.configurationFlow]: {
      text: dynamicBreadcrumbLookup?.flowConfiguration?.text,
      href: replacements?.configurationFlowId
        ? generatePath(routeConstants.configurationFlow, replacements)
        : "/",
      checkIsInList: matchPath => matchPath(routeConstants.configurationFlow)
    },
    [routeConstants.configurationSeries]: {
      key: "ui.configuration.series.title",
      href: generatePath(routeConstants.configurationSeries, replacements),
      checkIsInList: matchPath => matchPath(routeConstants.configurationSeries)
    },
    [routeConstants.configurationLabels]: {
      key: "ui.configuration.labels.title",
      href: generatePath(routeConstants.configurationLabels, replacements),
      checkIsInList: matchPath => matchPath(routeConstants.configurationLabels)
    }
  }) as BreadcrumbConstants;
