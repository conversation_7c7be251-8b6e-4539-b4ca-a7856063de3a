import dotenv from "dotenv";

dotenv.config();

if (
  process.env.JWT_PUBLIC_KEY === undefined ||
  process.env.JWT_PUBLIC_KEY === ""
) {
  throw new Error("Missing JWT_PUBLIC_KEY in config");
}

const jwtPublicKey = process.env.JWT_PUBLIC_KEY.split("\n")
  .map(line => line.trimStart())
  .join("\n");

if (process.env.JWT_TOKEN === undefined || process.env.JWT_TOKEN === "") {
  throw new Error("Missing JWT_TOKEN in config");
}

const jwtToken = process.env.JWT_TOKEN;

const DB_CONFIG = Object.freeze({
  maxPoolSize: parseInt(process.env.DB_POOL_SIZE ?? "20", 10),
  idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT_MS ?? "30000", 10),
  connectionTimeoutMillis: parseInt(
    process.env.DB_CONNECTION_TIMEOUT_MS ?? "2000",
    10
  )
});

const tenantId = process.env.TENANT_ID
  ? parseInt(process.env.TENANT_ID, 10)
  : 1;

const otaiService = Object.freeze({
  hostname: process.env.OTAI_HOSTNAME ?? "",
  apiUrl: process.env.OTAI_API_URL ?? ""
});

export const appConfig = Object.freeze({
  jwtPublicKey,
  jwtToken,
  sessionEncrypt: process.env.SESSION_ENCRYPT ?? "",
  dbConfig: DB_CONFIG,
  port: process.env.PORT !== undefined ? parseInt(process.env.PORT) : 3030,
  tenantId,
  otaiService
});
