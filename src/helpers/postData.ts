import { appConfig } from "./appConfig.ts";
import { handleFetchResponse } from "./fetchHelper.ts";

export const postData = async (
  endpoint: string,
  body: object,
  token: string
) => {
  return fetch(
    `${appConfig.otaiService.hostname}${appConfig.otaiService.apiUrl}${endpoint}`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
        origin: `${appConfig.otaiService.hostname}`
      },
      body: JSON.stringify(body)
    }
  ).then(handleFetchResponse);
};
