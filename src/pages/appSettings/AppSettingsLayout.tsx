import React, { ReactNode, useCallback, useEffect, useRef } from "react";

import { ChangeFn, ChangeOptions } from "@automerge/automerge/next";
import {
  Box,
  Loading,
  PageTemplate,
  ToastNotificationVariant,
  ToastStack,
  ToastStackHandle
} from "@oneteam/onetheme";
import { Outlet, useNavigate } from "react-router-dom";

import { SideNavigation } from "@components/shared/Navigation/SideNavigation.tsx";
import { TopNavigation } from "@components/shared/Navigation/TopNavigation.tsx";
import { useSideNavigationContext } from "@components/shared/Navigation/useSideNavigationContext.ts";

import { routeConstants } from "@src/constants/routeConstants.ts";
import { useLogout } from "@src/hooks/useLogout.js";
import { useMatchPath } from "@src/hooks/useMatchPath.tsx";
import { useOneTeam } from "@src/hooks/useOneTeam.js";
import { Action } from "@src/hooks/usePubSub.tsx";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { useAuth } from "../../hooks/useAuth.tsx";

interface LayoutProps {
  readonly pageError?: ReactNode;
}

export type DocChange = (
  changeFn: ChangeFn<WorkspaceDocument>,
  options?: ChangeOptions<WorkspaceDocument> | undefined
) => void;

// TODO: this is duplicated against workspace layout so should be moved to a common place
export type PushToastNotification = (
  heading: string,
  description: string,
  variant: ToastNotificationVariant,
  durationMs?: number,
  action?: Action
) => void;

export const AppSettingsLayout = ({ pageError = null }: LayoutProps) => {
  useLogout();
  useOneTeam();
  const { user } = useAuth();
  const navigate = useNavigate();
  const { matchPath } = useMatchPath();

  // NB: if accessing top level we need to redirect to the first page in the AppSettings
  useEffect(() => {
    if (matchPath(routeConstants.appSettings, { exact: true })) {
      navigate(routeConstants.appSettingsApiKeys, { replace: true });
    }
  }, [matchPath, navigate]);

  const toastStackRef = useRef<ToastStackHandle>(null);
  const pushToastNotification = useCallback<PushToastNotification>(
    (
      heading: string,
      description: string,
      variant: ToastNotificationVariant,
      durationMs: number = 15000
      // action?: Action
    ) => {
      toastStackRef.current?.pushToast({
        heading,
        description,
        variant,
        duration: durationMs,
        actions: (() => {
          return <></>;
        })()
      });
    },
    []
  );

  // probably different cos its application
  const { context } = useSideNavigationContext();
  const content = useCallback(() => {
    if (!user) {
      return (
        <Box height="100" contentsHeight="fill">
          <Loading size={24} />
        </Box>
      );
    }

    return (
      <Outlet
        context={{
          user,
          pushToastNotification
        }}
      />
    );
  }, [user, pushToastNotification]);

  return (
    <PageTemplate
      // TODO: TopNavigation needs to show previous workspace if available
      topNavigation={<TopNavigation user={user} workspace={null} />}
      sideNavigation={
        context ? (
          <SideNavigation
            context={context}
            workspace={undefined}
            docErrorHelper={undefined}
            pushToastNotification={pushToastNotification}
          />
        ) : undefined
      }
    >
      <Box
        position="fixed"
        style={{
          top: "var(--components-page-body-template-padding-vertical, 16px)",
          right:
            "var(--components-page-body-template-padding-horizontal, 24px)",
          zIndex: 1000
        }}
      >
        <ToastStack
          ref={toastStackRef}
          position="top-right"
          handleClearAll={toasts => {
            return toasts.map(toast => ({ ...toast, visible: false }));
          }}
        />
      </Box>
      {pageError ?? content()}
    </PageTemplate>
  );
};
