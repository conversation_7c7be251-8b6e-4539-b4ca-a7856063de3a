import { ToastNotificationVariant } from "@oneteam/onetheme";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { deleteData } from "@helpers/deleteData";
import { CustomError } from "@helpers/errorHelper";
import { getData } from "@helpers/getData";
import { postData } from "@helpers/postData";

import { Dictionary } from "@src/hooks/useDictionary";
import { ApiKey, ApiKeyForCreate, RevealedApiKey } from "@src/types/ApiKeys";
import { Page } from "@src/types/Page";

import { PushToastNotification } from "../../AppSettingsLayout";

export const APIKEYS_QUERY_KEY = "application-api-keys";

const NOTIFICATION_DURATION_MS = 10000;

export const useCreateApiKey = ({
  d,
  pushToastNotification
}: {
  d: Dictionary;
  pushToastNotification: PushToastNotification;
}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ApiKeyForCreate) => {
      return postData(`/api-management/api-keys`, data);
    },
    onSuccess: async (data: RevealedApiKey) => {
      await queryClient.invalidateQueries({ queryKey: [APIKEYS_QUERY_KEY] });
      pushToastNotification(
        d("ui.notifications.success"),
        d("ui.appSettings.apiKeys.create.success"),
        ToastNotificationVariant.SUCCESS,
        NOTIFICATION_DURATION_MS
      );
      return data;
    },
    onError: (error: CustomError) => {
      console.error("Error creating API Key:", error);
      pushToastNotification(
        d("errors.common.creationFailed"),
        d("ui.appSettings.apiKeys.create.failed"),
        ToastNotificationVariant.DANGER,
        NOTIFICATION_DURATION_MS
      );
      return error;
    }
  });
};

export const useDeleteApiKey = ({
  d,
  pushToastNotification
}: {
  d: Dictionary;
  pushToastNotification: PushToastNotification;
}) => {
  const queryClient = useQueryClient();

  return useMutation({
    networkMode: "always",
    mutationFn: (data: ApiKey) =>
      deleteData(`/api-management/api-keys/${data?.id}`, data),
    onSuccess: async (_, data: ApiKey) => {
      await queryClient.invalidateQueries({ queryKey: [APIKEYS_QUERY_KEY] });
      pushToastNotification(
        d("ui.notifications.success"),
        d("ui.appSettings.apiKeys.delete.success", {
          apiKeyName: data.name
        }),
        ToastNotificationVariant.SUCCESS,
        NOTIFICATION_DURATION_MS
      );
    },
    onError: (error: CustomError, data: ApiKey) => {
      console.error("Error deleting API Key:", error);
      pushToastNotification(
        d("errors.common.deletionFailed"),
        d("errors.appSettings.apiKeys.delete", {
          apiKeyName: data?.name
        }),
        ToastNotificationVariant.DANGER,
        NOTIFICATION_DURATION_MS
      );
      return error;
    }
  });
};

export const useApiKeysSearch = (params: URLSearchParams) => {
  const queryKey = [APIKEYS_QUERY_KEY, params.toString()];

  return useQuery<Page<ApiKey>>({
    queryKey: queryKey,
    queryFn: (): Promise<Page<ApiKey>> => {
      return getData(`/api-management/api-keys`, params);
    }
  });
};

export const buildParamsForApiKeysSearch = (
  textFilter: string | undefined,
  queryParams: URLSearchParams
): URLSearchParams => {
  const params = new URLSearchParams();
  if (textFilter) {
    params.set("search", textFilter ?? "");
  }

  queryParams.forEach((value, key) => {
    params.append(key, value);
  });

  return params;
};
