import React, {
  MutableRefObject,
  useCallback,
  useEffect,
  useMemo,
  useState
} from "react";

import {
  ColorText,
  DropdownItem,
  DropdownItemGroup,
  Icon,
  Inline,
  KebabMenu,
  PageBodyTemplate,
  Pill,
  PillSelect,
  SearchBar,
  SelectValue,
  StatusLine,
  TableColumn,
  TableKebabMenuFormatter,
  TableWithPagination,
  Text,
  TextAlignment,
  TextSize,
  Tooltip,
  useQueryParams,
  useTableSort
} from "@oneteam/onetheme";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  generatePath,
  useLocation,
  useNavigate,
  useOutletContext,
  useParams,
  useSearchParams
} from "react-router-dom";

import { getLinkToFEDDebugPage, logLinkToDebugPage } from "@helpers/debug.ts";
import { buildQueryParams, mergeQueryParams } from "@helpers/pagination.ts";
import { postData } from "@helpers/postData.ts";
import { WorkspaceConfigurationHelper } from "@helpers/workspace/WorkspaceConfigurationHelper.ts";

import {
  calculateRunTime,
  flowResultOptions,
  flowStatusOptions,
  formatDate,
  resultColumnGetVariant,
  statusColumnGetVariant,
  statusLineGetVariant
} from "@pages/collection/flows/FlowExecutionHelper";

import { UserLastActiveTabs } from "@src/authentication/AuthContext.tsx";
import { commonIcons } from "@src/constants/iconConstants.ts";
import { routeConstants } from "@src/constants/routeConstants.ts";
import { useAuth } from "@src/hooks/useAuth.tsx";
import { useBreadcrumbs } from "@src/hooks/useBreadcrumbs.tsx";
import { Dictionary, useDictionary } from "@src/hooks/useDictionary.tsx";
import { FlowRunnerModal } from "@src/pages/collection/flows/FlowRunnerModal.tsx";
import {
  FlowResult,
  FlowStatus
} from "@src/types/FlowConfiguration/FlowExecution.ts";
import {
  FlowExecution,
  buildQueryParamsForFlowExecutionSearch
} from "@src/types/collection/FlowExecution";
import { WorkspaceDocument } from "@src/types/documentTypes";
import { Workspace } from "@src/types/workspace.ts";

import { useFlowExecutionsSearch } from "./FlowExecutionHelper.ts";

export const FlowRunner = () => {
  const navigate = useNavigate();
  const params = useParams();
  const { pathname, search, hash } = useLocation();
  const { lastActiveTabs, updateLastActiveTabs } = useAuth();
  const d = useDictionary();

  const { workspace, document, flowRunnerRefresh } = useOutletContext<{
    workspace: Workspace;
    document: WorkspaceDocument;
    flowRunnerRefresh: MutableRefObject<() => void>;
  }>();

  const [flowRunnerSelectorOpen, setFlowRunnerSelectorOpen] = useState(false);

  const breadcrumbs = useBreadcrumbs();

  const { queryParams, updateQueryParams } = useQueryParams({
    initialDefaultIfNone: { page: "1" },
    useSearchParams
  });

  const { statusFilter, resultFilter, textFilter, page, pageSize, asc, dsc } =
    useMemo(
      () => ({
        statusFilter: queryParams.get("status") ?? undefined,
        resultFilter: queryParams.get("result") ?? undefined,
        tagFilter: queryParams.get("tag") ?? undefined,
        textFilter: queryParams.get("search") ?? undefined,
        page: queryParams.get("page") ?? undefined,
        pageSize: queryParams.get("pageSize") ?? undefined,
        asc: queryParams.get("asc") ?? undefined,
        dsc: queryParams.get("dsc") ?? undefined
      }),
      [queryParams]
    );

  const doUpdateQueryParams = useCallback(
    (newParams: { [key: string]: string | undefined }) => {
      updateQueryParams(mergeQueryParams(queryParams, newParams));
    },
    [updateQueryParams, queryParams]
  );

  const flowColumns = getFlowColumns(d);

  const { sort, handleSetSort } = useTableSort<FlowExecution>({
    columns: flowColumns,
    queryParams,
    updateQueryParams: (newParams: { [key: string]: string | undefined }) => {
      doUpdateQueryParams({
        page: newParams.page,
        search: textFilter,
        status: statusFilter,
        asc: newParams.asc,
        dsc: newParams.dsc
      });
    }
  });

  const queryKey = useMemo(() => {
    return [queryParams.toString(), workspace.id];
  }, [queryParams, workspace.id]);

  const { data: flowExecutions } = useFlowExecutionsSearch(
    workspace,
    queryKey,
    buildQueryParamsForFlowExecutionSearch(
      textFilter,
      statusFilter,
      resultFilter,
      buildQueryParams(
        page ? Number(page) : undefined,
        pageSize ? Number(pageSize) : undefined,
        asc,
        dsc
      )
    )
  );

  const flowExecutionsData = useMemo(() => {
    const mappedItems = flowExecutions?.items?.map(fe => ({
      ...fe,
      runTime: calculateRunTime(fe),
      createdAt: fe.createdAt ? formatDate(fe.createdAt as string) : null
    }));

    if (flowExecutions?.items) {
      flowExecutions.items = mappedItems as FlowExecution[];
    }

    return flowExecutions;
  }, [flowExecutions]);

  const onChangePage = useCallback(
    (newPage: number) => {
      doUpdateQueryParams({ page: String(newPage) });
    },
    [doUpdateQueryParams]
  );

  const onChangeFlowExecutionSearchTerm = useCallback(
    (input: string) => {
      doUpdateQueryParams({
        search: input,
        page: "1" // reset page to 1 when changing filter criteria since the total count may change
      });
    },
    [doUpdateQueryParams]
  );

  const flowOptions = new WorkspaceConfigurationHelper(
    document
  ).listFlowsAsOptions();

  const onChangeSearchStatus = useCallback(
    (input: SelectValue | undefined) => {
      doUpdateQueryParams({
        status: input as string,
        page: "1" // reset page to 1 when changing filter criteria since the total count may change
      });
    },
    [doUpdateQueryParams]
  );

  const onChangeSearchResult = useCallback(
    (input: SelectValue | undefined) => {
      doUpdateQueryParams({
        result: input as string,
        page: "1" // reset page to 1 when changing filter criteria since the total count may change
      });
    },
    [doUpdateQueryParams]
  );

  // Start pending flow execution
  const queryClient = useQueryClient();
  const { mutate: startPendingFlow } = useMutation({
    mutationFn: (flowExecutionId: FlowExecution["id"]) => {
      return postData(
        `/workspaces/${workspace.id}/flowExecutions/${flowExecutionId}/execute`,
        {}
      );
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey
      });
    }
  });

  const { mutate: cancelFlow } = useMutation({
    mutationFn: (flowExecutionId: FlowExecution["id"]) => {
      return postData(
        `/workspaces/${workspace.id}/flowExecutions/${flowExecutionId}/cancel`,
        {}
      );
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey
      });
    }
  });

  const handleCancel = useCallback(
    (id: string) => {
      cancelFlow(id);
    },
    [cancelFlow]
  );

  const kebabMenu: TableKebabMenuFormatter<FlowExecution> = useCallback(
    ({ row, props, closeMenu }) => {
      const showCancel =
        row.status === FlowStatus.PENDING || row.status === FlowStatus.RUNNING;
      return (
        <KebabMenu {...props}>
          <DropdownItemGroup>
            {row.status === FlowStatus.PENDING && (
              <DropdownItem
                id="edit"
                onClick={() => {
                  closeMenu();
                  startPendingFlow(row.id);
                }}
                leftElement={<Icon name="play_arrow" fillStyle="filled" />}
              >
                {d("ui.flow.startExecutionButton")}
              </DropdownItem>
            )}
            {showCancel && (
              <DropdownItem
                id="cancel"
                onClick={() => {
                  closeMenu();
                  handleCancel(row.id);
                }}
                leftElement={<Icon name="stop" fillStyle="filled" />}
              >
                {d("ui.flow.cancelButton")}
              </DropdownItem>
            )}
            {row.documentId && (
              <DropdownItem
                id="edit"
                onClick={() => {
                  closeMenu();
                  const linkToDebugPage = getLinkToFEDDebugPage(
                    row.workspaceId,
                    row.id
                  );
                  window.open(linkToDebugPage, "_blank");
                }}
                leftElement={<Icon name="description" />}
              >
                {d("ui.common.viewAsDocument")}
              </DropdownItem>
            )}
            {document.flows?.entities?.[row.configurationId] && (
              <DropdownItem
                leftElement={<Icon {...commonIcons.flows} />}
                onClick={() => {
                  const currentActiveTab = `${pathname}${search}${hash}`;
                  updateLastActiveTabs({
                    ...lastActiveTabs,
                    [routeConstants.home]: currentActiveTab
                  } as UserLastActiveTabs);

                  navigate(
                    generatePath(routeConstants.configurationFlow, {
                      ...params,
                      configurationFlowId: row.configurationId
                    })
                  );
                }}
              >
                {d("ui.terminology.flowConfiguration")}
              </DropdownItem>
            )}
          </DropdownItemGroup>
        </KebabMenu>
      );
    },
    [
      d,
      document.flows?.entities,
      startPendingFlow,
      handleCancel,
      pathname,
      search,
      hash,
      updateLastActiveTabs,
      lastActiveTabs,
      navigate,
      params
    ]
  );

  const invalidateQuery = useCallback(async () => {
    await queryClient.invalidateQueries({
      queryKey
    });
  }, [queryClient, queryKey]);

  useEffect(() => {
    flowRunnerRefresh.current = invalidateQuery;
  }, [flowRunnerRefresh, invalidateQuery]);

  return (
    <PageBodyTemplate
      heading={d("ui.flow.title")}
      breadcrumbs={breadcrumbs}
      withoutScroll
      overlay={
        flowRunnerSelectorOpen ? (
          <FlowRunnerModal
            onOpenChange={() =>
              setFlowRunnerSelectorOpen(!flowRunnerSelectorOpen)
            }
            flowOptions={flowOptions}
          />
        ) : (
          <></>
        )
      }
    >
      <Inline gap="100" spaceBetween wrap>
        <Inline gap="100" alignment="left">
          <SearchBar
            withDebounce
            handleChange={onChangeFlowExecutionSearchTerm}
            placeholder={d("ui.common.search")}
            value={textFilter ?? ""}
            autoFocus
          />
          <PillSelect
            label={d("ui.flow.filters.status")}
            handleChange={onChangeSearchStatus}
            value={statusFilter ?? undefined}
            options={flowStatusOptions(d)}
          />
          <PillSelect
            label={d("ui.flow.filters.result")}
            handleChange={onChangeSearchResult}
            value={resultFilter ?? undefined}
            options={flowResultOptions(d)}
          />
        </Inline>
        {/*<Button*/} {/* TODO: Add in starting variables to run flow */}
        {/*  label={d("ui.flow.runButton")}*/}
        {/*  onClick={() => setFlowRunnerSelectorOpen(true)}*/}
        {/*/>*/}
      </Inline>
      <TableWithPagination
        isLoading={flowExecutionsData === undefined}
        fillContainer
        columns={flowColumns}
        data={flowExecutionsData?.items ?? []}
        itemsPerPage={flowExecutionsData?.page.pageSize}
        startingPage={page ? parseInt(page) : 1}
        totalCount={flowExecutionsData?.total ?? 0}
        noDataText={d("ui.flow.noData")}
        isControlledOutside={true}
        onChangePage={onChangePage}
        handleSort={handleSetSort}
        sort={sort}
        rowKeyAccessor="id"
        onRowClick={flow => {
          // Implement onRowClick
          logLinkToDebugPage(flow.documentId, "FlowExecution");
        }}
        kebabMenu={kebabMenu}
      />
    </PageBodyTemplate>
  );
};

function getFlowColumns(d: Dictionary): TableColumn<FlowExecution>[] {
  return [
    {
      header: "",
      key: "hasErrors",
      options: {
        width: "var(--spacing-000)",
        paddingLess: true
      },
      formatter: (value: FlowExecution) => {
        const statusLineVariant = statusLineGetVariant(value.status);
        return (
          <Inline className="flows-list-status-line-container">
            <StatusLine
              variant={statusLineVariant}
              className="flows-list-status-line"
            />
          </Inline>
        );
      }
    },
    {
      header: d("ui.flow.columns.name"),
      key: "flowConfigurationName",
      canSort: true
    },
    // TODO: add back when flow tags are implemented
    // {
    //   header: d("ui.flow.columns.tags"),
    //   key: "" // no tag property yet
    // },
    {
      header: d("ui.flow.columns.status"),
      key: "status",
      canSort: true,
      formatter: (data: FlowExecution) => (
        <Inline alignment="left" gap="100">
          <Pill
            variant={statusColumnGetVariant(data.status)}
            key={data.status}
            label={d(`ui.flow.status.${data.status}`)}
          />
        </Inline>
      )
    },
    {
      header: d("ui.flow.columns.result"),
      key: "result",
      canSort: true,
      formatter: (data: FlowExecution) => (
        <Inline alignment="left" gap="100">
          {data.result && (
            <Pill
              icon={data.result === FlowResult.PENDING ? "custom" : "standard"}
              customIcon={{ name: "pending", fillStyle: "filled" }}
              variant={resultColumnGetVariant(data.result)}
              key={data.result}
              label={d(`ui.flow.result.${data.result}`)}
            />
          )}
        </Inline>
      )
    },
    {
      header: d("ui.flow.columns.runTime"),
      key: "runTime",
      options: {
        color: ColorText.TERTIARY,
        size: TextSize.XS
      }
    },
    {
      header: d("ui.flow.columns.startTime"),
      key: "startedAt",
      canSort: true,
      formatter: (data: FlowExecution) => (
        <Inline alignment="left" gap="100">
          <Tooltip content={data.startedAt} delay={500}>
            <Text size="xs" color="text-tertiary">
              {data.startedAt ? formatDate(data.startedAt) : null}
            </Text>
          </Tooltip>
        </Inline>
      )
    },
    {
      header: d("ui.flow.columns.endTime"),
      key: "finishedAt",
      canSort: true,
      formatter: (data: FlowExecution) => (
        <Inline alignment="left" gap="100">
          <Tooltip content={data.startedAt} delay={500}>
            <Text size="s" color="text-tertiary">
              {data.finishedAt ? formatDate(data.finishedAt) : null}
            </Text>
          </Tooltip>
        </Inline>
      )
    },
    {
      header: d("ui.flow.columns.createdAt"),
      key: "createdAt",
      canSort: true,
      options: {
        color: ColorText.TERTIARY,
        size: TextSize.XS,
        alignment: TextAlignment.RIGHT
      }
    }
  ];
}
