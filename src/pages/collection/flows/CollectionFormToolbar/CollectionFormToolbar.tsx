import React from "react";

import {
  Box,
  DropdownItem,
  DropdownItemCheckbox,
  DropdownItemGroup,
  DropdownMenu,
  Icon,
  WhiteboardTool,
  WhiteboardToolVariant,
  WhiteboardToolbar
} from "@oneteam/onetheme";
import {
  generatePath,
  useLocation,
  useNavigate,
  useParams
} from "react-router-dom";
import useOnClickOutside from "use-onclickoutside";

import { getLinkToDebugPage } from "@helpers/debug";

import { useCollectionFormContext } from "@pages/collection/forms/CollectionFormContext";

import { UserLastActiveTabs } from "@src/authentication/AuthContext";
import { commonIcons } from "@src/constants/iconConstants";
import { routeConstants } from "@src/constants/routeConstants";
import { useAuth } from "@src/hooks/useAuth";
import { useDictionary } from "@src/hooks/useDictionary";

export const CollectionFormToolbar = () => {
  const navigate = useNavigate();
  const params = useParams();
  const { pathname, search, hash } = useLocation();
  const { lastActiveTabs, updateLastActiveTabs } = useAuth();

  const d = useDictionary();
  const { documentId, annotationDocumentId, formData, settings } =
    useCollectionFormContext();

  const [openItem, setOpenItem] = React.useState<string | undefined>(undefined);
  const ref = React.useRef(null);

  useOnClickOutside(ref, () => {
    setOpenItem(undefined);
  });
  return (
    <Box ref={ref}>
      <WhiteboardToolbar
        style={{
          padding: "var(--spacing-050, 4px)",
          gap: "var(--components-whiteboard-toolbar-gap, 8px)"
        }}
      >
        <DropdownMenu
          trigger={({ isOpen }) => (
            <WhiteboardTool
              variant={WhiteboardToolVariant.TEXT}
              text="File"
              isSelected={isOpen}
              onClick={() => {
                setOpenItem(current =>
                  current === "file" ? undefined : "file"
                );
              }}
            />
          )}
          isOpen={openItem === "file"}
        >
          <DropdownItemGroup title={d("ui.forms.view.title")} hasDivider>
            {documentId && (
              <DropdownItem
                id="edit"
                onClick={() => {
                  setOpenItem(undefined);
                  const linkToDebugPage = getLinkToDebugPage(documentId);
                  window.open(linkToDebugPage, "_blank");
                }}
                leftElement={<Icon name="description" />}
              >
                {d("ui.forms.view.answersDocument")}
              </DropdownItem>
            )}
            {annotationDocumentId && (
              <DropdownItem
                id="edit"
                onClick={() => {
                  setOpenItem(undefined);
                  const linkToDebugPage =
                    getLinkToDebugPage(annotationDocumentId);
                  window.open(linkToDebugPage, "_blank");
                }}
                leftElement={<Icon name="draw" />}
              >
                {d("ui.forms.view.annotationsDocument")}
              </DropdownItem>
            )}
          </DropdownItemGroup>
        </DropdownMenu>
        <DropdownMenu
          trigger={({ isOpen }) => (
            <WhiteboardTool
              variant={WhiteboardToolVariant.TEXT}
              text="Edit"
              isSelected={isOpen}
              onClick={() => {
                setOpenItem(current =>
                  current === "edit" ? undefined : "edit"
                );
              }}
            />
          )}
          isOpen={openItem === "edit"}
        >
          {formData?.configuration.key && (
            <DropdownItemGroup>
              <DropdownItem
                leftElement={<Icon {...commonIcons.forms} />}
                onClick={() => {
                  const currentActiveTab = `${pathname}${search}${hash}`;
                  updateLastActiveTabs({
                    ...lastActiveTabs,
                    [routeConstants.home]: currentActiveTab
                  } as UserLastActiveTabs);

                  navigate(
                    generatePath(routeConstants.configurationForm, {
                      ...params,
                      configurationFormKey: formData?.configuration.key
                    })
                  );
                }}
              >
                {d("ui.terminology.formConfiguration")}
              </DropdownItem>
            </DropdownItemGroup>
          )}
        </DropdownMenu>
        <DropdownMenu
          trigger={({ isOpen }) => (
            <WhiteboardTool
              variant={WhiteboardToolVariant.TEXT}
              text="View"
              isSelected={isOpen}
              onClick={() => {
                setOpenItem(current =>
                  current === "view" ? undefined : "view"
                );
              }}
            />
          )}
          isOpen={openItem === "view"}
          width="maxContent"
        >
          <DropdownItemGroup>
            <DropdownItemCheckbox
              id="cursors"
              isChecked={!!settings?.liveCursorsVisible}
              onChange={updated => {
                settings?.setLiveCursorsVisible(updated);
              }}
            >
              {d("ui.forms.view.collaboratorsCursors")}
            </DropdownItemCheckbox>
          </DropdownItemGroup>
          {/* <DropdownItemGroup>
            <DropdownItemCheckbox
              isChecked={settings?.debugMode ?? false}
              onChange={updated => handleUpdateSetting("debugMode", updated)}
            >
              <Box width="maxContent">Debug mode</Box>
            </DropdownItemCheckbox>
          </DropdownItemGroup> */}
        </DropdownMenu>
      </WhiteboardToolbar>
    </Box>
  );
};
