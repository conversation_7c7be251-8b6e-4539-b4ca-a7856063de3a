import React, { useMemo } from "react";

import {
  ColorOption,
  ColorText,
  ColorTraffic,
  Icon,
  IconColor,
  Inline,
  Pill,
  PillVariant,
  Text
} from "@oneteam/onetheme";

enum PropertyValueFormatVariant {
  ICON = "icon",
  PILL = "pill",
  TEXT = "text"
}

enum PropertyValuePillColor {
  NEUTRAL = "neutral",
  OUTLINED = "outlined",
  SUCCESS = "success",
  WARNING = "warning",
  DANGER = "danger",
  INFO = "info",
  // Purple (or main color of the app)
  COLOR = "color"
}

enum PropertyValueColor {
  SUCCESS = "success",
  WARNING = "warning",
  DANGER = "danger",
  INFO = "info",
  SECONDARY = "secondary",
  PRIMARY = "primary",
  DISABLED = "disabled",
  TEXT_PRIMARY = "text-primary",
  TEXT_SECONDARY = "text-secondary",
  // Purple (or main color of the app)
  COLOR = "color"
}

// This component renders a property value.
// It can handle special syntax for displaying components such as icons, pills, and text.

// Syntax examples
// ":icon<check_circle>::danger: Failed"
// "":icon<add>:"
// "Assigned to :pill<<PERSON>>::color: :icon<check_circle>::danger:"
// ":pill<<PERSON>>::neutral:"
// ":text<Succeeded>::success:"
// ":text<Failed>::danger:"
// ":text<In Progress>:"

export const PropertyValueRenderer = ({ value }: { value?: string }) => {
  const splitValues = useMemo(() => getSplitValues(value), [value]);

  return (
    <Inline gap="025" alignment="left" wrap>
      {splitValues.map((rawValue, index) => (
        <SingleValueRenderer rawValue={rawValue} index={index} key={index} />
      ))}
    </Inline>
  );
};

const SingleValueRenderer = ({
  rawValue,
  index
}: {
  index: number;
  rawValue: string;
}) => {
  if (!rawValue.startsWith(":") || !rawValue.endsWith(":")) {
    return <Text key={index}>{rawValue}</Text>;
  }
  const { variant, content, display } = extractDetailsFromRawValue(rawValue);

  if (variant === PropertyValueFormatVariant.ICON) {
    // :icon<check_circle>::success:
    return (
      <Icon
        key={index}
        name={content}
        size="s"
        className="icon"
        color={stringColorToRecognizedIconColor(display)}
        fillStyle="filled"
        sizeOverride={18}
      />
    );
  } else if (variant === PropertyValueFormatVariant.PILL) {
    // :pill<John Smith>::success:
    return (
      <Pill
        variant={pillColorToRecognizedPillVariant(display)}
        key={index}
        label={content}
      />
    );
  } else if (variant === PropertyValueFormatVariant.TEXT) {
    // :text<Succeeded>::success:
    return (
      <Text color={stringColorToRecognizedIconColor(display)} key={index}>
        {content}
      </Text>
    );
  }
  return <Text key={index}>{rawValue}</Text>;
};

const getSplitValues = (value?: string) => {
  const rawValuesToRender = ((): string[] => {
    if (!value) {
      return [];
    }
    // Find all the words inside : and : and ignore ::

    // We assume the format is :word: or :word<details>: or :icon<icon_name>::color:
    // We will use a regex to find all occurrences of :word: or :word<details>:
    // or :icon<icon_name>::color: (keeping the : chars)
    const regex = /:([^:]+?)(?:<[^>]+>)?::?([^:]+)?:/g;
    const matches = value.match(regex);
    if (!matches) {
      return [];
    }

    return matches;
  })();

  if (!rawValuesToRender?.length) {
    return [value ?? ""];
  }

  const result: string[] = [];
  let remainingValue = String(value ?? "");

  rawValuesToRender.forEach(rawValue => {
    // Find the first occurrence of the valueToFormat and get the part before it
    const startIndex = remainingValue.indexOf(rawValue);
    if (startIndex !== -1) {
      // Add the part before the valueToFormat
      if (startIndex > 0) {
        result.push(remainingValue.slice(0, startIndex - 1));
      }
      // Add the valueToFormat itself
      result.push(rawValue);
      // Update remainingValue to the part after the valueToFormat
      remainingValue = remainingValue.slice(startIndex + rawValue.length);
    }
  });
  if (remainingValue) {
    result.push(remainingValue);
  }
  return result;
};

const extractDetailsFromRawValue = (
  rawValue: string
): {
  variant?: `${PropertyValueFormatVariant}`;
  content: string;
  display?: string;
} => {
  const value = rawValue.slice(1, -1);

  const [info, displayPreference] = value.split("::");
  const variant = (() => {
    for (const v of Object.values(PropertyValueFormatVariant)) {
      if (info.startsWith(v)) {
        return v;
      }
    }
  })();

  if (!variant) {
    return {
      variant: undefined,
      content: value,
      display: undefined
    };
  }

  const content = info
    .replace(variant, "")
    .replace(/<([^>]+)>/, "$1")
    .trim();
  return {
    variant,
    content: content,
    display: displayPreference
  };
};

const stringColorToRecognizedIconColor = (color = ""): IconColor => {
  switch (color) {
    case PropertyValueColor.SUCCESS:
      return ColorTraffic.SUCCESS;
    case PropertyValueColor.WARNING:
      return ColorTraffic.WARNING;
    case PropertyValueColor.DANGER:
      return ColorTraffic.DANGER;
    case PropertyValueColor.INFO:
      return ColorTraffic.INFO;
    case PropertyValueColor.COLOR:
      return ColorOption.COLOR;
    case PropertyValueColor.SECONDARY:
      return ColorOption.SECONDARY;
    case PropertyValueColor.PRIMARY:
      return ColorOption.PRIMARY;
    case PropertyValueColor.DISABLED:
      return ColorText.DISABLED;
    case PropertyValueColor.TEXT_PRIMARY:
      return ColorText.PRIMARY;
    case PropertyValueColor.TEXT_SECONDARY:
      return ColorText.SECONDARY;
    default:
      return ColorText.PRIMARY;
  }
};

const pillColorToRecognizedPillVariant = (color = ""): PillVariant => {
  switch (color) {
    case PropertyValuePillColor.SUCCESS:
      return PillVariant.SUCCESS;
    case PropertyValuePillColor.WARNING:
      return PillVariant.WARNING;
    case PropertyValuePillColor.DANGER:
      return PillVariant.DANGER;
    case PropertyValuePillColor.INFO:
      return PillVariant.INFO;
    case PropertyValuePillColor.COLOR:
      return PillVariant.COLORED;
    case PropertyValuePillColor.OUTLINED:
      return PillVariant.OUTLINED;
    case PropertyValuePillColor.NEUTRAL:
    default:
      return PillVariant.NEUTRAL;
  }
};
