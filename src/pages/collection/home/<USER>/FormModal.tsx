import React, { useEffect, useMemo, useState } from "react";

import {
  Box,
  Floating,
  FloatingPosition,
  Form,
  Modal,
  Overlay,
  SelectOptionType,
  Stack
} from "@oneteam/onetheme";
import { useMutation, useQueryClient } from "@tanstack/react-query";

import { CustomError } from "@helpers/errorHelper.ts";
import { modalZIndex } from "@helpers/modalHelper.ts";
import { postData } from "@helpers/postData.ts";
import { WorkspaceConfigurationHelper } from "@helpers/workspace/WorkspaceConfigurationHelper.ts";

import { OTAIFormField } from "@components/shared/OTAIForm/OTAIFormField.tsx";

import { ServerError } from "@src/ServerError.tsx";
import { useConfigurationOptions } from "@src/hooks/formConfiguration/select/useConfigurationOptions";
import { Dictionary, useDictionary } from "@src/hooks/useDictionary.tsx";
import { ExistingForm, NewForm, newFormSchema } from "@src/types/Form.ts";
import { Foundation } from "@src/types/Foundation.ts";
import { FoundationConfiguration } from "@src/types/FoundationConfiguration.ts";
import { Page } from "@src/types/Page";
import { Question, QuestionTypes } from "@src/types/Question.ts";
import {
  DynamicOptionTags,
  SelectQuestionProperties
} from "@src/types/QuestionProperties.ts";
import { Workspace } from "@src/types/workspace.ts";

import "./FormModal.scss";

interface FormModalFieldsProps {
  d: Dictionary;
  foundationsSelect: Question<SelectQuestionProperties>;
  foundationConfiguration: FoundationConfiguration;
  parentFoundationConfiguration?: FoundationConfiguration;
  workspaceConfigurationHelper: WorkspaceConfigurationHelper;
  existingForms?: Page<ExistingForm>;
  foundation: Foundation;
}

export const FormModalFields = ({
  d,
  foundationsSelect,
  foundationConfiguration,
  parentFoundationConfiguration,
  workspaceConfigurationHelper,
  existingForms,
  foundation
}: FormModalFieldsProps) => {
  const [formConfigurationId, setFormConfigurationId] = useState<string>();
  const [seriesId, setSeriesId] = useState<string>();

  const formConfigSelect: Question<SelectQuestionProperties> = useMemo(() => {
    const result: Question<SelectQuestionProperties> = {
      description: "",
      id: "formConfigurationId",
      identifier: "formConfigurationIdSelect",
      properties: {
        allowReuseAcrossForms: false,
        placeholder: "",
        required: true,
        dynamicOptions: {
          tag: DynamicOptionTags.FORM_CONFIGURATION_ID,
          body: {
            parentFoundationConfigurationId: foundationConfiguration.id
          }
        }
      },
      text: d("ui.terminology.form"),
      type: QuestionTypes.SELECT
    };
    return result;
  }, [d, foundationConfiguration.id]);

  const intervalSelect: Question<SelectQuestionProperties> = useMemo(() => {
    const result: Question<SelectQuestionProperties> = {
      description: "",
      id: "intervalId",
      identifier: "intervalIdSelect",
      properties: {
        allowReuseAcrossForms: false,
        placeholder: "",
        required: true,
        dynamicOptions: {
          tag: DynamicOptionTags.FORM_CONFIGURATION_SERIES_INTERVAL_ID,
          body: {
            formConfigurationId: formConfigurationId
          }
        }
      },
      text: d("ui.terminology.interval"),
      type: QuestionTypes.SELECT
    };
    return result;
  }, [d, formConfigurationId]);

  const formConfigDynamicOptions = useConfigurationOptions(
    formConfigSelect.properties?.dynamicOptions,
    workspaceConfigurationHelper.config
  );

  const filteredIntervalsSelectOptionsMap = useMemo(() => {
    const foundationId = foundation.id;
    const map: { [key: string]: SelectOptionType[] } = {};

    formConfigDynamicOptions?.options?.forEach(option => {
      const intervalOptions =
        workspaceConfigurationHelper.listFormIntervalsAsOptions(
          option.value.toString()
        );

      // Skip if no intervals
      if (intervalOptions.length === 0) {
        return;
      }

      // Get exiting intervals for this foundation+form combination
      const existingIntervals = existingForms?.items
        ?.filter(
          form =>
            form.foundationId === foundationId &&
            form.formConfigurationId === option.value
        )
        .map(form => form.intervalId);

      // Store unused intervals
      const mapKey = `${foundationId}-${option.value}`;
      map[mapKey] = intervalOptions.filter(
        interval => !existingIntervals?.includes(interval.value as string)
      );
    });

    return map;
  }, [
    foundation.id,
    formConfigDynamicOptions?.options,
    existingForms,
    workspaceConfigurationHelper
  ]);

  const filteredIntervalsSelect = useMemo(() => {
    const filteredIntervalsSelectObj = {
      ...intervalSelect,
      properties: {
        ...intervalSelect.properties,
        options:
          filteredIntervalsSelectOptionsMap[
            `${foundation.id}-${formConfigurationId}`
          ]
      }
    };
    delete filteredIntervalsSelectObj?.properties?.dynamicOptions;
    return filteredIntervalsSelectObj;
  }, [
    foundation.id,
    formConfigurationId,
    filteredIntervalsSelectOptionsMap,
    intervalSelect
  ]);

  const filteredFormConfigSelect = useMemo(() => {
    const foundationId = foundation.id;
    const filteredOptions = formConfigDynamicOptions?.options?.filter(
      option => {
        return !existingForms?.items?.some(item => {
          return (
            item.foundationId === foundationId &&
            item.formConfigurationId === option.value &&
            (filteredIntervalsSelectOptionsMap[
              `${foundationId}-${option.value}`
            ] === undefined ||
              filteredIntervalsSelectOptionsMap[
                `${foundationId}-${option.value}`
              ]?.length === 0)
          );
        });
      }
    );
    const filteredFormConfigSelectObj = {
      ...formConfigSelect,
      properties: {
        ...formConfigSelect.properties,
        options: filteredOptions
      }
    };
    delete filteredFormConfigSelectObj?.properties?.dynamicOptions;
    return filteredFormConfigSelectObj;
  }, [
    foundation.id,
    formConfigSelect,
    existingForms,
    formConfigDynamicOptions,
    filteredIntervalsSelectOptionsMap
  ]);

  /**
   * Setting the options for the form configuration select
   * and deleting the dynamicOptions property
   * because we want to use the published workspace document {@link workspaceConfigurationHelper.config}
   * @see {@link useConfigurationOptions}
   */
  useEffect(() => {
    if (
      formConfigDynamicOptions &&
      !formConfigDynamicOptions.isFetching &&
      formConfigSelect.properties
    ) {
      formConfigSelect.properties.options = formConfigDynamicOptions.options;
    }
  }, [formConfigDynamicOptions, formConfigSelect.properties]);

  return (
    <Stack gap="150" width="100" contentsWidth="100">
      {parentFoundationConfiguration && (
        <OTAIFormField question={foundationsSelect} id={"foundationId"} />
      )}
      <OTAIFormField
        question={filteredFormConfigSelect}
        id={"formConfigurationId"}
        onChange={formConfigurationId => {
          if (!formConfigurationId) {
            return;
          }

          const seriesId = workspaceConfigurationHelper.getForm(
            formConfigurationId.toString()
          )?.seriesId;
          setFormConfigurationId(formConfigurationId.toString());

          setSeriesId(seriesId);
        }}
      />

      {seriesId && (
        <OTAIFormField question={filteredIntervalsSelect} id={"intervalId"} />
      )}
    </Stack>
  );
};

interface FormModalProps {
  onOpenChange: () => void;
  fetchQueryKey: string;
  form: ExistingForm;
  workspace: Workspace;
  currentFoundation: Foundation;
  foundationConfiguration: FoundationConfiguration;
  parentFoundationConfiguration?: FoundationConfiguration;
  workspaceConfigurationHelper: WorkspaceConfigurationHelper;
  invalidateKey: string;
  isWorkspaceFormModal?: boolean;
  existingForms?: Page<ExistingForm>;
}

export const FormModal = ({
  onOpenChange,
  fetchQueryKey,
  form,
  workspace,
  currentFoundation,
  foundationConfiguration,
  parentFoundationConfiguration,
  workspaceConfigurationHelper,
  invalidateKey,
  isWorkspaceFormModal,
  existingForms
}: FormModalProps) => {
  const [serverError, setServerError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const d = useDictionary();

  const queryClient = useQueryClient();

  const { mutate: saveForm } = useMutation<NewForm, CustomError, NewForm>({
    mutationFn: (form: NewForm) => {
      setIsSubmitting(true);
      return postData(`/workspaces/${workspace.id}/forms`, form);
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: [fetchQueryKey] });
      await queryClient.invalidateQueries({ queryKey: [invalidateKey] });
      onOpenChange();
      setIsSubmitting(false);
    },
    onError: (error: CustomError) => {
      setServerError(error.key);
      setIsSubmitting(false);
    }
  });

  const foundationsSelect: Question<SelectQuestionProperties> = useMemo(() => {
    let optionsCombined: Partial<Question<SelectQuestionProperties>>;
    const foundationConfigurationName = foundationConfiguration?.name ?? "";
    if (!isWorkspaceFormModal) {
      optionsCombined = {
        properties: {
          dynamicOptions: {
            tag: DynamicOptionTags.FOUNDATION_COLLECTION_ID,
            body: {
              parentFoundationConfigurationId:
                currentFoundation.foundationConfigurationId,
              parentFoundationKey: currentFoundation.key
            }
          }
        },
        text: foundationConfigurationName ?? ""
      };
    } else {
      optionsCombined = {
        properties: {
          options: [
            {
              value: currentFoundation.id,
              label: currentFoundation.name
            }
          ],
          defaultValue: currentFoundation.id
        },
        text: foundationConfigurationName ?? ""
      };
    }

    const result: Question<SelectQuestionProperties> = {
      description: "",
      id: "foundationId",
      identifier: "foundationIdSelect",
      properties: {
        allowReuseAcrossForms: false,
        placeholder: "",
        required: true,
        ...(optionsCombined?.properties as SelectQuestionProperties)
      },
      text: optionsCombined?.text ?? "",
      type: QuestionTypes.SELECT
    };
    return result;
  }, [
    foundationConfiguration?.name,
    isWorkspaceFormModal,
    currentFoundation.foundationConfigurationId,
    currentFoundation.key,
    currentFoundation.id,
    currentFoundation.name
  ]);

  return (
    <Box style={{ zIndex: modalZIndex }}>
      <Overlay isOpen></Overlay>
      <Floating position={FloatingPosition.CENTER}>
        <Modal
          className="form-modal"
          onOpenChange={onOpenChange}
          heading={d("ui.collection.formModal.createTitle", {
            foundation: foundationConfiguration.name
          })}
        >
          <Form
            schema={newFormSchema(d)}
            submitLabel={d("ui.common.save")}
            handleCancel={onOpenChange}
            d={d}
            handleSubmit={form => {
              saveForm(form);
            }}
            defaultValues={form}
            disabled={isSubmitting}
          >
            <FormModalFields
              d={d}
              foundationsSelect={foundationsSelect}
              workspaceConfigurationHelper={workspaceConfigurationHelper}
              foundation={currentFoundation}
              foundationConfiguration={foundationConfiguration}
              parentFoundationConfiguration={parentFoundationConfiguration}
              existingForms={existingForms}
            />
          </Form>
          <ServerError key={serverError} />
        </Modal>
      </Floating>
    </Box>
  );
};

FormModal.displayName = "FormModal";
