import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";
import range from "lodash/range";
import { Params, generatePath } from "react-router-dom";

import { getData } from "@helpers/getData.ts";

import { routeConstants } from "@src/constants/routeConstants";
import { ExistingForm } from "@src/types/Form.ts";
import { Foundation } from "@src/types/Foundation.ts";
import { Page } from "@src/types/Page.ts";
import { WorkspaceVersion } from "@src/types/WorkspaceVersion.ts";
import { Workspace } from "@src/types/workspace.ts";

export enum ModalState {
  CLOSED,
  CREATE,
  EDIT
}
export class HomeHelper {
  readonly params: Params<string>;
  readonly level: number;
  readonly workspace: Workspace;
  readonly levelListForPath: number[];

  constructor(params: Params<string>, level: number, workspace: Workspace) {
    this.params = params;
    this.workspace = workspace;
    this.level = level;
    // Skip the root level
    this.levelListForPath = range(1, this.level + 1);
  }

  getLevelKeyAccessor(level = this.level): string {
    if (level === 0) {
      return "workspaceKey";
    }
    return `level${level}Key`;
  }

  buildRawPath(toLevel = this.level): string {
    if (toLevel === 0) {
      return routeConstants.collectionHome;
    }
    const levelsAsPath = this.levelListForPath
      .filter(level => (toLevel ? level <= toLevel : true))
      .map(level => `/:${this.getLevelKeyAccessor(level)}`)
      .join("");
    return `${routeConstants.collectionHome}${levelsAsPath}`;
  }

  buildPath(toLevel = this.level): string {
    try {
      return generatePath(this.buildRawPath(toLevel), this.params);
    } catch {
      return generatePath(this.buildRawPath(0), this.params); // Fallback to raw path generation
    }
  }

  hierarchicalFoundationPath(hierarchy: string): string {
    return generatePath(this.buildRawPath(0) + `/${hierarchy}`, this.params);
  }

  nextPath(key: string): string {
    const nextLevelKeyAccessor = this.getLevelKeyAccessor(this.level + 1);
    return generatePath(this.buildRawPath() + `/:${nextLevelKeyAccessor}`, {
      ...this.params,
      [nextLevelKeyAccessor]: key
    });
  }

  getSelectedFoundationKey(): string | undefined {
    const levelKeyAccessor = this.getLevelKeyAccessor(this.level);
    if (this.params[levelKeyAccessor]) {
      return this.params[levelKeyAccessor];
    }
    // NOTE: root foundation key = workspace key by design/contract
    return this.workspace.key ?? undefined;
  }

  getBreadcrumbs(
    { allInactive } = {
      allInactive: false
    }
  ): { href: string; text: string }[] {
    const workspaceLevel = {
      href: this.buildPath(0),
      text: this.workspace.key,
      isActive: allInactive ? false : this.level === 0
    };

    if (this.level === 0) {
      return [workspaceLevel];
    }

    const levelMap = this.levelListForPath.map(level => {
      const key = this.params[this.getLevelKeyAccessor(level)];
      return {
        href: this.buildPath(level),
        text: key ?? String(level),
        isActive: allInactive ? false : level === this.level
      };
    });
    return [workspaceLevel, ...levelMap];
  }
}

export const useRootFoundation = (workspace: Workspace) => {
  const path = `/workspaces/${workspace.id}/foundations/root`;
  return useQuery<Foundation>({
    queryKey: [path],
    queryFn: async (): Promise<Foundation> => {
      return getData(path);
    }
  });
};

export const useWorkspaceVersion = (workspace: Workspace) => {
  const path = `/workspaces/${workspace?.id}/configuration/versions?sort=id,desc&pageSize=1`;
  const query = useQuery<Page<WorkspaceVersion>>({
    queryKey: [path],
    queryFn: async (): Promise<Page<WorkspaceVersion>> => {
      return getData(path);
    }
  });

  const lastPublishedVersion = useMemo(
    () => query.data?.items?.[0],
    [query.data?.items]
  );

  return {
    ...query,
    lastPublishedVersion
  };
};

export const useFoundationByKeyAndConfigurationId = (
  workspaceId: Workspace["id"],
  key: string | undefined,
  configurationId: string | undefined
) => {
  const path = `/workspaces/${workspaceId}/foundations/key/${key}/${configurationId}`;
  return useQuery<Foundation>({
    queryKey: [path],
    queryFn: (): Promise<Foundation> => {
      return getData(path);
    },
    enabled: !!configurationId
  });
};

export const useFoundationsSearch = (
  workspaceId: Workspace["id"],
  queryKey: Array<string | number>,
  params: URLSearchParams
) => {
  return useQuery<Page<Foundation>>({
    queryKey: queryKey,
    queryFn: (): Promise<Page<Foundation>> => {
      return getData(`/workspaces/${workspaceId}/foundations`, params);
    }
  });
};

export const useFormSearch = (
  workspaceId: Workspace["id"],
  queryKey: Array<string | number>,
  params: URLSearchParams
) => {
  return useQuery<Page<ExistingForm>>({
    queryKey: queryKey,
    queryFn: (): Promise<Page<ExistingForm>> => {
      return getData(`/workspaces/${workspaceId}/forms`, params);
    }
  });
};

export const useFindForm = (
  formId?: number,
  options: {
    skip?: boolean;
  } = {
    skip: false
  }
) => {
  return useQuery<ExistingForm>({
    queryKey: [`/forms/${formId}`],
    queryFn: (): Promise<ExistingForm> => {
      return getData(`/forms/${formId}`);
    },
    enabled: !!formId && !options.skip
  });
};
