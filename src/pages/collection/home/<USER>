import { TabGroupOption } from "@oneteam/onetheme";

import { Dictionary } from "@src/hooks/useDictionary.tsx";
import { FoundationConfiguration } from "@src/types/FoundationConfiguration.ts";

/**
 * Tabs are related to the selected foundation level, and the associated child foundation level.
 */
export class CollectionsFoundationsTabs {
  static readonly CHILD_FOUNDATIONS = "foundations"; // show foundations for the child level
  static readonly CHILD_FORMS = "forms"; // show forms for the child level
  static readonly SELECTED_FORMS = "selectedForms"; // show forms for the selected level

  readonly allTabs: string[] = [];

  constructor(
    private readonly d: Dictionary,
    private readonly totals: CollectionFoundationsTabTotals,
    private readonly selectedFoundationConfiguration: FoundationConfiguration,
    private readonly nextFoundationConfiguration?: FoundationConfiguration
  ) {
    // can only see list of child foundations if we aren't at the last level
    if (nextFoundationConfiguration) {
      this.allTabs.push(CollectionsFoundationsTabs.CHILD_FOUNDATIONS);
      // forms at child foundation level
      this.allTabs.push(CollectionsFoundationsTabs.CHILD_FORMS);
    }
    // forms at selected foundation level
    this.allTabs.push(CollectionsFoundationsTabs.SELECTED_FORMS);
  }

  /**
   * Returns the current tab if it is in the list of all tabs, otherwise returns the first tab.
   * This can be used to clean up the query params in case it's not valid.
   * @param tab the tab specified by the query params
   */
  currentTab(tab: string): string {
    return this.allTabs.indexOf(tab) !== -1 ? tab : this.allTabs[0];
  }

  tabs(): TabGroupOption[] {
    const tabs = [];
    if (this.nextFoundationConfiguration) {
      // child foundations
      tabs.push({
        label: this.d("ui.collection.browse.tabs.foundations", {
          foundationConfigurationName: this.nextFoundationConfiguration?.name
        }),
        value: CollectionsFoundationsTabs.CHILD_FOUNDATIONS,
        pill: {
          label: `${this.totals.childFoundations}`
        }
      });
      // child forms
      tabs.push({
        label: this.d("ui.collection.browse.tabs.forms", {
          foundationConfigurationName: this.nextFoundationConfiguration?.name
        }),
        value: "forms",
        pill: {
          label: `${this.totals.childForms}`
        }
      });
    }
    tabs.push({
      label: this.d("ui.collection.browse.tabs.forms", {
        foundationConfigurationName: this.selectedFoundationConfiguration?.name
      }),
      value: "selectedForms",
      pill: {
        label: `${this.totals.selectedForms}`
      }
    });
    return tabs;
  }

  /**
   * Returns the parameter name we should be using for the server query based on the tab.
   */
  static getParameterForFormTab(tab: string): string {
    switch (tab) {
      case CollectionsFoundationsTabs.CHILD_FORMS:
        return "foundationParent";
      case CollectionsFoundationsTabs.SELECTED_FORMS:
        return "foundation";
      default:
        return "foundation";
    }
  }

  static getUrlParamsForFormCount(
    tab: string,
    foundationId: number
  ): URLSearchParams {
    const params = new URLSearchParams();
    params.set(
      CollectionsFoundationsTabs.getParameterForFormTab(tab),
      `${foundationId}`
    );
    return params;
  }

  static buildParamsForAllFormSearch(
    tab: string,
    foundationId: number
  ): URLSearchParams {
    const params = new URLSearchParams();
    params.set(
      CollectionsFoundationsTabs.getParameterForFormTab(tab),
      `${foundationId}`
    );
    //Hardcoded for now to get the Max_Page_Size forms
    params.set("pageSize", "1000");
    return params;
  }

  static getUrlParamsForFormSearch(
    tab: string,
    foundationId: number,
    textFilter: string | undefined,
    pagination: URLSearchParams
  ): URLSearchParams {
    const params = CollectionsFoundationsTabs.getUrlParamsForFormCount(
      tab,
      foundationId
    );
    params.set("search", textFilter ?? "");
    pagination.forEach((value, key) => {
      params.append(key, value);
    });
    return params;
  }

  static buildQueryParamsForFoundationSearch(
    parentFoundationKey: string,
    parentFoundationConfigurationId: string,
    textFilter: string | undefined,
    pagination: URLSearchParams
  ): URLSearchParams {
    const params = new URLSearchParams();
    params.set("parentKey", parentFoundationKey);
    params.set("parentConfigurationId", parentFoundationConfigurationId);
    params.set("search", textFilter ?? "");
    pagination.forEach((value, key) => {
      params.append(key, value);
    });
    return params;
  }

  static buildQueryParamsForFoundationCount(
    foundationKey: string,
    foundationConfigurationId: string
  ): URLSearchParams {
    const params = new URLSearchParams();
    params.set("parentKey", foundationKey);
    params.set("parentConfigurationId", foundationConfigurationId);
    return params;
  }
}

export type CollectionFoundationsTabTotals = {
  childFoundations: number;
  childForms: number;
  selectedForms: number;
};
