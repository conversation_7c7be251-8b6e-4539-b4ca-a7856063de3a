import React, { useMemo } from "react";

import { Box, Heading, Loading } from "@oneteam/onetheme";
import { generatePath, useNavigate, useOutletContext } from "react-router-dom";

import { WorkspaceConfigurationHelper } from "@helpers/workspace/WorkspaceConfigurationHelper.ts";

import { HomeContent } from "@pages/collection/home/<USER>";
import {
  useFoundationByKeyAndConfigurationId,
  useRootFoundation,
  useWorkspaceVersion
} from "@pages/collection/home/<USER>";

import { Workspace } from "@src/types/workspace.ts";

import { useCollectionHomeContext } from "./CollectionHomeContext";

export const Home = () => {
  const navigate = useNavigate();

  const { level, homeHelper } = useCollectionHomeContext();
  const { workspace } = useOutletContext<{ workspace: Workspace }>();

  // to use as default selected foundation if needed
  const { data: rootFoundation, isLoading: isLoadingRootFoundation } =
    useRootFoundation(workspace);

  // get the selected foundation
  const selectedFoundationKey = homeHelper?.getSelectedFoundationKey();

  // to get the latest configuration version for the workspace
  const {
    data: workspaceConfiguration,
    isLoading: isLoadingWorkspaceConfiguration
  } = useWorkspaceVersion(workspace);

  // get the selected foundation Configuration Id
  const selectedFoundationConfigurationId = useMemo(() => {
    if (!workspaceConfiguration) {
      return;
    }

    if (level === 0) {
      return rootFoundation?.foundationConfigurationId;
    }

    const orderedFoundationConfigurationIds =
      workspaceConfiguration?.items[0]?.configuration?.foundations?.order;

    if (!orderedFoundationConfigurationIds?.length) {
      return;
    }

    // we can determine the foundation configuration id by the order of the foundations
    //   and the current length of paths (what level we are on)
    return orderedFoundationConfigurationIds[level];
  }, [
    workspaceConfiguration,
    level,
    rootFoundation?.foundationConfigurationId
  ]);

  const {
    data: currentFoundation,
    isLoading: isLoadingFoundation,
    isError: isErrorFoundation
  } = useFoundationByKeyAndConfigurationId(
    workspace.id,
    selectedFoundationKey,
    selectedFoundationConfigurationId
  );

  // if the selected foundation is not found, redirect to the root foundation
  if (homeHelper && rootFoundation && isErrorFoundation) {
    navigate(generatePath(homeHelper.buildPath(0)), { replace: true });
  }

  if (
    isLoadingRootFoundation ||
    isLoadingFoundation ||
    isLoadingWorkspaceConfiguration ||
    !workspaceConfiguration
  ) {
    return (
      <Box height="100" contentsHeight="fill">
        <Loading size={24} />
      </Box>
    );
  }

  if (workspaceConfiguration && workspaceConfiguration.items.length === 0) {
    return (
      <Box width="100" height="100" padding="200">
        <Heading
          size="s"
          truncate
          maxLines={4}
          color="text-secondary"
          weight="regular"
        >
          No published version of workspace configuration for collection to use.
          Please publish first.
        </Heading>
      </Box>
    );
  }

  return (
    <HomeContent
      workspaceVersion={
        new WorkspaceConfigurationHelper(
          workspaceConfiguration.items[0]?.configuration
        )
      }
      foundation={currentFoundation!}
      workspace={workspace}
      homeHelper={homeHelper}
    />
  );
};

Home.displayName = "Home";
