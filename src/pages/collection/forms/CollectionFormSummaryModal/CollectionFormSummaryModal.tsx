import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from "react";

import {
  Box,
  Breadcrumbs,
  BreadcrumbsItem,
  Divider,
  DropdownItemCheckbox,
  DropdownItemGroup,
  DropdownMenu,
  Icon,
  IconButton,
  Inline,
  SearchBar,
  Stack,
  TabGroup,
  Toggle
} from "@oneteam/onetheme";

import { FloatingModal } from "@components/shared/FloatingModal/FloatingModal.tsx";

import { AnnotationDisplay } from "@pages/collection/forms/annotations/AnnotationDisplay/AnnotationDisplay";
import { AnnotationMessageField } from "@pages/collection/forms/annotations/AnnotationMessageField/AnnotationMessageField";

import { useAuth } from "@src/hooks/useAuth";
import { useDictionary } from "@src/hooks/useDictionary";
import {
  AlertType,
  AnnotationResolvedStatus,
  AnnotationVariant
} from "@src/types/Annotation";
import {
  AnswerLocation,
  JsonOrListItemLocation,
  QuestionLocation,
  SectionLocation,
  TableCellLocation
} from "@src/types/AnnotationLocation";
import { CollectionFormSummaryModalTab } from "@src/types/collection/CollectionForm";

import { useCollectionFormContext } from "../CollectionFormContext";
import { sortAnnotations } from "../annotations/annotationHelpers";
import { useFormAnnotations } from "../annotations/useFormAnnotations";
import "./CollectionFormSummaryModal.scss";

export const CollectionFormSummaryModal = ({
  onClose
  // mode
}: {
  onClose: () => void;
  // mode: ConfigurationFormMode;
}) => {
  const d = useDictionary();
  const { user } = useAuth();
  const {
    onCreateAnnotation,
    onUpdateAnnotation,
    onDeleteAnnotation,
    onChangeResolveAnnotation,
    getAnnotationLocationDisplay
  } = useFormAnnotations();
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const [width, setWidth] = useState<string | undefined>("30vw");
  const [showResolved, setShowResolved] = useState(false);
  const [alertTypes, setAlertTypes] = useState<string[]>(
    Object.values(AlertType)
  );
  const [alertFilterIsOpen, setAlertFilterIsOpen] = useState(false);
  const [search, setSearch] = useState("");
  const { formAnnotationDocument, summaryModal } = useCollectionFormContext();

  const scrollToBottom = useCallback(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTop =
        scrollContainerRef.current.scrollHeight;
    }
  }, [scrollContainerRef]);

  useEffect(() => {
    scrollToBottom();
  }, [scrollToBottom]);

  const annotationsToDisplay = useMemo(() => {
    if (!formAnnotationDocument) {
      return [];
    }
    let result = Object.values(formAnnotationDocument?.annotations ?? {});

    if (
      (summaryModal.location?.variant === "question" ||
        summaryModal.location?.variant === "answer") &&
      summaryModal.location?.questionId
    ) {
      result = result.filter(
        annotation =>
          (annotation.location?.variant === "question" ||
            annotation.location?.variant === "answer") &&
          annotation.location?.questionId ===
            (summaryModal.location as QuestionLocation | AnswerLocation)
              ?.questionId &&
          ((summaryModal.location as TableCellLocation)?.rowId
            ? (annotation.location as TableCellLocation)?.rowId ===
              (summaryModal.location as TableCellLocation)?.rowId
            : true) &&
          ((summaryModal.location as TableCellLocation)?.columnId
            ? (annotation.location as TableCellLocation)?.columnId ===
              (summaryModal.location as TableCellLocation)?.columnId
            : true) &&
          ((summaryModal.location as JsonOrListItemLocation)?.itemId
            ? (annotation.location as JsonOrListItemLocation)?.itemId ===
              (summaryModal.location as JsonOrListItemLocation)?.itemId
            : true)
      );
    } else if (summaryModal.location?.variant === "section") {
      result = result.filter(
        annotation =>
          annotation.location?.variant === "section" &&
          annotation.location?.sectionId ===
            (summaryModal.location as SectionLocation)?.sectionId
      );
    }

    if (summaryModal.tab === CollectionFormSummaryModalTab.COMMENTS) {
      result = result.filter(
        annotation => annotation.variant === AnnotationVariant.COMMENT
      );
    } else if (summaryModal.tab === CollectionFormSummaryModalTab.ALERTS) {
      result = result.filter(
        annotation => annotation.variant === AnnotationVariant.ALERT
      );
    }

    if (!showResolved) {
      result = result.filter(
        annotation =>
          annotation.resolved?.status !== AnnotationResolvedStatus.RESOLVED
      );
    }

    if (search) {
      result = result.filter(
        annotation =>
          annotation.message?.toLowerCase().includes(search.toLowerCase()) ||
          (annotation.variant === AnnotationVariant.ALERT &&
            annotation.groupIdentifier
              ?.toLowerCase()
              .includes(search.toLowerCase()))
      );
    }

    if (alertTypes.length !== Object.values(AlertType).length) {
      result = result.filter(
        annotation =>
          annotation.variant !== AnnotationVariant.ALERT ||
          alertTypes.includes(annotation.type)
      );
    }

    return sortAnnotations(result);
  }, [
    alertTypes,
    formAnnotationDocument,
    search,
    showResolved,
    summaryModal.location,
    summaryModal.tab
  ]);

  const breadcrumbItems = useMemo(() => {
    const isInSectionQuestionOrAnswer =
      summaryModal.location?.variant === "question" ||
      summaryModal.location?.variant === "answer" ||
      summaryModal.location?.variant === "section";
    return [
      <BreadcrumbsItem
        key={"summary"}
        leftElement={<Icon size="s" name="folder_open" />}
        text="Summary"
        onClick={() => {
          summaryModal.setLocation(undefined);
        }}
        href=""
        isActive={!isInSectionQuestionOrAnswer}
      />,
      ...(isInSectionQuestionOrAnswer
        ? [
            <BreadcrumbsItem
              key={"question"}
              text={getAnnotationLocationDisplay(summaryModal.location) ?? ""}
              onClick={() => {}}
              href=""
              isActive
            />
          ]
        : [])
    ];
  }, [getAnnotationLocationDisplay, summaryModal]);

  const filters = useMemo(() => {
    return (
      <Inline
        className="collection-form-summary-modal__filters"
        alignment="left"
        gap="100"
        padding="100"
        spaceBetween
      >
        <Inline alignment="left" gap="100" spaceBetween width="100">
          <SearchBar value={search} handleChange={setSearch} />
          <Inline alignment="left" gap="100">
            <Toggle
              isChecked={showResolved}
              onChange={setShowResolved}
              label={d("ui.forms.summaryModal.filters.showResolved.label", {
                defaultValue: "Show Resolved"
              })}
              description={d(
                "ui.forms.summaryModal.filters.showResolved.description",
                { defaultValue: "Show Resolved Annotations" }
              )}
            />
            {(summaryModal.tab === CollectionFormSummaryModalTab.ALL ||
              summaryModal.tab === CollectionFormSummaryModalTab.ALERTS) && (
              <DropdownMenu
                isOpen={alertFilterIsOpen}
                onOpenChange={setAlertFilterIsOpen}
                position="bottom-right"
                trigger={({ onClick, isOpen }) => (
                  <IconButton
                    name="filter_alt"
                    onClick={onClick}
                    color={isOpen ? "color" : undefined}
                  />
                )}
              >
                <DropdownItemGroup
                  title={d("ui.workspace.annotations.alerts.type.title")}
                >
                  {Object.values(AlertType).map(alertType => {
                    const isChecked = alertTypes.includes(alertType);
                    return (
                      <DropdownItemCheckbox
                        key={alertType}
                        isChecked={isChecked}
                        onChange={() => {
                          setAlertTypes(prev =>
                            prev.includes(alertType)
                              ? prev.filter(type => type !== alertType)
                              : [...prev, alertType]
                          );
                        }}
                        description={d(
                          `ui.workspace.annotations.alerts.type.${alertType}.description`
                        )}
                      >
                        {d(
                          `ui.workspace.annotations.alerts.type.${alertType}.label`
                        )}
                      </DropdownItemCheckbox>
                    );
                  })}
                </DropdownItemGroup>
              </DropdownMenu>
            )}
          </Inline>
        </Inline>
        <Box>{/* <SearchBar value={search} handleChange={setSearch} /> */}</Box>
      </Inline>
    );
  }, [
    alertFilterIsOpen,
    alertTypes,
    d,
    search,
    showResolved,
    summaryModal.tab
  ]);

  return (
    <FloatingModal
      className="collection-form-summary-modal"
      headingOverride={<Breadcrumbs>{breadcrumbItems}</Breadcrumbs>}
      onClose={onClose}
      width={width}
      minWidth="300px"
      setWidth={setWidth}
    >
      <TabGroup
        options={
          Object.values(CollectionFormSummaryModalTab).map(summary => ({
            label: d(`ui.forms.summaryModal.tabs.${summary}.label`),
            value: summary
          })) ?? []
        }
        value={summaryModal.tab}
        handleChange={(tab: string) => {
          summaryModal.setTab(
            tab as "none" | `${CollectionFormSummaryModalTab}`
          );
          setTimeout(() => scrollToBottom(), 0);
        }}
      />
      {filters}
      <Divider size="small" />
      <Box height="100" overflow="auto" alignment="bottom-center">
        <Stack
          className="collection-form-summary-modal__annotations"
          width="100"
          ref={scrollContainerRef}
        >
          {annotationsToDisplay?.map(annotation => (
            <React.Fragment key={annotation.id}>
              <Divider size="small" />
              <AnnotationDisplay
                elementId={`annotation-${annotation.id}`}
                annotation={annotation}
                user={user}
                onCreateAnnotation={onCreateAnnotation}
                onDeleteAnnotation={onDeleteAnnotation}
                onUpdateAnnotation={onUpdateAnnotation}
                onChangeResolveAnnotation={onChangeResolveAnnotation}
                getAnnotationLocationDisplay={getAnnotationLocationDisplay}
              />
            </React.Fragment>
          ))}
        </Stack>
      </Box>
      {summaryModal.tab !== CollectionFormSummaryModalTab.ALERTS && (
        <Stack>
          <Divider size="small" />
          <Box padding="100">
            <AnnotationMessageField
              id="new-annotation-message-field"
              onSave={message => {
                if (!message || !user?.id) {
                  return;
                }
                onCreateAnnotation({
                  annotation: {
                    variant: AnnotationVariant.COMMENT,
                    message,
                    attachments: [],
                    location: summaryModal.location
                  }
                });

                setTimeout(scrollToBottom, 100);
              }}
              isEditing={false}
              user={user}
            />
          </Box>
        </Stack>
      )}
    </FloatingModal>
  );
};
