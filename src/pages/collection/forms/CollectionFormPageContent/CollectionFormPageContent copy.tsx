import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from "react";

import {
  Box,
  Button,
  ColorGroup,
  DropdownMenu,
  Floating,
  FloatingPosition,
  Inline,
  Loading,
  Pill,
  Stack,
  Tooltip,
  WhiteboardTool,
  WhiteboardToolbar
} from "@oneteam/onetheme";
import { debounce } from "lodash";
import { useFormContext } from "react-hook-form";
import { ReactZoomPanPinchRef } from "react-zoom-pan-pinch";

import { useCursorStore } from "@components/shared/WhiteboardPageBodyTemplate/CursorStore";
import { WhiteboardPageBodyTemplate } from "@components/shared/WhiteboardPageBodyTemplate/WhiteboardPageBodyTemplate.tsx";

import { CollectionFormContent } from "@pages/collection/forms/CollectionFormContent";
import { CollectionFormDocumentsPanel } from "@pages/collection/forms/CollectionFormDocumentsPanel/CollectionFormDocumentsPanel";
import { useFormCollectionBreadcrumbs } from "@pages/collection/forms/useFormCollectionBreadcrumbs";

import { useDictionary } from "@src/hooks/useDictionary";
import { HighlightColor } from "@src/types/AnnotationLocation";
import {
  CollectionFormMode,
  CollectionFormSummaryModalTab
} from "@src/types/collection/CollectionForm";
import { FlowButtonLocation } from "@src/types/collection/FlowExecution";

import { CollectionFormToolbar } from "../../flows/CollectionFormToolbar/CollectionFormToolbar";
import { FlowButtons } from "../../interactions/FlowButtons";
import { CollaboratorsPresence } from "../CollaboratorsPresence";
import { useCollectionFormContext } from "../CollectionFormContext";
import { CollectionFormSummaryModal } from "../CollectionFormSummaryModal/CollectionFormSummaryModal";
import "./CollectionFormPageContent.scss";

export const CollectionFormPageContent = () => {
  const d = useDictionary();
  const frameOfReferenceForPresence = useRef<HTMLDivElement>(null);
  const WHEEL_PANNING_DEBOUNCE = 175;

  // Use transient updates for cursor position to avoid re-renders on mouse movement
  const isCursorOnElementRef = useRef(false);

  const { setValue, trigger } = useFormContext();

  const {
    collectionFormId,
    documentId,
    formData,
    docChange,
    summaryModal,
    settings,
    mode,
    onChangeMode,
    currentHighlightColor,
    setCurrentHighlightColor
  } = useCollectionFormContext();

  const dynamicBreadcrumbs = useFormCollectionBreadcrumbs({
    foundationConfigurationId: formData?.configuration.foundationId
  });

  useEffect(() => {
    if (!formData?.answers) {
      return;
    }
    // update answers in form context so that new table rows are added to the form state
    setValue("answers", formData.answers);
    trigger("answers");
  }, [formData?.answers, trigger, setValue]);

  const ref = useRef<ReactZoomPanPinchRef>(null);
  // const [isWheelPanning, setIsWheelPanning] = useState(false);
  const isWheelPanningRef = useRef(false);
  const [previousWhiteboardScale] = useState(1);

  const resetPanningState = useCallback(() => {
    // setIsWheelPanning(false);
    isWheelPanningRef.current = false;
  }, []);

  const debouncedResetPanningState = useMemo(
    () => debounce(resetPanningState, WHEEL_PANNING_DEBOUNCE),
    [resetPanningState]
  );

  // const isCursorOnElement = useCallback(() => {
  //   // Get the current cursor position at the time this function is called
  //   const currentCursorPosition = cursorPositionRef.current;

  //   // Check if the cursor is over any table question or textarea field input.
  //   // We also need to check textareaFieldInputs because text-type questions with multiline enabled
  //   // can become scrollable once their height exceeds 400px.
  //   if (!currentCursorPosition || (!tableQuestions && !textAreaFieldInputs)) {
  //     return false;
  //   }

  //   for (const element of [...tableQuestions, ...textAreaFieldInputs]) {
  //     const rect = element.getBoundingClientRect();
  //     if (
  //       currentCursorPosition.x >= rect.left &&
  //       currentCursorPosition.x <= rect.right &&
  //       currentCursorPosition.y >= rect.top &&
  //       currentCursorPosition.y <= rect.bottom
  //     ) {
  //       return true;
  //     }
  //   }
  //   return false;
  // }, [tableQuestions, textAreaFieldInputs]);

  // Track cursor-on-element status with state for real-time updates
  // const [cursorOnElement, setCursorOnElement] = useState(false);

  const handlePanning = useCallback(() => {
    console.log("handlePanning called", isCursorOnElementRef.current);
    const currentScale = ref.current?.instance.transformState.scale;
    const previousScale = ref.current?.instance.transformState.previousScale;
    if (
      currentScale !== previousScale &&
      previousScale !== previousWhiteboardScale
    ) {
      if (previousScale) {
        // setPreviousWhiteboardScale(previousScale);
      }

      // setIsWheelPanning(false);
      isWheelPanningRef.current = false;
      return;
    }

    if (isCursorOnElementRef.current && !isWheelPanningRef.current) {
      debouncedResetPanningState();
    } else if (!isCursorOnElementRef.current) {
      // setIsWheelPanning(true);
      isWheelPanningRef.current = true;
      debouncedResetPanningState();
    }
  }, [ref, debouncedResetPanningState, previousWhiteboardScale]);

  // Connect to cursor store and update cursor position + element status in real-time
  // useEffect(() => {
  //   const unsubscribe = useCursorStore.subscribe(state => {
  //     cursorPositionRef.current = state.cursorPosition;
  //     console.log("cursor position updated:", cursorPositionRef.current);
  //     // Check if cursor is on element every time position updates
  //     const test = isCursorOnElement();
  //     console.log("isCursorOnElement:", test);
  //     handlePanning();
  //     // setCursorOnElement(isOnElement);
  //   });

  //   return unsubscribe;
  // }, [isCursorOnElement, handlePanning]);

  // useEffect(() => {
  //   const unsubscribe = useCursorStore.subscribe(state => {
  //     isCursorOnElementRef.current = state.cursorPosition;
  //     console.log("cursor position updated:", cursorPositionRef.current);
  //     // Check if cursor is on element every time position updates
  //     const test = isCursorOnElement();
  //     console.log("isCursorOnElement:", test);
  //     handlePanning();
  //     // setCursorOnElement(isOnElement);
  //   });

  //   return unsubscribe;
  // }, [isCursorOnElement, handlePanning]);
  // Initialize cursor store once and subscribe to cursor position changes using transient updates
  useEffect(() => {
    // Initialize the cursor store (only once)
    const cleanup = useCursorStore.getState().initialize();

    // Set initial value
    isCursorOnElementRef.current = useCursorStore.getState().isOnElement;

    // Use transient updates pattern - subscribe with useRef to avoid re-renders
    const unsubscribe = useCursorStore.subscribe(state => {
      isCursorOnElementRef.current = state.isOnElement;
      // console.log("cursor on element:", state.isOnElement);
    });

    return () => {
      unsubscribe();
      if (cleanup) cleanup();
    };
  }, []); // Empty dependency array to run only once

  console.log("cursorOnElement", isCursorOnElementRef.current);

  if (!formData || !docChange) {
    return <Loading size={24} />;
  }

  return (
    <WhiteboardPageBodyTemplate
      handlePanning={handlePanning}
      className="collection-form-page-content"
      name={formData?.configuration?.name ?? "  "}
      toolbar={<CollectionFormToolbar />}
      headingPills={
        <Inline gap="050" padding="050" alignment="left" width="fit">
          <Tooltip
            content={d("ui.configuration.forms.tooltip.formKey")}
            delay={500}
          >
            <Pill label={formData.configuration?.key ?? ""} />
          </Tooltip>
          {formData.interval?.name && (
            <Tooltip
              content={d("ui.configuration.forms.tooltip.interval", {
                seriesName: formData.series?.name ?? ""
              })}
              delay={500}
            >
              <Pill label={`${formData.interval?.name ?? ""}`} />
            </Tooltip>
          )}
          {/* <Pill
            variant="neutral"
            label={formData.foundation.name ?? ""}
          /> */}
        </Inline>
      }
      isRenamable={false}
      headingFilters={
        <Stack gap="100" alignment="left">
          {documentId && (
            <Inline gap="100" alignment="left">
              <CollaboratorsPresence
                documentId={documentId}
                frameOfReference={frameOfReferenceForPresence}
                showCursors={settings.liveCursorsVisible}
              />
            </Inline>
          )}
        </Stack>
      }
      breadcrumbs={dynamicBreadcrumbs}
      floatingLayer={({ parentRef }) => (
        <>
          <Floating
            parentRef={parentRef}
            position={FloatingPosition.BOTTOM_LEFT}
          >
            <CollectionFormDocumentsPanel />
          </Floating>
          <Floating parentRef={parentRef} position={FloatingPosition.RIGHT}>
            {/* TODO: use enum */}
            {summaryModal.tab !== "none" && (
              <CollectionFormSummaryModal
                onClose={() => {
                  summaryModal.setTab("none");
                  summaryModal.setLocation(undefined);
                }}
                // questionPath={questionPath}
                // onClose={() => updateQuestionHash("none")}
              />
            )}
          </Floating>
          <Floating parentRef={parentRef} position={FloatingPosition.TOP_RIGHT}>
            <Inline gap="150">
              {collectionFormId && (
                <FlowButtons
                  collectionId={collectionFormId}
                  location={FlowButtonLocation.FORM}
                />
              )}
              <Button
                label={"Summary"}
                variant="secondary"
                onClick={() => {
                  if (
                    (summaryModal.location?.variant === "question" &&
                      summaryModal.location?.questionId) ||
                    summaryModal.tab === "none"
                  ) {
                    summaryModal.setTab(CollectionFormSummaryModalTab.ALL);
                    summaryModal.setLocation(undefined);
                  } else {
                    summaryModal.setTab("none");
                    summaryModal.setLocation(undefined);
                  }
                }}
                leftIcon={{ name: "folder_open" }}
              />
            </Inline>
          </Floating>
        </>
      )}
      modeSelection={
        <Stack alignment="center" gap="050">
          <WhiteboardToolbar
            style={{
              padding: "2px",
              visibility:
                mode === CollectionFormMode.ANNOTATE ? "visible" : "hidden"
            }}
          >
            <Box padding="050">
              <ColorGroup
                options={Object.values(HighlightColor).map(color => ({
                  color: `var(--components-whiteboard-sticky-note-color-${color})`,
                  value: color
                }))}
                onChange={value => {
                  if (!value || !setCurrentHighlightColor) {
                    return;
                  }
                  setCurrentHighlightColor(value as HighlightColor);
                }}
                value={currentHighlightColor}
                allowClear={false}
              />
            </Box>
          </WhiteboardToolbar>

          <WhiteboardToolbar>
            <Tooltip
              content={d("ui.common.view")}
              position="top-left"
              delay={200}
            >
              <WhiteboardTool
                isSelected={mode === CollectionFormMode.VIEW}
                icon={{ name: "arrow_selector_tool" }}
              />
            </Tooltip>
            <Tooltip
              content={d("ui.common.update")}
              position="top-left"
              delay={200}
            >
              <WhiteboardTool
                isSelected={mode === CollectionFormMode.EDIT}
                icon={{ name: "edit" }}
                onClick={() => onChangeMode(CollectionFormMode.EDIT)}
              />
            </Tooltip>
            <DropdownMenu
              isOpen={mode === CollectionFormMode.ANNOTATE}
              position="top-left"
              trigger={({ isOpen, onClick }) => {
                return (
                  <Tooltip
                    content={d("ui.common.highlight")}
                    position="top-left"
                    delay={200}
                  >
                    <WhiteboardTool
                      isSelected={isOpen}
                      icon={{
                        name: "ink_highlighter"
                      }}
                      onClick={e => {
                        onClick?.(e);
                        onChangeMode(CollectionFormMode.ANNOTATE);
                      }}
                    />
                  </Tooltip>
                );
              }}
            />
          </WhiteboardToolbar>
        </Stack>
      }
    >
      <Stack
        className="collection-form-page-content__content"
        gap="200"
        ref={frameOfReferenceForPresence}
      >
        <CollectionFormContent
          configuration={formData.configuration}
          level={0}
        />
      </Stack>
    </WhiteboardPageBodyTemplate>
  );
};

CollectionFormPageContent.displayName = "CollectionFormPageContent";
