import React from "react";

import { Icon, Inline, Stack, Tooltip } from "@oneteam/onetheme";

import "./LabelWithHighlight.scss";

export const LabelWithHighlight = ({
  label,
  description,
  tooltip,
  required = false,
  className
}: {
  label: string;
  description?: string;
  tooltip?: string;
  required?: boolean;
  className?: string;
}) => {
  return (
    <Stack classNames={["label", className]}>
      <Inline alignment="left" gap="050">
        <span className="label__text">
          <mark>{label}</mark>
        </span>
        {required && <span className="required">*</span>}
        {tooltip && (
          <Tooltip content={tooltip}>
            <Icon
              name="info"
              color="text-tertiary"
              className="label__icon"
              size="s"
              fillStyle="filled"
            />
          </Tooltip>
        )}
      </Inline>
      {description && (
        <span className="label__description">
          <mark>{description}</mark>
        </span>
      )}
    </Stack>
  );
};
