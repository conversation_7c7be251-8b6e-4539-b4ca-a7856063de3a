import { createContext, useContext } from "react";

import {
  AnnotationLocation,
  HighlightColor
} from "@src/types/AnnotationLocation";
import { Question } from "@src/types/Question";
import {
  AnswerDocChange,
  CollectionFormData,
  CollectionFormMode,
  CollectionFormSummaryModalTab,
  FormAnnotationDocChange
} from "@src/types/collection/CollectionForm";
import { FormAnnotationDocument } from "@src/types/documentTypes";

export type FieldState = {
  error: undefined | string;
};

export type CollectionFormContextType = {
  documentId?: string;
  setDocumentId?: (documentId: string) => void;

  docChange?: AnswerDocChange;
  setDocChange?: (docChange: AnswerDocChange) => void;

  collectionFormId?: number;

  formData?: CollectionFormData;
  setFormData?: (data: CollectionFormData) => void;

  getFieldState?: (field: string) => FieldState;
  setFieldState?: (field: string, state: FieldState) => void;

  // Annotations
  annotationDocumentId?: string;
  setAnnotationDocumentId?: (documentId: string) => void;

  formAnnotationDocument?: FormAnnotationDocument;
  setFormAnnotationDocument?: (annotations: FormAnnotationDocument) => void;

  annotationDocChange?: FormAnnotationDocChange;
  setAnnotationDocChange?: (
    annotationDocChange: FormAnnotationDocChange
  ) => void;

  summaryModal: {
    tab?: "none" | `${CollectionFormSummaryModalTab}`;
    setTab: (tab?: "none" | `${CollectionFormSummaryModalTab}`) => void;
    // filter??
    location?: AnnotationLocation;
    setLocation: (location?: AnnotationLocation) => void;
  };
  questionsById: {
    [questionId: string]: Question;
  };
  onChangeSectionIsCollapsed: ({
    sectionId,
    isCollapsed
  }: {
    sectionId: string;
    isCollapsed: boolean;
  }) => void;

  settings: {
    liveCursorsVisible: boolean;
    setLiveCursorsVisible: (visible: boolean) => void;
  };

  mode?: `${CollectionFormMode}`;
  onChangeMode: (mode: `${CollectionFormMode}`) => void;

  currentHighlightColor?: HighlightColor;
  setCurrentHighlightColor?: (color: HighlightColor) => void;
};

export const CollectionFormContext = createContext<CollectionFormContextType>({
  documentId: undefined,
  setDocumentId: () => {},
  collectionFormId: undefined,
  formData: undefined,
  setFormData: () => {},
  annotationDocumentId: undefined,
  setAnnotationDocumentId: () => {},
  formAnnotationDocument: undefined,
  setFormAnnotationDocument: () => {},
  docChange: undefined,
  setDocChange: () => {},
  getFieldState: () => ({ error: undefined }),
  setFieldState: () => {},
  summaryModal: {
    tab: "none",
    setTab: () => {},
    location: undefined,
    setLocation: () => {}
  },
  questionsById: {},
  onChangeSectionIsCollapsed: () => {},
  settings: {
    liveCursorsVisible: true,
    setLiveCursorsVisible: () => {}
  },
  mode: undefined,
  onChangeMode: () => {},
  currentHighlightColor: undefined,
  setCurrentHighlightColor: () => {}
});

export const useCollectionFormContext = () => useContext(CollectionFormContext);
