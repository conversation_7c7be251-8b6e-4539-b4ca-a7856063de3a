import React, { useCallback, useMemo } from "react";

import { isQuestionContent } from "@helpers/forms/formHelper.ts";

import { Section } from "@src/types/FormConfiguration.ts";
import { Question } from "@src/types/Question.ts";

import { CollectionFormQuestions } from "../questions/CollectionFormQuestions.tsx";
import { CollectionFormSectionBlock } from "./CollectionFormSectionBlock/CollectionFormSectionBlock.tsx";

export const CollectionFormSection = ({
  level,
  section,
  index
}: {
  level: number;
  section: Section;
  index: number;
}) => {
  const isQuestionSection = useMemo(
    () => isQuestionContent(section.content),
    [section.content]
  );

  const renderContent = useCallback(() => {
    if (isQuestionSection) {
      return (
        <CollectionFormQuestions questions={section.content as Question[]} />
      );
    }

    return (section.content as Section[]).map((contentItem, itemIndex) => {
      return (
        <CollectionFormSection
          key={`section-${contentItem.id}`}
          section={contentItem}
          level={level + 1}
          index={itemIndex}
        />
      );
    });
  }, [isQuestionSection, level, section.content]);

  // Section block here
  return (
    <CollectionFormSectionBlock
      key={`section-block-${section.id}-${index}`}
      section={section}
    >
      {renderContent()}
    </CollectionFormSectionBlock>
  );
};

CollectionFormSection.displayName = "CollectionFormSection";
