import React, {
  LegacyRef,
  PropsWithChildren,
  RefObject,
  useCallback,
  useMemo,
  useRef
} from "react";

import {
  Accordion,
  Box,
  Card,
  ColorText,
  CustomAccordionTrigger,
  FontWeight,
  Heading,
  HeadingSize,
  Inline,
  OpenCloseIcon,
  Stack
} from "@oneteam/onetheme";

import { useCollectionFormContext } from "@pages/collection/forms/CollectionFormContext";

import { Section } from "@src/types/FormConfiguration.ts";

import { HighlightAnnotationLocation } from "../../HighlightAnnotationLocation/HighlightAnnotationLocation";
import { CollectionQuestionBlockAlertFloating } from "../../questions/CollectionQuestionBlock/CollectionQuestionBlockAlert/CollectionQuestionBlockAlertFloating/CollectionQuestionBlockAlertFloating";
import { CollectionQuestionBlockCommentFloating } from "../../questions/CollectionQuestionBlock/CollectionQuestionBlockComment/CollectionQuestionBlockCommentFloating/CollectionQuestionBlockCommentFloating";
import "./CollectionFormSectionBlock.scss";

export const CollectionFormSectionBlock = ({
  section,
  children
}: PropsWithChildren<{
  section: Section;
}>) => {
  const sectionRef = useRef<HTMLElement>();
  const { onChangeSectionIsCollapsed, formAnnotationDocument } =
    useCollectionFormContext();

  const isCollapsed = useMemo(
    () => formAnnotationDocument?.state?.sectionsCollapsed?.[section.id],
    [formAnnotationDocument, section.id]
  );

  const sectionActions = useMemo(() => {
    return (
      <Inline
        className="collection-form-section-block__actions"
        gap="200"
        height="fit"
        style={{ background: "white", padding: "0 2px" }}
      />
    );
  }, []);

  const accordionTrigger: CustomAccordionTrigger = useCallback(
    ({ isOpen, onClick }) => (
      <Inline
        className="collection-form-section-block__heading"
        width="100"
        gap="100"
        alignment="left"
        spaceBetween
      >
        <Inline
          className="collection-form-section-block__heading__left"
          gap="050"
          onClick={section.content?.length ? e => onClick?.(e) : undefined}
          alignment="left"
          width="100"
        >
          <OpenCloseIcon isOpen={isOpen} />
          {/* {section.isSource && <Pill label="Data Source" />} */}
          <HighlightAnnotationLocation
            location={{
              variant: "section",
              sectionId: section.id
            }}
          >
            <mark>
              <Heading size={HeadingSize.S} weight={FontWeight.REGULAR}>
                {section.name}
              </Heading>
            </mark>
          </HighlightAnnotationLocation>
        </Inline>

        {sectionActions}
        {/* <Box width="100">
          <ProgressBar percentage={80} message="8/8 complete" />
        </Box> */}
      </Inline>
    ),
    [section.content?.length, section.id, section.name, sectionActions]
  );

  // sub-section
  if (section.level === 1) {
    return (
      <Card
        className="collection-form-section-block"
        key={section.id}
        size="medium"
        ref={sectionRef}
      >
        {isCollapsed && (
          <CollectionQuestionBlockAlertFloating
            location={{
              variant: "section",
              sectionId: section.id
            }}
            parentRef={sectionRef as RefObject<HTMLElement>}
            anchorName={`section-${section.id}-alerts`}
          />
        )}
        <Accordion
          contentOverflow="visible"
          isOpen={!isCollapsed}
          onOpenChange={(open: boolean) => {
            onChangeSectionIsCollapsed?.({
              sectionId: section.id,
              isCollapsed: !open
            });
          }}
          key={`${section.id}-accordion`}
          trigger={accordionTrigger}
        >
          <Stack gap="100" style={{ padding: "var(--spacing-100) 0 0 0" }}>
            {children}
          </Stack>
        </Accordion>
        {isCollapsed && (
          <CollectionQuestionBlockCommentFloating
            location={{
              variant: "section",
              sectionId: section.id
            }}
            parentRef={sectionRef as RefObject<HTMLElement>}
            anchorName={`section-${section.id}-alerts`}
          />
        )}
      </Card>
    );
  }

  // section
  return (
    <Stack
      className="collection-form-section-block"
      gap="050"
      style={{
        paddingLeft: `calc(var(--spacing-100) * ${(section.level ?? 0) - 2})`
      }}
      // overflow="auto"
      ref={sectionRef as LegacyRef<HTMLDivElement> | undefined}
    >
      <Inline alignment="left" width="100" position="relative">
        <Inline
          className="collection-form-section-block__heading"
          width="100"
          gap="050"
          alignment="left"
          spaceBetween
        >
          <Box
            style={{
              background: "var(--color-surface-secondary)",
              padding: "0 3px"
            }}
          >
            <HighlightAnnotationLocation
              location={{
                variant: "section",
                sectionId: section.id
              }}
            >
              <mark>
                <Heading
                  size={HeadingSize.XS}
                  color={ColorText.SECONDARY}
                  weight={FontWeight.REGULAR}
                >
                  {section.name}
                </Heading>
              </mark>
            </HighlightAnnotationLocation>
          </Box>
          {sectionActions}
        </Inline>
      </Inline>
      {children}
    </Stack>
  );
};
