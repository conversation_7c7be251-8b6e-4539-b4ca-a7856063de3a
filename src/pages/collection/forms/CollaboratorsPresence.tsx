import React, {
  use<PERSON><PERSON>back,
  useEffect,
  useMemo,
  useRef,
  useState
} from "react";

import {
  AnyDocumentId,
  DocHandleEphemeralMessagePayload
} from "@automerge/automerge-repo";
import { useDocHandle } from "@automerge/automerge-repo-react-hooks";
import {
  Avatar,
  AvatarSize,
  Box,
  Icon,
  Inline,
  Pill,
  PillVariant,
  Tooltip
} from "@oneteam/onetheme";
import { throttle } from "lodash";
import { useTransformEffect } from "react-zoom-pan-pinch";

import logo from "@src/components/shared/Navigation/logo.png";
import { useAuth } from "@src/hooks/useAuth";
import { useDictionary } from "@src/hooks/useDictionary";

// As long as the user is interacting with the site, we will broadcast their presence at a throttled interval
// User broadcast their 'intended' availabilty which is how long that presence event is valid for
// It's intentially a bit longer so connected users aren't flipping in and out of view
// This is a simple way to handle the case where a user disconnects without sending a 'goodbye' message
const BROADCAST_THROTTLE_SEC = 1000 * 1;
const CURSOR_TRANSITION_DURATION_SEC = 1000 * 0.8;
const AVAILABILITY_DURATION_SEC = 1000 * 7;
enum PayloadMessageType {
  PRESENCE = "presence"
}

enum CollaboratorType {
  USER = "user",
  SYSTEM = "system"
}

// collaborator is client that is connected to the document
// peerId is unique PER client connection. i.e. a User may be connected from multiple devices so these result in different peerIds
interface Collaborator {
  peerId?: string;
  type: CollaboratorType;
  firstName: string;
  lastName: string;
  cursorLocation?: CursorLocation;
}

interface CursorLocation {
  x: number;
  y: number;
}

interface CollaboratorPrecence extends Collaborator {
  availableUntil: Date;
}

interface EMPayloadMessagePresence {
  type: PayloadMessageType.PRESENCE;
  user: {
    type: CollaboratorType;
    firstName: string;
    lastName: string;
  };
  availabilityDurationMs: number;
  location?: CursorLocation;
}

/**
 * @param frameOfReference Ref to the element that the cursor locations are relative to
 */
export const CollaboratorsPresence = ({
  documentId,
  frameOfReference,
  showCursors = true
}: {
  documentId: string;
  frameOfReference: React.RefObject<HTMLElement>;
  showCursors?: boolean;
}) => {
  const d = useDictionary();
  const docHandle = useDocHandle<unknown>(documentId as AnyDocumentId);
  const { user: authUser } = useAuth();
  const [activeCollaborators, setActiveCollaborators] = useState<{
    [userId: string]: CollaboratorPrecence;
  }>({});
  const currentCursorLocation = useRef<CursorLocation | undefined>(undefined);
  const currentZoomScaleForCuror = useRef<number>(1);

  useTransformEffect(({ state }) => {
    const oneHundredPercentScale = 1;
    const currentScaleAsPercentage = Math.round(
      (state.scale / oneHundredPercentScale) * 100
    );
    currentZoomScaleForCuror.current = currentScaleAsPercentage;
  });

  const calculateAvailabileUntil = (
    durationMs: number,
    reference: Date = new Date()
  ) => {
    return new Date(reference.getTime() + durationMs);
  };

  const throttledBroadcastPresence = useMemo(
    () =>
      throttle(message => {
        docHandle?.broadcast(message);
      }, BROADCAST_THROTTLE_SEC),
    [docHandle]
  );

  const broadcastPresence = useCallback(async () => {
    const message: EMPayloadMessagePresence = {
      type: PayloadMessageType.PRESENCE,
      user: {
        type: CollaboratorType.USER,
        firstName: authUser?.firstName ?? "",
        lastName: authUser?.lastName ?? ""
      },
      availabilityDurationMs: AVAILABILITY_DURATION_SEC,
      location: currentCursorLocation.current
    };

    setActiveCollaborators(prev => {
      const now = new Date();
      const filtered = Object.fromEntries(
        Object.entries(prev).filter(([, user]) => {
          return now <= user.availableUntil;
        })
      );

      return {
        ...filtered
      };
    });

    throttledBroadcastPresence(message);
  }, [authUser?.firstName, authUser?.lastName, throttledBroadcastPresence]);

  const onMessageReceived = useCallback(
    (payload: DocHandleEphemeralMessagePayload<unknown>) => {
      const payloadMessage = payload.message;
      const senderId = payload.senderId;

      const messageType = (payloadMessage as { type: string }).type;
      if (messageType !== PayloadMessageType.PRESENCE) {
        return;
      }

      const presenseMessage = payloadMessage as EMPayloadMessagePresence;

      setActiveCollaborators(prev => {
        const now = new Date();
        const filtered = Object.fromEntries(
          Object.entries(prev).filter(([, user]) => {
            return now <= user.availableUntil;
          })
        );

        const collaborator: CollaboratorPrecence = {
          ...presenseMessage.user,
          peerId: senderId,
          availableUntil: calculateAvailabileUntil(
            presenseMessage.availabilityDurationMs
          ),
          cursorLocation: presenseMessage.location
        };

        return {
          ...filtered,
          [senderId]: collaborator
        };
      });
    },
    []
  );

  const getCursorReferenceElement = useCallback((): Element | undefined => {
    return frameOfReference.current as Element;
  }, [frameOfReference]);

  const makeAbsoluteFromOrigin = (
    loc: { x: number; y: number },
    origin: Element
  ) => {
    const rect = origin.getBoundingClientRect();
    const scaled = {
      x: loc.x * (currentZoomScaleForCuror.current / 100),
      y: loc.y * (currentZoomScaleForCuror.current / 100)
    };
    const x = scaled.x + rect.x;
    const y = scaled.y + rect.y;
    return { x, y };
  };

  const makeRelativeToOrigin = (
    loc: { x: number; y: number },
    origin: Element
  ) => {
    const rect = origin.getBoundingClientRect();
    const x = (loc.x - rect.x) / (currentZoomScaleForCuror.current / 100.0);
    const y = (loc.y - rect.y) / (currentZoomScaleForCuror.current / 100.0);
    return { x, y };
  };

  useEffect(() => {
    const mouseHandler = (e: MouseEvent) => {
      const origin = getCursorReferenceElement();
      if (!origin) {
        return;
      }

      const cursorLocation = makeRelativeToOrigin(e, origin);
      currentCursorLocation.current = cursorLocation;
    };

    window.document.addEventListener("mousemove", mouseHandler);
    return () => {
      window.document.removeEventListener("mousemove", mouseHandler);
    };
  }, [authUser?.firstName, authUser?.lastName, getCursorReferenceElement]);

  useEffect(() => {
    if (!docHandle) {
      return;
    }

    window.document.addEventListener("mousemove", broadcastPresence);
    window.document.addEventListener("scroll", broadcastPresence);
    window.document.addEventListener("keyup", broadcastPresence);
    window.document.addEventListener("click", broadcastPresence);
    docHandle?.on("ephemeral-message", onMessageReceived);

    return () => {
      docHandle.off("ephemeral-message");
      window.document.removeEventListener("mousemove", broadcastPresence);
      window.document.removeEventListener("scroll", broadcastPresence);
      window.document.removeEventListener("keyup", broadcastPresence);
      window.document.addEventListener("click", broadcastPresence);
    };
  }, [broadcastPresence, docHandle, onMessageReceived]);

  const collaborators = useMemo(() => {
    const result: Collaborator[] = [];

    // add the current user
    if (authUser) {
      result.push({
        peerId: "self",
        type: CollaboratorType.USER,
        firstName: authUser.firstName,
        lastName: authUser.lastName
      });
    }

    // add connected users
    for (const [, user] of Object.entries(activeCollaborators ?? [])) {
      result.push(user);
    }

    return result;
  }, [activeCollaborators, authUser]);

  if (!collaborators.length) {
    return <></>;
  }

  const renderSystemCollaborator = (systemCollaborator: Collaborator) => {
    return (
      <Tooltip
        content={`${d("ui.forms.collaboration.users.system.displayName")}`}
      >
        <Box
          key={systemCollaborator.peerId}
          alignment="center"
          style={{
            width: "var(--components-avatar-s-size, 24px)",
            height: "var(--components-avatar-s-size, 24px)",
            borderRadius: "var(--components-avatar-xl-size, 100%)",
            backgroundColor: "var(--color-surface-secondary)"
          }}
        >
          <img
            src={logo}
            alt={d("ui.navigation.altOneTeamAILogo")}
            style={{ width: "var(--components-avatar-xs-size, 16px)" }}
          />
        </Box>
      </Tooltip>
    );
  };

  const renderUserCollaborator = (userCollaborator: Collaborator) => {
    const isAnonymous =
      userCollaborator.firstName === "" && userCollaborator.lastName === "";
    if (isAnonymous) {
      const displayName = d(
        "ui.forms.collaboration.users.anonymous.displayName"
      );
      return (
        <Tooltip content={`${displayName}`} key={userCollaborator.peerId}>
          <Avatar
            key={userCollaborator.peerId}
            size={AvatarSize.S}
            user={{
              firstName: d("ui.forms.collaboration.users.anonymous.firstName"),
              lastName: d("ui.forms.collaboration.users.anonymous.lastName")
            }}
          />
        </Tooltip>
      );
    }

    const displayName = `${userCollaborator.firstName} ${userCollaborator.lastName}`;
    return (
      <Tooltip content={`${displayName}`} key={userCollaborator.peerId}>
        <Avatar
          key={userCollaborator.peerId}
          size={AvatarSize.S}
          user={{
            firstName: userCollaborator.firstName,
            lastName: userCollaborator.lastName
          }}
        />
      </Tooltip>
    );
  };

  const renderCollaboratorCursors = (collaborators: Collaborator[]) => {
    const origin = getCursorReferenceElement();
    if (!origin) {
      return;
    }

    return (
      <>
        {collaborators.map((user: Collaborator) => {
          if (!user.cursorLocation) {
            return;
          }
          const cursor = makeAbsoluteFromOrigin(user.cursorLocation, origin);

          return (
            <Box
              key={user.peerId}
              style={{
                position: "fixed",
                top: cursor.y,
                left: cursor.x,
                transition: `all ${CURSOR_TRANSITION_DURATION_SEC}ms ease-in-out`
              }}
            >
              <Icon
                name="arrow_selector_tool"
                fillStyle="filled"
                color="color"
              />
              <Pill
                label={`${user.firstName} ${user.lastName}`}
                variant={PillVariant.COLORED}
              />
            </Box>
          );
        })}
      </>
    );
  };

  return (
    <Box>
      <Inline gap="050" alignment="center">
        {collaborators.map((user: Collaborator) => {
          if (user.type === CollaboratorType.USER) {
            return renderUserCollaborator(user);
          }
          return renderSystemCollaborator(user);
        })}
      </Inline>
      {showCursors && <Box>{renderCollaboratorCursors(collaborators)}</Box>}
    </Box>
  );
};
