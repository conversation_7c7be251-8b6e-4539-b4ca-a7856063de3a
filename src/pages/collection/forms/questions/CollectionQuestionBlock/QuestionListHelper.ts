import { customNanoId } from "@helpers/customNanoIdHelper.ts";

import { Question } from "@src/types/Question";
import { ListQuestionProperties } from "@src/types/QuestionProperties";
import { ListAnswerItem } from "@src/types/collection/CollectionForm";

export class QuestionListHelper {
  private readonly question: Question<ListQuestionProperties>;

  constructor(question: Question<ListQuestionProperties>) {
    this.question = question;
  }

  // path should look like `answers.VEOTahOeLj.value.entities.yD6Dq_LaVDhyD12CXlGJe`
  itemId(itemPath: string) {
    const path = itemPath.split(".");
    return path.pop()!;
  }

  createNewItem() {
    const itemId = customNanoId();
    if (this.question.properties) {
      const listOf = (this.question.properties as ListQuestionProperties)
        ?.items?.[0];
      if (!listOf) {
        return {
          id: itemId,
          item: {}
        };
      }
      const listAnswerItem: ListAnswerItem = {
        id: itemId,
        item: {
          [listOf.id]: {
            questionId: listOf.id,
            type: listOf.type
          }
        }
      }; // create a new row without answers
      return listAnswerItem;
    } else {
      console.error("No properties found for table question");
      return {
        id: itemId,
        item: {}
      };
    }
  }
}
