import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";

import {
  Button,
  FileUpload,
  IconFillStyle,
  Inline,
  Stack,
  TableField,
  TableFieldCellValue,
  TableFieldColumnId,
  TableFieldColumns,
  TableFieldReorderRow,
  TableFieldRowValue,
  TableFieldSetColumn,
  TableFieldSetRow,
  TableFieldSetTable,
  TableFieldValue,
  ToastNotificationVariant
} from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import {
  addItem,
  mapOverResource,
  removeItem
} from "@helpers/OrderedMapNoState.ts";
import { getByPath } from "@helpers/automergeDocumentHelper.ts";
import { downloadTableData, uploadTableDataForPrefill } from "@helpers/files";
import { updateAnswer } from "@helpers/forms/answerHelper";

import {
  MAX_FILE_SIZE_MB,
  fileFormatsToMimeType
} from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionConfigurationFields/Files/FilesHelper";

import { useCollectionFormContext } from "@pages/collection/forms/CollectionFormContext";
import { QuestionAnswer } from "@pages/collection/forms/questions/CollectionQuestionBlock/QuestionAnswer";
import { QuestionTableHelper } from "@pages/collection/forms/questions/CollectionQuestionBlock/QuestionTableHelper";
import { PushToastNotification } from "@pages/workspace/WorkspaceLayout";

import { Dictionary } from "@src/hooks/useDictionary.tsx";
import { Question } from "@src/types/Question.ts";
import { TableQuestionProperties } from "@src/types/QuestionProperties.ts";
import {
  FormAnswer,
  TableAnswer,
  TableAnswerRow
} from "@src/types/collection/CollectionForm.ts";
import { Resource } from "@src/types/documentTypes.ts";

import { HighlightAnnotationLocation } from "../../HighlightAnnotationLocation/HighlightAnnotationLocation";
import { LabelWithHighlight } from "../../LabelWithHighlight";
import { CollectionQuestionBlockAlert } from "./CollectionQuestionBlockAlert/CollectionQuestionBlockAlert";
import { CollectionQuestionBlockComment } from "./CollectionQuestionBlockComment/CollectionQuestionBlockComment";

// TODO: move to OTAIFormFieldTable
export const QuestionTableAnswer = ({
  d,
  question,
  answer,
  answerAccessor,
  showPreviewAnswerRow = false,
  disabled
}: {
  d: Dictionary;
  question: Question<TableQuestionProperties>;
  answer?: FormAnswer<TableAnswer>;
  answerAccessor: string;
  disableAddRow?: boolean;
  showPreviewAnswerRow?: boolean;
  disabled?: boolean;
}) => {
  const { pushToastNotification } = useOutletContext<{
    pushToastNotification: PushToastNotification;
  }>();
  const { docChange, collectionFormId, documentId } =
    useCollectionFormContext();
  const restrictedFileTypes = fileFormatsToMimeType(["spreadsheet"]);
  const [transientError, setTransientError] = useState<string | undefined>();
  const validateFileSize = useCallback(
    (file: File) => {
      const maxFileSizeBytes = MAX_FILE_SIZE_MB * 1024 * 1024;
      if (file.size > maxFileSizeBytes) {
        setTransientError(
          d(
            "errors.configurationForm.question.files.maxFileSize.fileTooLarge",
            { maxFileSize: MAX_FILE_SIZE_MB }
          )
        );
        return false;
      }
      return true;
    },
    [d]
  );

  const questionDisabled = useMemo(
    () => question.properties?.disabled || disabled,
    [question.properties?.disabled, disabled]
  );

  const downloadDataFile = useCallback(async () => {
    if (!collectionFormId) {
      return;
    }

    pushToastNotification(
      d("ui.forms.question.table.notifications.download.started.heading"),
      d("ui.forms.question.table.notifications.download.started.description"),
      ToastNotificationVariant.INFO
    );

    await downloadTableData(
      collectionFormId,
      question.id,
      question.identifier
    ).catch(() => {
      pushToastNotification(
        d("ui.forms.question.table.notifications.download.failed.heading"),
        d("ui.forms.question.table.notifications.download.failed.description"),
        ToastNotificationVariant.DANGER
      );
    });
  }, [
    pushToastNotification,
    d,
    collectionFormId,
    question.id,
    question.identifier
  ]);

  const onChange = useCallback(
    (_: string | string[], fileList: FileList | null) => {
      setTransientError(undefined);
      if (!collectionFormId) {
        return;
      }
      if (!fileList) {
        alert(d("errors.configurationForm.question.files.noFilesSelected"));
        return;
      }

      for (const file of fileList) {
        if (!validateFileSize(file)) {
          return;
        }
      }

      // NB: We expect at most 1 file
      const newFiles = Array.from(fileList);
      newFiles.forEach(file => {
        pushToastNotification(
          d("ui.forms.question.table.notifications.upload.started.heading"),
          d("ui.forms.question.table.notifications.upload.started.description"),
          ToastNotificationVariant.INFO
        );

        uploadTableDataForPrefill(collectionFormId, question.id, file)
          .then(({ status }) => {
            if (status === 500) {
              throw new Error("Failed to process file");
            }

            pushToastNotification(
              d("ui.forms.question.table.notifications.upload.success.heading"),
              d(
                "ui.forms.question.table.notifications.upload.success.description"
              ),
              ToastNotificationVariant.SUCCESS
            );
          })
          .catch(() => {
            pushToastNotification(
              d("ui.forms.question.table.notifications.upload.failed.heading"),
              d(
                "ui.forms.question.table.notifications.upload.failed.description"
              ),
              ToastNotificationVariant.DANGER
            );
          });
      });
      return true;
    },
    [collectionFormId, validateFileSize, question.id, d, pushToastNotification]
  );

  const questionTableHelper = useMemo(() => {
    return new QuestionTableHelper(question);
  }, [question]);

  const createEmptyAnswerRow = useCallback(() => {
    return addItem(questionTableHelper.createNewRow(), {
      entities: {},
      order: []
    });
  }, [questionTableHelper]);

  useEffect(() => {
    // if there are no rows on the table answer yet, create the first one
    if (docChange) {
      docChange(d => {
        const v = d.answers[question.id]?.value as TableAnswer;
        if (!v?.order?.length && question.properties) {
          const resource = createEmptyAnswerRow();
          d.answers[question.id] = {
            questionId: question.id,
            value: resource,
            type: question.type
          };
        }
      });
    }
  }, [docChange, createEmptyAnswerRow, question]);

  const displayColumnQuestions = useMemo(() => {
    return question.properties?.columns?.filter(c => !c.properties?.hidden);
  }, [question.properties?.columns]);

  const newValue = useMemo(() => {
    if (showPreviewAnswerRow) {
      return createEmptyAnswerRow();
    }

    if (!answer?.value || !Array.isArray(answer.value.order)) {
      return [];
    }
    const rows: TableFieldValue = mapOverResource(answer?.value, row => {
      const rowData: TableFieldRowValue = {};

      // Map each column value to corresponding questionId
      if (row.columns) {
        Object.entries(row.columns).forEach(([questionId, columnData]) => {
          rowData[questionId] = columnData.value;
        });
      }

      return rowData;
    });

    return rows as TableFieldValue;
  }, [showPreviewAnswerRow, createEmptyAnswerRow, answer?.value]);

  const newColumns = useMemo(() => {
    return displayColumnQuestions?.map(column => ({
      label: column.text,
      name: column.text,
      id: column.id,
      type: column.type,
      properties: {
        ...column.properties
      },
      highlightComponents: (
        <>
          <HighlightAnnotationLocation
            location={{
              variant: "question",
              questionId: question.id,
              columnId: column.id
            }}
          >
            <LabelWithHighlight
              className="table-question__table__header__cell__label"
              label={column?.text}
              required={column?.properties?.required}
              description={column?.description}
            />
          </HighlightAnnotationLocation>
          <CollectionQuestionBlockAlert
            location={{
              variant: "question",
              questionId: question.id,
              columnId: column.id
            }}
            stylingVariant="cellIndicator"
            floatingAnnotationPosition="bottom-left"
          />
          <CollectionQuestionBlockComment
            location={{
              variant: "question",
              questionId: question.id,
              columnId: column.id
            }}
            stylingVariant="cellIndicator"
            floatingAnnotationPosition="bottom-left"
          />
        </>
      ),
      customCellRenderer: ({
        value,
        disabled,
        rowIndex
      }: {
        value: TableFieldCellValue | undefined;
        disabled?: boolean;
        rowIndex: number;
      }) => {
        const rowId = answer?.value?.order?.[rowIndex];
        return (
          <QuestionAnswer
            question={column}
            disabled={
              disabled ||
              column.properties?.disabled ||
              question.properties?.disabled
            }
            answer={value as FormAnswer["value"]}
            answerAccessor={`${answerAccessor}.entities.${rowId}.columns.${column.id}.value`}
            location={{
              variant: "answer",
              questionId: question.id,
              rowId,
              columnId: column.id
            }}
          />
        );
      }
    }));
  }, [displayColumnQuestions, answer, answerAccessor, question]);

  const newRemoveTableRow = useCallback(
    (rowIndex: number) => {
      if (docChange) {
        docChange(d => {
          // const rowId = questionTableHelper.rowId(accessor);
          const questionAnswers = getByPath<Resource<TableAnswerRow>>(
            d,
            answerAccessor.split(".")
          );
          const rowId = questionAnswers?.order?.[rowIndex];

          const updatedTable = removeItem(
            rowId,
            JSON.parse(JSON.stringify(questionAnswers))
          );
          updateAnswer(
            question,
            updatedTable as TableAnswer,
            answerAccessor,
            documentId
          );
        });
      }
    },
    [answerAccessor, question, documentId, docChange]
  );

  const newAddTableRow: TableFieldSetRow = useCallback(
    (
      rowValue = {},
      rowIndex = (newValue as TableFieldValue)?.length,
      options = {}
    ) => {
      if (docChange) {
        docChange(d => {
          const questionAnswers = getByPath<Resource<TableAnswerRow>>(
            d,
            answerAccessor.split(".")
          );

          if (!questionAnswers || !question.properties) {
            console.error("Path not found", answerAccessor);
            return d;
          }

          // Create and add new row to OrderedMap
          const newRow = questionTableHelper.createNewRow();
          // addItem(newRow, questionAnswers);

          // Get current order length
          const currentLength = questionAnswers.order?.length ?? 0;
          const targetIndex = rowIndex ?? currentLength;

          // Handle row placement
          if (options?.keepOtherRowValues && targetIndex < currentLength) {
            // Merge with existing row
            const existingId = questionAnswers.order[targetIndex];
            questionAnswers.entities[existingId] = {
              ...questionAnswers.entities[existingId],
              ...rowValue
            };
          } else {
            // Add new row at index
            const newRowId = newRow.id;
            questionAnswers.order.splice(targetIndex, 0, newRowId);
            questionAnswers.entities[newRowId] = newRow;
          }

          return questionAnswers;
        });
      }
    },
    [answerAccessor, questionTableHelper, question, docChange, newValue]
  );

  const setCell = useCallback(
    (
      cellValue: TableFieldCellValue | undefined,
      rowIndex: number,
      columnId: TableFieldColumnId
    ) => {
      if (docChange) {
        docChange(doc => {
          const questionAnswers = getByPath<Resource<TableAnswerRow>>(
            doc,
            answerAccessor.split(".")
          );

          if (!questionAnswers || !questionAnswers.order) {
            console.error(
              "Path not found or invalid structure",
              answerAccessor
            );
          }

          // Get row ID from order array
          const rowId = questionAnswers.order[rowIndex];
          if (!rowId) {
            console.error("Row not found at index", rowIndex);
          }

          // Update cell value in entities
          if (
            questionAnswers.entities[rowId]?.columns?.[columnId]?.value !==
            cellValue
          ) {
            questionAnswers.entities[rowId] = {
              ...questionAnswers.entities[rowId],
              columns: {
                ...questionAnswers.entities[rowId].columns,
                [columnId]: {
                  questionId: columnId,
                  type:
                    displayColumnQuestions?.find(q => q.id === columnId)
                      ?.type ?? "text",
                  value: cellValue as FormAnswer["value"]
                }
              }
            };
          }
        });
      }
    },
    [answerAccessor, displayColumnQuestions, docChange]
  );

  const setTable: TableFieldSetTable = useCallback(
    (valueToSet: TableFieldValue, options: { fromRowIndex?: number }) => {
      if (docChange) {
        docChange(doc => {
          const questionAnswers = getByPath<Resource<TableAnswerRow>>(
            doc,
            answerAccessor.split(".")
          );

          if (!questionAnswers || !questionAnswers.order) {
            console.error(
              "Path not found or invalid structure",
              answerAccessor
            );
            return;
          }

          const fromRowIndex = options.fromRowIndex ?? 0;
          const maxRows = Math.max(
            questionAnswers.order.length,
            valueToSet.length + fromRowIndex
          );

          // Process each row
          for (let rowIndex = 0; rowIndex < maxRows; rowIndex++) {
            const existingRowId = questionAnswers.order[rowIndex];
            const newRowData = valueToSet[rowIndex - fromRowIndex];

            if (rowIndex < fromRowIndex) {
              // Skip rows before fromRowIndex
              continue;
            }

            if (newRowData && existingRowId) {
              // Update existing row
              Object.entries(newRowData).forEach(([columnId, cellValue]) => {
                if (questionAnswers.entities[existingRowId]) {
                  questionAnswers.entities[existingRowId].columns = {
                    ...questionAnswers.entities[existingRowId].columns,
                    [columnId]: {
                      questionId: columnId,
                      type:
                        displayColumnQuestions?.find(q => q.id === columnId)
                          ?.type ?? "text",
                      value: cellValue as FormAnswer["value"]
                    }
                  };
                }
              });
            } else if (newRowData && !existingRowId) {
              // Add new row
              const newRow = questionTableHelper.createNewRow();
              const newRowId = newRow.id;
              questionAnswers.order.splice(rowIndex, 0, newRowId);

              // Set column values for new row
              Object.entries(newRowData).forEach(([columnId, cellValue]) => {
                newRow.columns[columnId] = {
                  questionId: columnId,
                  type:
                    displayColumnQuestions?.find(q => q.id === columnId)
                      ?.type ?? "text",
                  value: cellValue as FormAnswer["value"]
                };
              });

              questionAnswers.entities[newRowId] = newRow;
            }
          }
        });
      }
    },
    [answerAccessor, displayColumnQuestions, docChange, questionTableHelper]
  );

  const setColumn: TableFieldSetColumn = useCallback(
    (
      columnValues: TableFieldCellValue[],
      columnId: TableFieldColumnId,
      options?: {
        keepOtherColumnValues?: boolean;
        fromRowIndex?: number;
      }
    ) => {
      if (docChange) {
        docChange(doc => {
          const questionAnswers = getByPath<Resource<TableAnswerRow>>(
            doc,
            answerAccessor.split(".")
          );

          if (!questionAnswers || !questionAnswers.order) {
            console.error(
              "Path not found or invalid structure",
              answerAccessor
            );
            return;
          }

          const fromRowIndex = options?.fromRowIndex ?? 0;
          const maxRows = Math.max(
            questionAnswers.order.length,
            columnValues.length + fromRowIndex
          );

          // Process each row
          for (let rowIndex = 0; rowIndex < maxRows; rowIndex++) {
            if (rowIndex < fromRowIndex) {
              continue;
            }

            const columnValue = columnValues[rowIndex - fromRowIndex];
            let rowId = questionAnswers.order[rowIndex];

            // Create new row if it doesn't exist
            if (!rowId && columnValue !== undefined) {
              const newRow = questionTableHelper.createNewRow();
              rowId = newRow.id;
              questionAnswers.order.splice(rowIndex, 0, rowId);
              questionAnswers.entities[rowId] = newRow;
            }

            if (rowId && questionAnswers.entities[rowId]) {
              if (columnValue === undefined) {
                if (!options?.keepOtherColumnValues) {
                  // Remove the column value
                  delete questionAnswers.entities[rowId].columns[columnId];
                }
              } else {
                // Set the column value
                questionAnswers.entities[rowId].columns[columnId] = {
                  questionId: columnId,
                  type:
                    displayColumnQuestions?.find(q => q.id === columnId)
                      ?.type ?? "text",
                  value: columnValue as FormAnswer["value"]
                };
              }
            }
          }
        });
      }
    },
    [answerAccessor, displayColumnQuestions, docChange, questionTableHelper]
  );

  const reorderRow: TableFieldReorderRow = useCallback(
    (rowIndexToMove: number, toRowIndex: number) => {
      if (docChange) {
        docChange(doc => {
          const questionAnswers = getByPath<Resource<TableAnswerRow>>(
            doc,
            answerAccessor.split(".")
          );

          if (!questionAnswers?.order) {
            console.error("Invalid question answers structure");
            return doc;
          }

          // Get row ID to move
          const rowIdToMove = questionAnswers.order[rowIndexToMove];
          if (!rowIdToMove) {
            console.error("Row not found at index", rowIndexToMove);
            return doc;
          }

          // Remove from current position and insert at new position
          questionAnswers.order.splice(rowIndexToMove, 1);
          questionAnswers.order.splice(toRowIndex, 0, rowIdToMove);

          return doc;
        });
      }
    },
    [docChange, answerAccessor]
  );

  return (
    <Stack className="table-question__container">
      <TableField
        id="tableListField"
        name="table-list-field"
        value={newValue as TableFieldValue}
        setTable={setTable}
        setRow={newAddTableRow}
        removeRow={newRemoveTableRow}
        setColumn={setColumn}
        setCell={setCell}
        reorderRow={reorderRow}
        columns={(newColumns as TableFieldColumns) ?? []}
      />
      <Inline spaceBetween alignment="left">
        <Inline gap="150">
          {!questionDisabled && (
            <FileUpload
              multiple={false}
              acceptableTypes={restrictedFileTypes}
              error={transientError}
              onChange={onChange}
              trigger={({ onClick }) => (
                <Button
                  variant="text"
                  leftIcon={{ name: "upload", fillStyle: IconFillStyle.FILLED }}
                  onClick={onClick}
                  label={d("ui.forms.question.table.upload")}
                />
              )}
            />
          )}
          <Button
            variant="text"
            leftIcon={{ name: "download", fillStyle: IconFillStyle.FILLED }}
            onClick={downloadDataFile}
            label={d("ui.forms.question.table.download")}
          />
        </Inline>
      </Inline>
    </Stack>
  );
};

QuestionTableAnswer.displayName = "QuestionTableAnswer";
