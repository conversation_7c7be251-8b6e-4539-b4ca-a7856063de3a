.collection-question-block {
  border-bottom: 1px solid var(--color-border);
  position: relative;

  & .collection-question-block-comment-floating {
    visibility: hidden;
    opacity: 0;
    &:has(.collection-question-block-comment--open),
    &:has(.collection-question-block-comment--with-annotations) {
      visibility: visible;
      opacity: 1;
    }
  }

  &:hover,
  &:focus-within {
    & .collection-question-block-comment-floating {
      visibility: visible;
      opacity: 1;
    }
  }
  &--selected {
    background-color: var(--color-colorLightest);
  }
  &__status {
    position: absolute;
    top: 0;
    margin-left: -2px;
    height: calc(100% + 1px);
  }

  &__answer {
    padding: var(--spacing-050) 0;
  }

  &__actions {
    transition: opacity 0.15s;
    opacity: 0;
    visibility: hidden;

    & .icon-button {
      color: var(--color-text-secondary);
    }
  }

  .collection-question-block__data__cell {
    position: relative;
  }

  &__label {
    align-items: left;
    justify-content: center;
    box-sizing: content-box;
    // padding: var(--spacing-100) 0;
  }

  & .highlight-location--highlighted {
    & .text-field__value,
    & .number-field__value,
    & .select,
    & .text-area-field__value,
    & .date-picker__value,
    & .drag-and-drop-file {
      background-color: transparent !important;
    }
  }

  &--type-table,
  &--type-json {
    border-bottom: 0 solid transparent;

    & .collection-question-block__data__cell {
      padding: var(--spacing-075) 0;
    }
  }

  &__json {
    border-bottom: 1px solid var(--color-border);
    .collection-question-block {
      border-bottom: 0 solid transparent;
      &__td {
        overflow: visible !important;
      }
    }
  }

  &__table {
    border-bottom: 1px solid var(--color-border);
    position: relative;
    border-collapse: collapse;
    overflow: auto;

    .highlight {
      background-color: var(--color-traffic-warningSurface);
    }
  }

  &__td {
    padding-right: var(--spacing-100);
    width: calc(40vw - 30px);
    max-width: 0;
    overflow: auto;
    overscroll-behavior-x: contain;
  }

  &__data {
    &__cell {
      &:last-child {
        padding-right: var(--components-table-cell-padding-horizontal, 6px);
      }
    }
  }
}
