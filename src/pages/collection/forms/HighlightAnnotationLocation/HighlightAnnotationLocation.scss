@mixin highlight_color($color) {
  background-color: var(
    --components-whiteboard-sticky-note-color-#{$color},
    #fff171
  );
  border-collapse: collapse;
  border: 2px dashed transparent;

  &:has(mark) {
    background-color: transparent;
    mark {
      background-color: var(
        --components-whiteboard-sticky-note-color-#{$color},
        #fff171
      );
    }
  }
  // If we want the highlight on the actual text instead of the input box - keeping incase
  // &:has(.text-field__value),
  // &:has(.number-field__value),
  // &:has(.date-picker__value),
  // &:has(.text-area__value),
  // &:has(.select__input) {
  //   background-color: transparent;
  //   & input.text-field__input,
  //   & .number-field__display-value,
  //   & input.number-field__input,
  //   & input.date-picker__input,
  //   & .select__value,
  //   & .select__value--absolute {
  //     background-color: var(
  //       --components-whiteboard-sticky-note-color-#{$color},
  //       #fff171
  //     );
  //     border-radius: 2px;
  //     // width: fit-content !important;
  //     field-sizing: content !important;
  //     // highlight content that says "upload"
  //   }
  // }
}

.highlight-location {
  border: 2px dashed var(--color-transparent);
  transition:
    background-color 0.3s ease,
    border 0.3s ease;

  border-radius: 2px;

  & .floating-annotations {
    bottom: anchor(top);
  }

  & .badge {
    --components-badge-default-color-background: var(
      --components-tooltip-color-background
    );
    --components-badge-size: 12px;
    --components-badge-font-size: var(--font-size-body-xs);

    & .icon {
      color: var(--components-status-circle-comment-color-icon, #009aff);
    }
  }

  &.highlight-location--hug-container {
    padding: 0;
    margin: 0;
    border-radius: var(--components-inputs-border-radius, 8px);
  }

  mark {
    background-color: transparent;
  }
  &--highlighted {
    width: fit-content;
  }

  &__highlight-box {
    z-index: 5;
    cursor: pointer;
    &:hover {
      &:not(.highlight-location--highlighted) {
        border: 2px dashed var(--color-focus);
      }
      border: 2px dashed var(--color-focus);

      &:has(mark) {
        border: 2px dashed transparent;
        & mark {
          outline: 2px dashed var(--color-focus);
        }
      }
    }
  }

  &.highlight-location--color {
    &-1 {
      @include highlight_color(1);
    }
    &-2 {
      @include highlight_color(2);
    }
    &-3 {
      @include highlight_color(3);
    }
    &-4 {
      @include highlight_color(4);
    }
    &-5 {
      @include highlight_color(5);
    }
  }

  .floating-highlight-annotation-location-indicator {
    $size: 24px;
    width: fit-content;
    &__content {
      z-index: 19;
      width: $size;
      height: $size;
      cursor: pointer;
    }

    &__icon {
      width: $size;
      height: $size;
      background: var(--color-surface-secondary);
      border: 1px solid var(--color-border);
      border-radius: 50%;
      box-shadow: 0px 3px 6px rgba(39, 52, 75, 0.2);
    }
  }
}
