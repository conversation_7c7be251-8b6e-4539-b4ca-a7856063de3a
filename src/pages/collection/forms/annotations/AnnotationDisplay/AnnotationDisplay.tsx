import React, { useMemo, useState } from "react";

import {
  Attachment,
  AttachmentGroup,
  Avatar,
  Box,
  Button,
  ConfirmationModal,
  Divider,
  DropdownItem,
  DropdownItemGroup,
  FloatingWithParentPosition,
  Icon,
  IconButton,
  Inline,
  KebabMenu,
  Link,
  ModalDialog,
  Overlay,
  Pill,
  Stack,
  StatusCircle,
  Text,
  Tooltip,
  getClassNames,
  getUserDisplayName
} from "@oneteam/onetheme";

import { formatDate } from "@pages/collection/flows/FlowExecutionHelper";
import { useWorkspaceUsers } from "@pages/settings/permissions/WorkspaceUsersContext/WorkspaceUsersContext";

import { AuthUser } from "@src/authentication/AuthContext";
import logo from "@src/components/shared/Navigation/logo.png";
import { useDictionary } from "@src/hooks/useDictionary";
import { ActorType, UserActor } from "@src/types/Actor";
import {
  Annotation,
  AnnotationAttachment,
  AnnotationResolvedStatus,
  AnnotationV<PERSON>t,
  ChangeResolveAnnotationHandler,
  CreateAnnotationHandler,
  DeleteAnnotationHandler,
  GetAnnotationLocationDisplay,
  UpdateAnnotationHandler,
  alertTypeToStatusLine
} from "@src/types/Annotation";
import { WorkspaceUserStatus } from "@src/types/WorkspaceUser";
import { CollectionFormSummaryModalTab } from "@src/types/collection/CollectionForm";

import { useCollectionFormContext } from "../../CollectionFormContext";
import { AnnotationMessageField } from "../AnnotationMessageField/AnnotationMessageField";
import "./AnnotationDisplay.scss";

export const AnnotationDisplay = ({
  elementId,
  annotation,
  user,
  parentAnnotationId,
  isReply = false,
  onCreateAnnotation,
  onUpdateAnnotation,
  onDeleteAnnotation,
  onChangeResolveAnnotation,
  getAnnotationLocationDisplay,
  hideAnnotationLocationDisplay
}: {
  elementId: string;
  annotation: Annotation;
  user: AuthUser | null;
  parentAnnotationId?: string;
  isReply?: boolean;

  // handlers
  onCreateAnnotation?: CreateAnnotationHandler;
  onUpdateAnnotation?: UpdateAnnotationHandler;
  onDeleteAnnotation?: DeleteAnnotationHandler;
  onChangeResolveAnnotation?: ChangeResolveAnnotationHandler;

  getAnnotationLocationDisplay?: GetAnnotationLocationDisplay;
  hideAnnotationLocationDisplay?: boolean; // Hide the name at the top of the annotation display
}) => {
  const { summaryModal } = useCollectionFormContext();
  const d = useDictionary();
  const [kebabMenuOpen, setKebabMenuOpen] = useState(false);
  const [repliesExpanded, setRepliesExpanded] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  const { workspaceUsersById } = useWorkspaceUsers();

  const annotationWorkspaceUser = useMemo(() => {
    if (annotation.createdBy?.type === ActorType.USER) {
      return workspaceUsersById?.[(annotation.createdBy as UserActor).userId];
    }
    return undefined;
  }, [annotation.createdBy, workspaceUsersById]);

  const confirmDeleteModal = useMemo(() => {
    if (!isDeleting) {
      return null;
    }

    return (
      <ModalDialog isOpen>
        <Overlay isOpen />
        <ConfirmationModal
          closeOnClickOutside
          variant="danger"
          heading={d("ui.workspace.annotations.deleteModal.title")}
          message={d("ui.workspace.annotations.deleteModal.message")}
          onConfirm={() => {
            if (isDeleting) {
              setIsDeleting(false);
              onDeleteAnnotation?.({
                annotationId: annotation.id,
                parentAnnotationId
              });
            }
          }}
          confirmLabel={d("ui.workspace.annotations.deleteModal.confirmLabel")}
          onCancel={() => {
            setIsDeleting(false);
          }}
        />
      </ModalDialog>
    );
  }, [isDeleting, d, onDeleteAnnotation, annotation.id, parentAnnotationId]);

  const isFromCurrentUser = useMemo(() => {
    if (!user?.id || annotation.createdBy?.type === ActorType.FLOW) {
      return false;
    }
    return user.id === annotation.createdBy.userId;
  }, [user, annotation.createdBy]);

  const avatarDisplay = useMemo(() => {
    if (annotation.variant === "alert") {
      return (
        <StatusCircle
          className="alert-avatar-display"
          variant={alertTypeToStatusLine[annotation?.type]}
        />
      );
    }
    if (isFromCurrentUser) {
      return (
        <Avatar
          user={{
            firstName: user?.firstName ?? "",
            lastName: user?.lastName ?? "",
            image: user?.image ?? ""
          }}
          size="s"
        />
      );
    }

    if (annotation.createdBy?.type === ActorType.USER) {
      return (
        <Avatar
          user={
            annotationWorkspaceUser
              ? {
                  firstName:
                    annotationWorkspaceUser.user?.properties?.firstName ?? "",
                  lastName:
                    annotationWorkspaceUser.user?.properties?.lastName ?? "",
                  image: ""
                }
              : undefined
          }
          size="s"
        />
      );
    } else if (annotation.createdBy?.type === ActorType.FLOW) {
      return (
        <Box
          key={annotation.id}
          alignment="center"
          style={{
            width: "var(--components-avatar-s-size, 24px)",
            height: "var(--components-avatar-s-size, 24px)",
            borderRadius: "var(--components-avatar-xl-size, 100%)",
            backgroundColor: "var(--color-surface-secondary)"
          }}
        >
          <img
            src={logo}
            alt={d("ui.navigation.altOneTeamAILogo")}
            style={{ width: "var(--components-avatar-xs-size, 16px)" }}
          />
        </Box>
      );
    }
  }, [user, annotation, d, isFromCurrentUser, annotationWorkspaceUser]);

  const nameDisplay = useMemo(() => {
    if (annotation.variant === "alert") {
      return d(`ui.workspace.annotations.alerts.type.${annotation.type}.label`);
    }
    if (isFromCurrentUser) {
      return `${user?.firstName} ${user?.lastName}`;
    }
    if (annotation.createdBy?.type === ActorType.USER) {
      return getUserDisplayName({
        firstName: annotationWorkspaceUser?.user?.properties?.firstName,
        lastName: annotationWorkspaceUser?.user?.properties?.lastName,
        email: annotationWorkspaceUser?.user?.email
      });
    } else if (annotation.createdBy?.type === ActorType.FLOW) {
      return "OneTeam Internal";
    }
  }, [
    annotation,
    isFromCurrentUser,
    d,
    user?.firstName,
    user?.lastName,
    annotationWorkspaceUser
  ]);

  const repliesCount = useMemo(() => {
    return Object.keys(annotation.replies ?? {}).length;
  }, [annotation.replies]);

  return (
    <>
      <Stack>
        <Inline
          id={elementId}
          className="annotation-display"
          gap="075"
          width="100"
          padding="100"
          onClick={() => {
            if (!isReply) {
              setRepliesExpanded(current => !current);
            }
          }}
        >
          <Stack gap="025" alignment="top-center">
            {avatarDisplay}
          </Stack>
          <Stack gap="050" width="100">
            <Inline gap="050">
              {isEditing && onUpdateAnnotation ? (
                <AnnotationMessageField
                  id={`${elementId}-message-field-${annotation.id}`}
                  defaultMessage={annotation.message}
                  user={user}
                  onSave={message => {
                    onUpdateAnnotation({
                      annotation: {
                        ...annotation,
                        message
                      }
                    });
                    setIsEditing(false);
                  }}
                  isEditing
                  onCancel={() => {
                    setIsEditing(false);
                  }}
                />
              ) : (
                <Stack
                  className="annotation-display__content"
                  gap="050"
                  width="100"
                >
                  <Stack className="annotation-display__header">
                    <Inline
                      width="100"
                      alignment="left"
                      gap="050"
                      className="annotation-display__user-info"
                      spaceBetween
                    >
                      <Inline
                        alignment="left"
                        wrap
                        style={{
                          gap: "0 var(--spacing-025)"
                        }}
                      >
                        <Text
                          weight="medium"
                          style={{
                            minWidth: "max-content"
                          }}
                          className="annotation-display__name"
                        >
                          {nameDisplay}
                        </Text>
                        {annotationWorkspaceUser?.status ===
                          WorkspaceUserStatus.INACTIVE && (
                          <Text
                            weight="regular"
                            size="s"
                            color="text-tertiary"
                            style={{
                              minWidth: "max-content"
                            }}
                            className="annotation-display__created-at"
                            alignment="left"
                          >
                            (
                            {d(`ui.settings.permissions.status.INACTIVE.label`)}
                            )
                          </Text>
                        )}
                        <Text
                          weight="regular"
                          size="xs"
                          color="text-tertiary"
                          style={{
                            minWidth: "max-content"
                          }}
                          className="annotation-display__created-at"
                          alignment="left"
                        >
                          {annotation.createdAt
                            ? formatDate(annotation.createdAt)
                            : ""}
                        </Text>
                      </Inline>
                      <Inline alignment="left" gap="150">
                        {!isEditing && (!isReply || isFromCurrentUser) && (
                          <AnnotationDisplayKebabMenu
                            isFromCurrentUser={isFromCurrentUser}
                            isReply={isReply}
                            kebabMenuOpen={kebabMenuOpen}
                            setKebabMenuOpen={setKebabMenuOpen}
                            setIsEditing={
                              onUpdateAnnotation ? setIsEditing : undefined
                            }
                            setIsDeleting={
                              onDeleteAnnotation ? setIsDeleting : undefined
                            }
                            setRepliesExpanded={setRepliesExpanded}
                            onChangeResolveAnnotation={
                              onChangeResolveAnnotation
                            }
                          />
                        )}
                        {!isReply &&
                          (annotation.resolved?.status ===
                          AnnotationResolvedStatus.RESOLVED ? (
                            <IconButton
                              label={d(
                                "ui.workspace.annotations.actions.markAsUnresolved"
                              )}
                              className={getClassNames([
                                "annotation-display__resolve-button",
                                "annotation-display__resolve-button--resolved"
                              ])}
                              name="check_circle"
                              color="traffic-success"
                              fillStyle="filled"
                              tooltipPosition="bottom-right"
                              onClick={e => {
                                onChangeResolveAnnotation?.({
                                  annotationId: annotation.id,
                                  parentAnnotationId,
                                  isResolved:
                                    annotation.resolved?.status !==
                                    AnnotationResolvedStatus.RESOLVED
                                });
                                e.stopPropagation();
                              }}
                            />
                          ) : (
                            <IconButton
                              label={d(
                                "ui.workspace.annotations.actions.markAsResolved"
                              )}
                              className={getClassNames([
                                "annotation-display__resolve-button",
                                "annotation-display__resolve-button--unresolved"
                              ])}
                              name="check_circle"
                              color="text-primary"
                              tooltipPosition="bottom-right"
                              onClick={e => {
                                onChangeResolveAnnotation?.({
                                  annotationId: annotation.id,
                                  parentAnnotationId,
                                  isResolved:
                                    annotation.resolved?.status !==
                                    AnnotationResolvedStatus.RESOLVED
                                });
                                e.stopPropagation();
                              }}
                            />
                          ))}
                      </Inline>
                    </Inline>

                    {!hideAnnotationLocationDisplay &&
                      annotation.location &&
                      getAnnotationLocationDisplay && (
                        <Box width="fit">
                          <Link
                            onClick={e => {
                              e.stopPropagation();
                              summaryModal.setLocation(annotation.location);
                              if (
                                summaryModal.tab === "none" ||
                                summaryModal.tab !==
                                  CollectionFormSummaryModalTab.ALL
                              ) {
                                summaryModal.setTab(
                                  CollectionFormSummaryModalTab.ALL
                                );
                              }
                            }}
                          >
                            <Text
                              weight="regular"
                              size="s"
                              color="text-tertiary"
                              className="annotation-display__location"
                              alignment="left"
                            >
                              {getAnnotationLocationDisplay(
                                annotation.location
                              )}
                            </Text>
                          </Link>
                        </Box>
                      )}
                  </Stack>
                  {annotation.variant === AnnotationVariant.ALERT &&
                    annotation.groupIdentifier && (
                      <Pill
                        className="annotation-display__alert-group"
                        label={annotation.groupIdentifier}
                      />
                    )}
                  {annotation.variant === AnnotationVariant.HIGHLIGHT && (
                    <Box
                      className="annotation-display__highlight-color"
                      style={{
                        width: "var(--spacing-600)",
                        height: "var(--spacing-200)",
                        backgroundColor: `var(--components-whiteboard-sticky-note-color-${annotation.color})`
                      }}
                    />
                  )}
                  <Tooltip
                    content={
                      annotation.createdAt !== annotation.updatedAt
                        ? `${d("ui.workspace.annotations.edited")} ${formatDate(annotation.updatedAt)}`
                        : undefined
                    }
                  >
                    <Box onClick={e => e.stopPropagation()}>
                      <Text
                        className="annotation-display__message"
                        weight="regular"
                        alignment="left"
                        color="text-primary"
                      >
                        {annotation.message}
                        {annotation.createdAt !== annotation.updatedAt && (
                          <span className="annotation-display__edited">
                            ({d("ui.workspace.annotations.edited")})
                          </span>
                        )}
                      </Text>
                    </Box>
                  </Tooltip>
                  {(annotation.attachments?.length ?? 0) > 0 && (
                    <AttachmentGroup
                      className="annotation-display__attachments"
                      width="fit"
                    >
                      {annotation.attachments?.map(
                        (attachment: AnnotationAttachment, i: number) => (
                          <Attachment
                            key={`${annotation.id}-attachment-${i}`}
                            className="annotation-display__attachment-item"
                            width="fit"
                            file={{
                              name: attachment.name
                            }}
                            // TODO: implement download
                            onClickDownload={() => {
                              console.log("Download attachment");
                            }}
                          />
                        )
                      )}
                    </AttachmentGroup>
                  )}
                </Stack>
              )}
            </Inline>
            {repliesCount > 0 && (
              <Box
                alignment="left"
                className="annotation-display__replies-button"
              >
                <Button
                  label={d(
                    `ui.workspace.annotations.replies.${repliesCount > 1 ? "many" : "one"}`,
                    {
                      count: repliesCount
                    }
                  )}
                  variant="text"
                  onClick={() => {
                    setRepliesExpanded(current => !current);
                  }}
                  rightIcon={{
                    name: repliesExpanded ? "expand_less" : "expand_more"
                  }}
                />
              </Box>
            )}
          </Stack>
        </Inline>
        {repliesExpanded && (
          <Stack
            className="annotation-display__replies"
            width="100"
            style={{
              paddingLeft:
                "calc((var(--spacing-100)) * 2 + var(--components-avatar-s-size)"
            }}
          >
            {Object.values(annotation.replies ?? {}).map(reply => (
              <>
                <Divider size="small" />
                <AnnotationDisplay
                  elementId={`${elementId}-reply-${reply.id}`}
                  isReply
                  key={`${reply.id}`}
                  user={user}
                  annotation={reply}
                  parentAnnotationId={annotation.id}
                  onDeleteAnnotation={({ annotationId }) => {
                    onDeleteAnnotation?.({
                      annotationId,
                      parentAnnotationId: annotation.id
                    });
                  }}
                  onCreateAnnotation={({ annotation: newAnnotation }) => {
                    return onCreateAnnotation?.({
                      annotation: newAnnotation,
                      parentAnnotationId: annotation.id
                    });
                  }}
                  onUpdateAnnotation={({ annotation: updatingAnnotation }) => {
                    onUpdateAnnotation?.({
                      annotation: updatingAnnotation,
                      parentAnnotationId: annotation.id
                    });
                  }}
                  getAnnotationLocationDisplay={getAnnotationLocationDisplay}
                />
              </>
            ))}
            {onCreateAnnotation && (
              <>
                <Divider size="small" />
                <Box padding="100">
                  <AnnotationMessageField
                    id={`${elementId}-reply-message-field`}
                    user={user}
                    onSave={message => {
                      onCreateAnnotation({
                        annotation: {
                          variant: AnnotationVariant.COMMENT,
                          message,
                          attachments: []
                        },
                        parentAnnotationId: annotation.id
                      });
                      setRepliesExpanded(true);
                    }}
                  />
                </Box>
              </>
            )}
          </Stack>
        )}
      </Stack>
      {confirmDeleteModal}
    </>
  );
};

const AnnotationDisplayKebabMenu = ({
  isFromCurrentUser,
  isReply,
  kebabMenuOpen,
  setKebabMenuOpen,
  setIsEditing,
  setIsDeleting,
  setRepliesExpanded,
  onChangeResolveAnnotation
}: {
  isFromCurrentUser: boolean;
  isReply?: boolean;
  kebabMenuOpen: boolean;
  setKebabMenuOpen: (open: boolean) => void;
  setIsEditing?: (editing: boolean) => void;
  setIsDeleting?: (deleting: boolean) => void;
  setRepliesExpanded?: (expanded: boolean) => void;
  onChangeResolveAnnotation?: ChangeResolveAnnotationHandler;
}) => {
  const d = useDictionary();
  return (
    <KebabMenu
      className={getClassNames([
        "annotation-display__kebab-menu",
        `annotation-display__kebab-menu--${kebabMenuOpen ? "open" : "closed"}`
      ])}
      isOpen={kebabMenuOpen}
      onOpenChange={setKebabMenuOpen}
      position={FloatingWithParentPosition.BOTTOM_RIGHT}
    >
      {!isReply && setRepliesExpanded && (
        <DropdownItemGroup>
          {setRepliesExpanded && (
            <DropdownItem
              leftElement={<Icon name="reply" />}
              onClick={() => {
                setRepliesExpanded(true);
                setKebabMenuOpen(false);
              }}
            >
              {d("ui.workspace.annotations.actions.reply")}
            </DropdownItem>
          )}
        </DropdownItemGroup>
      )}
      {isFromCurrentUser && (setIsEditing || setIsDeleting) && (
        <DropdownItemGroup
          hasDivider={
            !isReply && (!!setRepliesExpanded || !!onChangeResolveAnnotation)
          }
        >
          {setIsEditing && (
            <DropdownItem
              leftElement={<Icon name="edit" />}
              onClick={() => {
                setKebabMenuOpen(false);
                setIsEditing(true);
              }}
            >
              {d("ui.workspace.annotations.actions.update")}
            </DropdownItem>
          )}
          {setIsDeleting && (
            <DropdownItem
              leftElement={<Icon name="delete" color="traffic-danger" />}
              onClick={() => {
                setKebabMenuOpen(false);
                setIsDeleting(true);
              }}
            >
              <Text color="traffic-onDanger">
                {d("ui.workspace.annotations.actions.delete")}
              </Text>
            </DropdownItem>
          )}
        </DropdownItemGroup>
      )}
    </KebabMenu>
  );
};
