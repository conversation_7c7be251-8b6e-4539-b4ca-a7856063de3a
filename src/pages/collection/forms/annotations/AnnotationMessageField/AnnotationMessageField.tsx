import React, { useEffect, useMemo, useState } from "react";

import {
  Avatar,
  Button,
  Inline,
  Stack,
  TextAreaField
} from "@oneteam/onetheme";

import { AuthUser } from "@src/authentication/AuthContext";
import { useDictionary } from "@src/hooks/useDictionary";

import "./AnnotationMessageField.scss";

export const AnnotationMessageField = ({
  id,
  defaultMessage = "",
  onSave,
  onCancel,
  isEditing,
  user
}: {
  id: string;
  defaultMessage?: string;
  onSave: (message: string) => void;
  onCancel?: () => void;
  isEditing?: boolean;
  user: AuthUser | null;
}) => {
  const d = useDictionary();
  const [message, setMessage] = useState<string>(defaultMessage);

  useEffect(() => {
    setMessage(defaultMessage);
    setTimeout(() => {
      document.getElementById(id)?.focus();
    }, 100);
  }, [defaultMessage, id]);

  const buttons = useMemo(() => {
    if (isEditing) {
      return (
        <Inline gap="050">
          {onCancel && (
            <Button
              label={d("ui.common.cancel")}
              variant="secondary"
              size="small"
              onClick={onCancel}
            />
          )}
          <Button
            label={d("ui.common.save")}
            variant="primary"
            size="small"
            onClick={() => {
              onSave(message);
              setMessage("");
            }}
            disabled={!message}
          />
        </Inline>
      );
    }

    return (
      <Button
        leftIcon={{ name: "arrow_upward" }}
        label=""
        variant="primary"
        size="small"
        onClick={() => {
          onSave(message);
          setMessage("");
        }}
        disabled={!message}
      />
    );
  }, [d, isEditing, message, onCancel, onSave]);

  return (
    <Inline
      className="annotation-message-field"
      gap="075"
      width="100"
      overflow="auto"
    >
      {!isEditing && (
        <Avatar
          user={{
            firstName: user?.firstName ?? "",
            lastName: user?.lastName ?? "",
            image: user?.image ?? ""
          }}
          size="s"
        />
      )}
      <Stack
        width="100"
        gap="050"
        overflow="auto"
        onClick={e => e.stopPropagation()}
      >
        <Inline gap="050" alignment="left">
          <TextAreaField
            id={id}
            width="100"
            value={message}
            onChange={setMessage}
            onKeyDown={e => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                onSave(message);
                setMessage("");
              }
            }}
          />
          {message.length === 0 && buttons}
        </Inline>
        {message.length > 0 && (
          <Inline gap="050" spaceBetween>
            <Inline gap="100">
              {/* TODO: add back with @ mentioned */}
              {/* <IconButton name="alternate_email" label="Add mention" /> */}
              {/* TODO: add back with attach files */}
              {/* <IconButton name="attach_file" label="Add file" disabled /> */}
            </Inline>
            {buttons}
          </Inline>
        )}
      </Stack>
    </Inline>
  );
};
