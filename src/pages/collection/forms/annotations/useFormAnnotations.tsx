import { useCallback } from "react";

import { customNanoId } from "@helpers/customNanoIdHelper.ts";

import { useAuth } from "@src/hooks/useAuth";
import { useDictionary } from "@src/hooks/useDictionary";
import { ActorType, UserActor } from "@src/types/Actor";
import {
  Annotation,
  AnnotationVariant,
  ChangeResolveAnnotationHandler,
  CreateAnnotationHandler,
  DeleteAnnotationHandler,
  GetAnnotationLocationDisplay,
  HighlightAnnotation,
  UpdateAnnotationHandler
} from "@src/types/Annotation";
import {
  HighlightColor,
  JsonOrListItemLocation,
  TableCellLocation
} from "@src/types/AnnotationLocation";
import {
  JSONQuestionProperties,
  ListQuestionProperties,
  TableQuestionProperties
} from "@src/types/QuestionProperties";
import { TableAnswer } from "@src/types/collection/CollectionForm";

import { useCollectionFormContext } from "../CollectionFormContext";

export const useFormAnnotations = () => {
  const d = useDictionary();
  const { user } = useAuth();
  const { annotationDocChange, questionsById, formData } =
    useCollectionFormContext();

  const onCreateAnnotation: CreateAnnotationHandler = useCallback(
    ({
      annotation: {
        variant = AnnotationVariant.COMMENT,
        location,
        message,
        attachments = [],
        type,
        groupIdentifier,
        createdBy = {
          type: "user",
          userId: user?.id
        } as UserActor,
        color
      },
      parentAnnotationId
    }) => {
      if (!user?.id || (!message && variant !== AnnotationVariant.HIGHLIGHT)) {
        return;
      }

      const newAnnotationId = customNanoId();
      annotationDocChange?.(d => {
        const dateAsIsoString = new Date().toISOString();
        const newAnnotation = {
          id: newAnnotationId,
          variant,
          message,
          attachments,
          createdAt: dateAsIsoString,
          updatedAt: dateAsIsoString,
          createdBy,
          updatedBy: createdBy
        };

        if (variant === AnnotationVariant.HIGHLIGHT) {
          (newAnnotation as HighlightAnnotation).color =
            color ?? HighlightColor.COLOR_1;
        }

        if (parentAnnotationId) {
          // Annotation reply
          const parentAnnotation = d.annotations?.[parentAnnotationId];
          if (!parentAnnotation) {
            return;
          }
          parentAnnotation.replies ??= {};
          parentAnnotation.replies[newAnnotationId] = {
            ...newAnnotation,
            variant: AnnotationVariant.COMMENT
          };
          return newAnnotationId;
        }

        let annotationTypeSpecificField = {};
        if (variant === AnnotationVariant.ALERT) {
          if (type) {
            console.error(
              "Alert annotation type is required when creating an alert annotation"
            );
            return;
          }
          annotationTypeSpecificField = { groupIdentifier, type };
        }

        d.annotations ??= {};
        d.annotations[newAnnotationId] = {
          ...annotationTypeSpecificField,
          ...newAnnotation,
          location
        } as Annotation;
      });
      return newAnnotationId;
    },
    [annotationDocChange, user?.id]
  );

  const onUpdateAnnotation: UpdateAnnotationHandler = useCallback(
    ({
      annotation: {
        id,
        variant = AnnotationVariant.COMMENT,
        message,
        // attachments = [],
        type,
        groupIdentifier,
        color
      },
      parentAnnotationId
    }) => {
      if (!user?.id || (!message && variant !== AnnotationVariant.HIGHLIGHT)) {
        return;
      }

      annotationDocChange?.(d => {
        const dateAsIsoString = new Date().toISOString();

        if (parentAnnotationId) {
          // Annotation reply
          const parentAnnotation = d.annotations?.[parentAnnotationId];
          if (!parentAnnotation) {
            return;
          }
          const reply = parentAnnotation.replies?.[id];
          if (!reply) {
            return;
          }
          reply.message = message;
          reply.updatedAt = dateAsIsoString;
          reply.updatedBy = {
            type: "user",
            userId: user.id
          };
          // TODO: Support updating attachments
          // reply.attachments = attachments;
        } else {
          // Annotation
          const annotationToUpdate = d.annotations?.[id];
          if (!annotationToUpdate) {
            console.error("Annotation not found");
            return;
          }

          annotationToUpdate.variant = variant;
          annotationToUpdate.message = message;
          annotationToUpdate.updatedAt = dateAsIsoString;
          // TODO: Support updating attachments
          // annotationToUpdate.attachments = attachments;

          if (annotationToUpdate.variant === AnnotationVariant.ALERT) {
            annotationToUpdate.groupIdentifier = groupIdentifier;
            if (type) {
              annotationToUpdate.type = type;
            }
          }

          if (
            annotationToUpdate.variant === AnnotationVariant.HIGHLIGHT &&
            color
          ) {
            (annotationToUpdate as HighlightAnnotation).color = color;
          }
        }
      });
    },
    [annotationDocChange, user?.id]
  );

  const onDeleteAnnotation: DeleteAnnotationHandler = useCallback(
    ({ annotationId, parentAnnotationId }) => {
      if (!user?.id) {
        return;
      }
      annotationDocChange?.(d => {
        if (parentAnnotationId) {
          // Annotation reply
          const parentAnnotation = d.annotations?.[parentAnnotationId];
          if (!parentAnnotation) {
            return;
          }
          const replyToDelete = parentAnnotation.replies?.[annotationId];
          if (!replyToDelete) {
            return;
          }
          delete parentAnnotation.replies?.[annotationId];
        } else {
          // Annotation
          delete d.annotations?.[annotationId];
        }
      });
    },
    [annotationDocChange, user?.id]
  );

  const onChangeResolveAnnotation: ChangeResolveAnnotationHandler = useCallback(
    ({ annotationId, parentAnnotationId, isResolved }) => {
      if (!user?.id) {
        return;
      }
      annotationDocChange?.(d => {
        if (parentAnnotationId) {
          // Don't yet support resolving replies
          console.error("Resolving replies is not yet supported.");
        } else {
          // Annotation
          const annotation = d.annotations?.[annotationId];
          if (!annotation) {
            return;
          }
          // TODO: publish a "reply" event to the annotation
          annotation.resolved = isResolved
            ? {
                status: "resolved",
                actor: { type: ActorType.USER, userId: user.id }
              }
            : {
                status: "unresolved",
                actor: { type: ActorType.USER, userId: user.id }
              };

          onCreateAnnotation({
            annotation: {
              variant: AnnotationVariant.COMMENT,
              message: `${user.firstName} ${user.lastName} marked as "${isResolved ? "resolved" : "unresolved"}"`,
              location: annotation.location,
              attachments: [],
              type: undefined,
              groupIdentifier: undefined,
              // TODO: adjust implementation so resolve doesn't create a new annotation
              createdBy: {
                type: "user",
                userId: -1
              }
            },
            parentAnnotationId: annotationId
          });
        }
      });
    },
    [
      annotationDocChange,
      onCreateAnnotation,
      user?.firstName,
      user?.id,
      user?.lastName
    ]
  );

  const getAnnotationLocationDisplay: GetAnnotationLocationDisplay =
    useCallback(
      location => {
        if (!location) {
          return "";
        }

        let displayName = "";
        if (location.variant === "question" || location.variant === "answer") {
          if (!location.questionId) {
            return "";
          }
          const question = questionsById[location.questionId];
          const answer = formData?.answers?.[location.questionId];
          if (!question) {
            return "";
          }
          displayName += question.text;

          if ((location as TableCellLocation).rowId) {
            const rowIndex = (
              (answer?.value as TableAnswer)?.order ?? []
            ).indexOf((location as TableCellLocation).rowId as string);
            if (rowIndex !== -1) {
              displayName += ` - ${d("ui.common.row")} ${rowIndex + 1}`;
            }
          }
          if ((location as TableCellLocation).columnId) {
            const column = (
              question.properties as TableQuestionProperties
            )?.columns?.find(
              col => col.id === (location as TableCellLocation).columnId
            );
            if (!column) {
              return "";
            }
            displayName += `: ${column.text}`;
          }
          if ((location as JsonOrListItemLocation).itemId) {
            const item = (
              question.properties as
                | JSONQuestionProperties
                | ListQuestionProperties
            )?.items?.find(
              item => item.id === (location as JsonOrListItemLocation).itemId
            );
            if (!item) {
              return "";
            }
          }
          if (location.variant === "answer") {
            displayName += ` (${d("ui.terminology.answer")})`;
          }
        } else if (location.variant === "section") {
          // TODO: get section name from questionsById
          displayName += d("ui.terminology.section");
        }
        return displayName;
      },
      [d, formData?.answers, questionsById]
    );

  return {
    onCreateAnnotation,
    onDeleteAnnotation,
    onChangeResolveAnnotation,
    onUpdateAnnotation,
    getAnnotationLocationDisplay
  };
};
