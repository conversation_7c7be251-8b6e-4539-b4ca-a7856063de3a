import React from "react";

import {
  ConfigurationFormType,
  Section
} from "@src/types/FormConfiguration.ts";

import { CollectionFormSection } from "./sections/CollectionFormSection.tsx";

// ConfigurationFormType
export const CollectionFormContent = ({
  configuration,
  level
}: {
  configuration: ConfigurationFormType;
  level: number;
}) => {
  return (configuration.content as Section[]).map((contentItem, index) => (
    <CollectionFormSection
      key={`section-${contentItem.id}`}
      section={contentItem}
      level={level + 1}
      index={index}
    />
  ));
};

CollectionFormContent.displayName = "CollectionFormContent";
