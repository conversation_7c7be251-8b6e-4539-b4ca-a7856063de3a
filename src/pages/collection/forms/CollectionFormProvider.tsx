import React, {
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useState
} from "react";

import { useUrlHash } from "@oneteam/onetheme";

import { isSection } from "@helpers/configurationFormHelper";

import {
  AnnotationLocation,
  HighlightColor
} from "@src/types/AnnotationLocation";
import { Section } from "@src/types/FormConfiguration";
import { Question } from "@src/types/Question";
import {
  JSONQuestionProperties,
  ListQuestionProperties,
  TableQuestionProperties
} from "@src/types/QuestionProperties";
import {
  AnswerDocChange,
  CollectionFormData,
  CollectionFormMode,
  CollectionFormSummaryModalTab,
  FormAnnotationDocChange
} from "@src/types/collection/CollectionForm";
import { FormAnnotationDocument } from "@src/types/documentTypes";

import { useCollectionHomeContext } from "../home/<USER>";
import { CollectionFormContext, FieldState } from "./CollectionFormContext";
import {
  getAnnotationLocationAsString,
  getAnnotationLocationFromString
} from "./annotations/annotationHelpers";

export const CollectionFormProvider = ({
  children
}: {
  children: ReactNode;
}) => {
  const { urlHashDetail, updateUrlHash } = useUrlHash();
  const { collectionFormId } = useCollectionHomeContext();
  const [isInitialized, setIsInitialized] = useState(false);

  const [documentId, setDocumentId] = useState<string | undefined>(undefined);
  const [formData, setFormData] = useState<CollectionFormData | undefined>(
    undefined
  );
  const [docChange, setDocChange] = useState<AnswerDocChange | undefined>(
    undefined
  );
  const [fieldStates] = useState(() => new Map<string, FieldState>());

  const [annotationDocChange, setAnnotationDocChange] = useState<
    FormAnnotationDocChange | undefined
  >(undefined);
  const [annotationDocumentId, setAnnotationDocumentId] = useState<
    string | undefined
  >(undefined);
  const [formAnnotationDocument, setFormAnnotationDocument] = useState<
    FormAnnotationDocument | undefined
  >(undefined);

  const [currentHighlightColor, setCurrentHighlightColor] =
    useState<HighlightColor>(HighlightColor.COLOR_1);

  const mode = useMemo(() => {
    let hashMode = urlHashDetail?.get("mode") as `${CollectionFormMode}`;
    if (!hashMode) {
      hashMode = CollectionFormMode.EDIT;
      updateUrlHash("mode", hashMode);
    }

    return hashMode;
  }, [updateUrlHash, urlHashDetail]);

  const onChangeMode = useCallback(
    (newMode: `${CollectionFormMode}`) => {
      if (newMode === mode) {
        return;
      }
      updateUrlHash("mode", newMode);
    },
    [mode, updateUrlHash]
  );

  const questionsById = useMemo(() => {
    const questionsById: {
      [key: string]: Question;
    } = {};

    const getQuestionsInSection = (
      sectionOrQuestion: Section | Question
    ): Question[] => {
      const isQuestion = !isSection(sectionOrQuestion);
      if (isQuestion) {
        return [sectionOrQuestion];
      }

      return sectionOrQuestion.content?.flatMap(getQuestionsInSection);
    };

    formData?.configuration.content?.forEach(
      (sectionOrQuestion: Section | Question) => {
        const questionSection = getQuestionsInSection(sectionOrQuestion);
        questionSection?.forEach(question => {
          questionsById[question.id] = question;

          if (question?.type === "table") {
            (question.properties as TableQuestionProperties).columns?.forEach(
              column => {
                questionsById[column.id] = column;
              }
            );
          } else if (question?.type === "json" || question?.type === "list") {
            (
              question.properties as
                | JSONQuestionProperties
                | ListQuestionProperties
            ).items?.forEach(item => {
              questionsById[item.id] = item;
            });
          }
        });
      }
    );

    return questionsById;
  }, [formData?.configuration.content]);

  const onChangeSectionIsCollapsed = useCallback(
    ({
      sectionId,
      isCollapsed
    }: {
      sectionId: string;
      isCollapsed: boolean;
    }) => {
      annotationDocChange?.(d => {
        if (!d) {
          return d;
        }
        d.state ??= {
          sectionsCollapsed: {}
        };
        d.state.sectionsCollapsed ??= {};
        d.state.sectionsCollapsed[sectionId] = isCollapsed;
      });
    },
    [annotationDocChange]
  );

  const getFieldState = useCallback(
    (field: string) => {
      return fieldStates.get(field) || { error: undefined };
    },
    [fieldStates]
  );

  const setFieldState = useCallback(
    (field: string, state: FieldState) => {
      fieldStates.set(field, state);
    },
    [fieldStates]
  );

  const [summaryModalTab, setSummaryModalTab] = useState<
    "none" | `${CollectionFormSummaryModalTab}` | undefined
  >("none");
  const [summaryModalLocation, setSummaryModalLocation] = useState<
    AnnotationLocation | undefined
  >(undefined);

  const [liveCursorsVisible, setLiveCursorsVisible] = useState<boolean>(true);

  useEffect(() => {
    if (isInitialized || !formData || !questionsById) {
      return;
    }
    const summaryTab = urlHashDetail.get("summary") ?? "none";
    setSummaryModalTab(summaryTab as CollectionFormSummaryModalTab | "none");

    const locationString = urlHashDetail.get("location") ?? "";
    const location = getAnnotationLocationFromString({
      locationString,
      formData,
      questionsById
    });
    if (location) {
      setSummaryModalLocation(location);
    }
    setIsInitialized(true);
  }, [urlHashDetail, formData, questionsById, isInitialized]);

  useEffect(() => {
    if (!isInitialized) {
      return;
    }
    if (summaryModalTab === "none" || !summaryModalTab) {
      updateUrlHash("summary", "none");
    } else {
      updateUrlHash("summary", summaryModalTab);
    }
  }, [isInitialized, summaryModalTab, updateUrlHash]);

  // keep url hash location in sync
  useEffect(() => {
    if (!isInitialized) {
      return;
    }
    if (
      summaryModalTab === "none" ||
      !summaryModalTab ||
      !summaryModalLocation
    ) {
      updateUrlHash("location", "none");
    } else {
      updateUrlHash(
        "location",
        getAnnotationLocationAsString({
          location: summaryModalLocation,
          formData,
          questionsById
        })
      );
    }
  }, [
    formData,
    isInitialized,
    questionsById,
    summaryModalLocation,
    summaryModalTab,
    updateUrlHash
  ]);

  const contextValue = React.useMemo(
    () => ({
      documentId,
      setDocumentId,
      collectionFormId,
      formData,
      setFormData,
      docChange,
      setDocChange,
      getFieldState,
      setFieldState,
      formAnnotationDocument,
      setFormAnnotationDocument,
      annotationDocumentId,
      setAnnotationDocumentId,
      annotationDocChange,
      setAnnotationDocChange,
      summaryModal: {
        tab: summaryModalTab,
        setTab: setSummaryModalTab,
        location: summaryModalLocation,
        setLocation: setSummaryModalLocation
      },
      questionsById,
      onChangeSectionIsCollapsed,
      settings: {
        liveCursorsVisible,
        setLiveCursorsVisible
      },
      mode,
      onChangeMode,
      currentHighlightColor,
      setCurrentHighlightColor
    }),
    [
      documentId,
      collectionFormId,
      formData,
      docChange,
      getFieldState,
      setFieldState,
      formAnnotationDocument,
      annotationDocumentId,
      annotationDocChange,
      summaryModalTab,
      summaryModalLocation,
      questionsById,
      onChangeSectionIsCollapsed,
      liveCursorsVisible,
      mode,
      onChangeMode,
      currentHighlightColor,
      setCurrentHighlightColor
    ]
  );

  return (
    <CollectionFormContext.Provider value={contextValue}>
      {children}
    </CollectionFormContext.Provider>
  );
};
