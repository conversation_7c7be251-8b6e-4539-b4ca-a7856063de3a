import { createContext, useContext } from "react";

import { WorkspaceUserSearch } from "@src/types/WorkspaceUser";

export type WorkspaceUsersContextType = {
  workspaceUsers: WorkspaceUserSearch[];
  workspaceUsersById?: Record<number, WorkspaceUserSearch>;
};

export const WorkspaceUsersContext = createContext<WorkspaceUsersContextType>({
  workspaceUsers: [],
  workspaceUsersById: undefined
});

export const useWorkspaceUsers = () => useContext(WorkspaceUsersContext);
