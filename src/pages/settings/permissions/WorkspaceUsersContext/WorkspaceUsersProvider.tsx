import React, { ReactNode, useMemo } from "react";

import { keyBy } from "lodash";

import { useWorkspaceUsersSearch } from "@helpers/userHelper";

import { Page } from "@src/types/Page";
import { WorkspaceUserSearch } from "@src/types/WorkspaceUser";
import { Workspace } from "@src/types/workspace";

import { WorkspaceUsersContext } from "./WorkspaceUsersContext";

export const WorkspaceUsersProvider = ({
  workspace,
  children
}: {
  workspace: Workspace;
  children: ReactNode;
}) => {
  // TODO: need to not limit the number of users
  // Change this to be a - get minimal workspace users (just id, first name, last name, email)
  const { data } = useWorkspaceUsersSearch(
    workspace.id,
    new URLSearchParams([["pageSize", "1000"]]),
    !!workspace?.id
  ) as {
    data: Page<WorkspaceUserSearch>;
  };

  const workspaceUsers = useMemo(() => data?.items ?? [], [data]);

  const workspaceUsersById = useMemo(
    () => keyBy(workspaceUsers, (user: WorkspaceUserSearch) => user.user.id),
    [workspaceUsers]
  );
  return (
    <WorkspaceUsersContext.Provider
      value={{
        workspaceUsers,
        workspaceUsersById
      }}
    >
      {children}
    </WorkspaceUsersContext.Provider>
  );
};
