import React, { ReactNode, useState } from "react";

import { WorkspaceAccessLevel } from "@src/types/WorkspaceUser";

import { WorkspaceAccessLevelContext } from "./WorkspaceAccessLevelContext";

export const WorkspaceAccessLevelProvider = ({
  children
}: {
  children: ReactNode;
}) => {
  const [currentLevel, setCurrentLevel] = useState<
    WorkspaceAccessLevel[] | null
  >(null);
  const [hasAccessToLevel, setHasAccessToLevel] = useState<boolean>(false);

  return (
    <WorkspaceAccessLevelContext.Provider
      value={{
        currentLevel,
        setCurrentLevel,
        hasAccessToLevel,
        setHasAccessToLevel
      }}
    >
      {children}
    </WorkspaceAccessLevelContext.Provider>
  );
};
