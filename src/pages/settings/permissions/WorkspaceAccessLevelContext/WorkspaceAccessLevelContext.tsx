import { createContext, useContext } from "react";

import { WorkspaceAccessLevel } from "@src/types/WorkspaceUser";

export type WorkspaceAccessLevelContextType = {
  currentLevel: WorkspaceAccessLevel[] | null;
  setCurrentLevel: (level: WorkspaceAccessLevel[] | null) => void;
  hasAccessToLevel: boolean;
  setHasAccessToLevel: (hasAccess: boolean) => void;
};

export const WorkspaceAccessLevelContext =
  createContext<WorkspaceAccessLevelContextType>({
    currentLevel: null,
    setCurrentLevel: () => {},
    hasAccessToLevel: false,
    setHasAccessToLevel: () => {}
  });

export const useWorkspaceAccessLevel = () =>
  useContext(WorkspaceAccessLevelContext);
