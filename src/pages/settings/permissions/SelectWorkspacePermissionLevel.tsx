import React, { useCallback } from "react";

import {
  <PERSON><PERSON>,
  Card,
  Checkbox,
  Divider,
  Heading,
  Inline,
  Stack,
  Text,
  selectAllLabelDefault
} from "@oneteam/onetheme";

import { useDictionary } from "@src/hooks/useDictionary";
import { WorkspaceAccessLevel } from "@src/types/WorkspaceUser";

export const SelectWorkspacePermissionLevel = ({
  onChange,
  value = [],
  disabledLevels,
  isInitialAdd
}: {
  onChange: (levels: WorkspaceAccessLevel[]) => void;
  value?: WorkspaceAccessLevel[];
  disabledLevels?: WorkspaceAccessLevel[];
  isInitialAdd?: boolean;
}) => {
  const d = useDictionary();
  const onChangeLevel = useCallback(
    (level: WorkspaceAccessLevel) => (isChecked: boolean) => {
      if (isChecked) {
        onChange([...value, level]);
      } else {
        onChange(value.filter(l => l !== level));
      }
    },
    [onChange, value]
  );

  const onChangeSelectAll = useCallback(
    (isChecked: boolean) => {
      if (isChecked) {
        onChange([
          WorkspaceAccessLevel.COLLECTION,
          WorkspaceAccessLevel.CONFIGURATION,
          WorkspaceAccessLevel.SETTINGS
        ]);
      } else {
        onChange([]);
      }
    },
    [onChange]
  );

  // TODO: enable this when collection permissions are implemented
  // const [withAddToGlobalWorkspace, setwithAddToGlobalWorkspace] =
  //   React.useState(false);

  // const [configurationAccess, setConfigurationAccess] = React.useState<
  //   "view" | "edit"
  // >("edit");
  // const [configurationAccessIsOpen, setConfigurationAccessIsOpen] =
  //   React.useState(false);

  return (
    <Stack gap="100">
      <Card size="small">
        <Stack gap="150">
          {isInitialAdd && (
            <>
              <WorkspacePermissionLevelSelectCheckbox
                key="selectAll"
                value="selectAll"
                label={selectAllLabelDefault}
                isChecked={value.length === 3}
                onChange={onChangeSelectAll}
                disabled={disabledLevels?.length === 3}
              />
              <Divider size="small" />
            </>
          )}
          <WorkspacePermissionLevelSelectCheckbox
            key={WorkspaceAccessLevel.COLLECTION}
            value={WorkspaceAccessLevel.COLLECTION}
            label={d(
              `ui.settings.permissions.level.${WorkspaceAccessLevel.COLLECTION}.label`
            )}
            description={d(
              `ui.settings.permissions.level.${WorkspaceAccessLevel.COLLECTION}.description`
            )}
            isChecked={value.includes(WorkspaceAccessLevel.COLLECTION)}
            onChange={isChecked => {
              onChangeLevel(WorkspaceAccessLevel.COLLECTION)(isChecked);
              // TODO: enable this when collection permissions are implemented
              // setwithAddToGlobalWorkspace(false);
            }}
            disabled={disabledLevels?.includes(WorkspaceAccessLevel.COLLECTION)}
            afterElement={
              isInitialAdd
                ? // TODO: enable this when collection permissions are implemented
                  // <Checkbox
                  //   label={d(
                  //     `ui.settings.permissions.level.${WorkspaceAccessLevel.COLLECTION}.addToGlobalWorkspace.label`
                  //   )}
                  //   tooltip={d(
                  //     `ui.settings.permissions.level.${WorkspaceAccessLevel.COLLECTION}.addToGlobalWorkspace.tooltip`
                  //   )}
                  //   isChecked={withAddToGlobalWorkspace}
                  //   onChange={setwithAddToGlobalWorkspace}
                  //   disabled={!value.includes(WorkspaceAccessLevel.COLLECTION)}
                  //   id="with-add-to-global-workspace-toggle"
                  // />
                  null
                : null
            }
          />
          <Divider size="small" />
          <WorkspacePermissionLevelSelectCheckbox
            key={WorkspaceAccessLevel.CONFIGURATION}
            value={WorkspaceAccessLevel.CONFIGURATION}
            label={d(
              `ui.settings.permissions.level.${WorkspaceAccessLevel.CONFIGURATION}.label`
            )}
            description={d(
              `ui.settings.permissions.level.${WorkspaceAccessLevel.CONFIGURATION}.description`
            )}
            isChecked={value.includes(WorkspaceAccessLevel.CONFIGURATION)}
            onChange={onChangeLevel(WorkspaceAccessLevel.CONFIGURATION)}
            disabled={disabledLevels?.includes(
              WorkspaceAccessLevel.CONFIGURATION
            )}
            // TODO: Enable this when configuration access levels are implemented
            // rightElement={
            //   value.includes(WorkspaceAccessLevel.CONFIGURATION) ? (
            //     <DropdownMenu
            //       isOpen={configurationAccessIsOpen}
            //       onOpenChange={setConfigurationAccessIsOpen}
            //       trigger={
            //         configurationAccess === "view" ? "Can view" : "Can edit"
            //       }
            //       position="bottom-right"
            //       // TODO: Enable this when configuration access levels are implemented
            //       // disabled={disabledLevels?.includes(
            //       //   WorkspaceAccessLevel.CONFIGURATION
            //       // )}
            //       disabled
            //     >
            //       <DropdownItemGroup>
            //         <DropdownItemRadio
            //           isChecked={configurationAccess === "view"}
            //           onChange={() => {
            //             setConfigurationAccess("view");
            //             setConfigurationAccessIsOpen(false);
            //           }}
            //         >
            //           Can view
            //         </DropdownItemRadio>
            //         <DropdownItemRadio
            //           isChecked={configurationAccess === "edit"}
            //           onChange={() => {
            //             setConfigurationAccess("edit");
            //             setConfigurationAccessIsOpen(false);
            //           }}
            //         >
            //           Can edit
            //         </DropdownItemRadio>
            //       </DropdownItemGroup>
            //     </DropdownMenu>
            //   ) : null
            // }
          />
          <Divider size="small" />
          <WorkspacePermissionLevelSelectCheckbox
            key={WorkspaceAccessLevel.SETTINGS}
            value={WorkspaceAccessLevel.SETTINGS}
            label={d(
              `ui.settings.permissions.level.${WorkspaceAccessLevel.SETTINGS}.label`
            )}
            description={d(
              `ui.settings.permissions.level.${WorkspaceAccessLevel.SETTINGS}.description`
            )}
            isChecked={value.includes(WorkspaceAccessLevel.SETTINGS)}
            onChange={onChangeLevel(WorkspaceAccessLevel.SETTINGS)}
            disabled={disabledLevels?.includes(WorkspaceAccessLevel.SETTINGS)}
          />
        </Stack>
      </Card>
      {!value.length && (
        <Alert variant="danger" background="transparent">
          {d("ui.settings.permissions.addUsers.noAccessSelected")}
        </Alert>
      )}
    </Stack>
  );
};

const WorkspacePermissionLevelSelectCheckbox = ({
  label,
  description,
  value,
  isChecked,
  onChange,
  disabled,
  rightElement,
  afterElement
}: {
  label?: string;
  description?: string;
  value?: `${WorkspaceAccessLevel}` | "selectAll";
  isChecked: boolean;
  onChange: (isChecked: boolean) => void;
  disabled?: boolean;
  rightElement?: React.ReactNode;
  afterElement?: React.ReactNode;
}) => {
  return (
    <Inline spaceBetween={!!rightElement}>
      <Stack gap="075" width="100">
        <Inline
          onClick={disabled ? undefined : () => onChange(!isChecked)}
          key={value}
          gap="100"
          alignment="left"
          style={{ cursor: disabled ? "not-allowed" : "pointer" }}
        >
          <Checkbox
            id={`workspace-level-${value}`}
            isChecked={isChecked}
            onChange={() => onChange(!isChecked)}
            disabled={disabled}
          />
          <Stack gap="000">
            <Heading
              size="xs"
              weight="regular"
              color={disabled ? "text-tertiary" : "text-primary"}
            >
              {label}
            </Heading>
            {description && (
              <Text
                size="s"
                color={disabled ? "text-disabled" : "text-tertiary"}
              >
                {description}
              </Text>
            )}
          </Stack>
        </Inline>
        {afterElement && (
          <Inline
            alignment="left"
            gap="100"
            style={{
              paddingLeft:
                "calc(var(--components-inputs-checkbox-size, 16px) + var(--spacing-100))"
            }}
          >
            {afterElement}
          </Inline>
        )}
      </Stack>
      {rightElement && <Inline alignment="right">{rightElement}</Inline>}
    </Inline>
  );
};
