import React, { use<PERSON><PERSON>back, useMemo } from "react";

import {
  Ava<PERSON>,
  <PERSON>ton,
  ButtonGroup,
  ButtonVariant,
  CloseIconButton,
  Heading,
  Inline,
  Label,
  Modal,
  ModalDialog,
  Overlay,
  Pill,
  Stack,
  Text
} from "@oneteam/onetheme";

import { useUpdateWorkspaceUser } from "@helpers/userHelper";

import { formatDate } from "@pages/collection/flows/FlowExecutionHelper";

import { useAuth } from "@src/hooks/useAuth";
import { useDictionary } from "@src/hooks/useDictionary";
import {
  WorkspaceAccessLevel,
  WorkspaceUser,
  WorkspaceUserForUpdate,
  WorkspaceUserStatus
} from "@src/types/WorkspaceUser";
import { Workspace } from "@src/types/workspace";

import { SelectWorkspacePermissionLevel } from "../SelectWorkspacePermissionLevel";
import "./UpdateWorkspaceUserModal.scss";

export const UpdateWorkspaceUserModal = ({
  workspace,
  workspaceUser,
  closeModal,
  onClickUpdateStatus
}: {
  workspace: Workspace;
  workspaceUser: WorkspaceUser;
  closeModal: () => void;
  onClickUpdateStatus?: () => void;
}) => {
  const { user: authUser } = useAuth();
  const d = useDictionary();

  const [levels, setLevels] = React.useState<WorkspaceAccessLevel[]>(
    workspaceUser.levels as WorkspaceAccessLevel[]
  );

  const { updateWorkspaceUser } = useUpdateWorkspaceUser({
    workspace
  });

  const disabledLevels = useMemo(() => {
    if (workspaceUser.status === WorkspaceUserStatus.INACTIVE) {
      return Object.values(WorkspaceAccessLevel);
    }

    if (workspaceUser.isOwner || authUser?.id === workspaceUser.userId) {
      return [WorkspaceAccessLevel.SETTINGS];
    }
    return undefined;
  }, [
    authUser?.id,
    workspaceUser.isOwner,
    workspaceUser.status,
    workspaceUser.userId
  ]);

  // Get list of all current users in the tenant

  const handleUpdateUser = useCallback(() => {
    const formattedUserToUpdate: WorkspaceUserForUpdate = {
      id: workspaceUser.id,
      workspaceId: workspace.id,
      userId: workspaceUser.userId,
      status: workspaceUser.status,
      accessLevel: levels
    };
    updateWorkspaceUser(formattedUserToUpdate);
    closeModal();
  }, [closeModal, workspaceUser, levels, updateWorkspaceUser, workspace.id]);

  return (
    <ModalDialog isOpen>
      <Overlay isOpen />
      <Modal
        className="update-workspace-user-modal"
        isOpen
        headingOverride={
          <Inline
            alignment="top-center"
            width="100"
            spaceBetween
            style={{
              paddingBottom: "var(--spacing-100)"
            }}
          >
            <Inline gap="150" alignment="left">
              <Avatar
                user={{
                  firstName: workspaceUser.firstName,
                  lastName: workspaceUser.lastName,
                  image: workspaceUser.image || ""
                }}
              />
              <Stack>
                <Inline alignment="left" gap="050">
                  <Heading>
                    {`${workspaceUser.firstName} ${workspaceUser.lastName}`}
                  </Heading>
                  {workspaceUser.isOwner && (
                    <Pill label="Workspace owner" variant="neutral" />
                  )}
                  {workspaceUser.status === WorkspaceUserStatus.INACTIVE && (
                    <Pill
                      label="Deactivated from workspace"
                      variant="neutral"
                    />
                  )}
                </Inline>
                <Text weight="regular" size="m" color="text-tertiary">
                  {workspaceUser.email}
                </Text>
              </Stack>
            </Inline>
            <CloseIconButton onClick={closeModal} />
          </Inline>
        }
        onOpenChange={closeModal}
        closeOnClickOutside={true}
        footer={
          <ButtonGroup alignment="right">
            <Button
              variant={ButtonVariant.TEXT}
              label={d("ui.common.cancel")}
              onClick={closeModal}
            />
            <Button
              variant={ButtonVariant.PRIMARY}
              label={d("ui.common.save")}
              onClick={() => {
                handleUpdateUser();
              }}
            />
          </ButtonGroup>
        }
      >
        <Stack gap="200" contentsWidth="fit">
          <Stack gap="050">
            <Label
              label="Permissions"
              required
              disabled={disabledLevels?.length === 3}
            />
            <SelectWorkspacePermissionLevel
              value={levels}
              onChange={setLevels}
              disabledLevels={disabledLevels}
            />
          </Stack>
          <Stack gap="050">
            {workspaceUser.status === WorkspaceUserStatus.ACTIVE &&
              !workspaceUser.isOwner &&
              authUser?.id !== workspaceUser.userId && (
                <Button
                  variant="secondary"
                  traffic="danger"
                  label={d(
                    `ui.settings.permissions.status.${WorkspaceUserStatus.INACTIVE}.action.label`
                  )}
                  onClick={onClickUpdateStatus}
                />
              )}
            {workspaceUser.status === WorkspaceUserStatus.INACTIVE && (
              <Button
                variant="secondary"
                label={d(
                  `ui.settings.permissions.status.${WorkspaceUserStatus.ACTIVE}.action.label`
                )}
                onClick={onClickUpdateStatus}
              />
            )}
            {workspaceUser.metadata?.createdAt && (
              <Text size="s" color="text-secondary">
                {`Joined workspace ${formatDate(workspaceUser.metadata.createdAt)}`}
              </Text>
            )}
          </Stack>
        </Stack>
      </Modal>
    </ModalDialog>
  );
};
