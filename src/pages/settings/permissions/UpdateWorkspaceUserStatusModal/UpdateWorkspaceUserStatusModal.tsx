import React, { useCallback, useMemo } from "react";

import { ConfirmationModal, ModalDialog, Overlay } from "@oneteam/onetheme";

import { useUpdateWorkspaceUser } from "@helpers/userHelper";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import {
  WorkspaceUser,
  WorkspaceUserForUpdate,
  WorkspaceUserStatus
} from "@src/types/WorkspaceUser";
import { Workspace } from "@src/types/workspace";

import "./UpdateWorkspaceUserStatusModal.scss";

export const UpdateWorkspaceUserStatusModal = ({
  workspace,
  userToUpdate,
  closeModal
}: {
  workspace: Workspace;
  userToUpdate: WorkspaceUser;
  closeModal: () => void;
}) => {
  const d = useDictionary();

  const { updateWorkspaceUser } = useUpdateWorkspaceUser({
    workspace
  });

  const statusToUpdateTo = useMemo(
    () =>
      userToUpdate.status === WorkspaceUserStatus.ACTIVE
        ? WorkspaceUserStatus.INACTIVE
        : WorkspaceUserStatus.ACTIVE,
    [userToUpdate.status]
  );

  const handleUpdateUserStatus = useCallback(() => {
    const formattedUserToUpdate: WorkspaceUserForUpdate = {
      id: userToUpdate.id,
      workspaceId: workspace.id,
      userId: userToUpdate.userId,
      status: statusToUpdateTo
    };
    updateWorkspaceUser(formattedUserToUpdate);
    closeModal();
  }, [
    closeModal,
    statusToUpdateTo,
    userToUpdate,
    updateWorkspaceUser,
    workspace.id
  ]);

  return (
    <ModalDialog isOpen>
      <Overlay isOpen />
      <ConfirmationModal
        className="update-workspace-user-status-modal"
        variant={
          statusToUpdateTo === WorkspaceUserStatus.INACTIVE
            ? "danger"
            : "warning"
        }
        heading={d(
          `ui.settings.permissions.updateStatusModal.${statusToUpdateTo}.heading`
        )}
        message={d(
          `ui.settings.permissions.updateStatusModal.${statusToUpdateTo}.message`,
          {
            name: `${userToUpdate.firstName} ${userToUpdate.lastName}`,
            value: workspace.name
          }
        )}
        onConfirm={handleUpdateUserStatus}
        onCancel={closeModal}
        confirmLabel={d(
          `ui.settings.permissions.updateStatusModal.${statusToUpdateTo}.confirmLabel`
        )}
      />
    </ModalDialog>
  );
};
