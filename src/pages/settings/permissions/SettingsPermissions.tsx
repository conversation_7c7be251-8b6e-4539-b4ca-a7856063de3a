import React, { use<PERSON><PERSON><PERSON>, useMemo, useState } from "react";

import {
  Avatar,
  Button,
  ButtonGroup,
  DropdownItem,
  DropdownItemCheckbox,
  DropdownItemGroup,
  DropdownMenu,
  Icon,
  IconButton,
  Inline,
  KebabMenu,
  KebabMenuProps,
  PageBodyTemplate,
  Pill,
  SearchBar,
  Stack,
  TableColumn,
  TableKebabMenuFormatter,
  TableWithPagination,
  Text,
  TextAlignment,
  useQueryParams,
  useTableSort
} from "@oneteam/onetheme";
import { useOutletContext, useSearchParams } from "react-router-dom";

import { buildQueryParams } from "@helpers/pagination";
import { useWorkspaceUsersSearch } from "@helpers/userHelper";

import { useAuth } from "@src/hooks/useAuth";
import { useBreadcrumbs } from "@src/hooks/useBreadcrumbs.tsx";
import { Dictionary, useDictionary } from "@src/hooks/useDictionary.tsx";
import { Page } from "@src/types/Page";
import {
  WorkspaceAccessLevel,
  WorkspaceUser,
  WorkspaceUserSearch,
  WorkspaceUserStatus,
  buildQueryParamsForWorkspaceUserSearch
} from "@src/types/WorkspaceUser";
import { Workspace } from "@src/types/workspace";

import { AddWorkspaceUsersModal } from "./AddWorkspaceUsersModal/AddWorkspaceUsersModal";
import { UpdateWorkspaceUserModal } from "./UpdateWorkspaceUserModal/UpdateWorkspaceUserModal";
import { UpdateWorkspaceUserStatusModal } from "./UpdateWorkspaceUserStatusModal/UpdateWorkspaceUserStatusModal";

enum SettingsPermissionsModals {
  ADD_USERS = "addUsers",
  UPDATE_USER_STATUS_CONFIRMATION = "updateUserStatusConfirmation",
  UPDATE_USER = "updateUser"
}

export const SettingsPermissions = () => {
  const d = useDictionary();
  const breadcrumbs = useBreadcrumbs();

  const { workspace } = useOutletContext<{
    workspace: Workspace;
  }>();

  const [modalOpen, setModalOpen] = useState<
    SettingsPermissionsModals | undefined
  >();
  const [userToUpdate, setUserToUpdate] = useState<WorkspaceUser | undefined>();

  const [filterKebabIsOpen, setFilterKebabIsOpen] = useState(false);
  const { queryParams, updateQueryParams } = useQueryParams({
    initialDefaultIfNone: { page: "1" },
    useSearchParams
  });

  const { searchFilter, page, pageSize, asc, dsc, showDeactivated } = useMemo(
    () => ({
      searchFilter: queryParams.get("search") ?? undefined,
      page: queryParams.get("page") ?? undefined,
      pageSize: queryParams.get("pageSize") ?? undefined,
      asc: queryParams.get("asc") ?? undefined,
      dsc: queryParams.get("dsc") ?? undefined,
      showDeactivated: queryParams.get("showDeactivated") ?? undefined
    }),
    [queryParams]
  );

  const permissionsColumns: TableColumn<WorkspaceUser>[] = useMemo(
    () => getPermissionsColumns(d),
    [d]
  );

  const { sort, handleSetSort } = useTableSort<WorkspaceUser>({
    columns: permissionsColumns,
    queryParams,
    updateQueryParams: (newParams: { [key: string]: string | undefined }) => {
      doUpdateQueryParams({
        page: newParams.page,
        search: searchFilter,
        asc: newParams.asc,
        dsc: newParams.dsc,
        showDeactivated: newParams.showDeactivated
      });
    }
  });

  const { data: workspaceUsers } = useWorkspaceUsersSearch(
    workspace.id,
    buildQueryParamsForWorkspaceUserSearch(
      searchFilter,
      buildQueryParams(
        page ? Number(page) : undefined,
        pageSize ? Number(pageSize) : undefined,
        asc,
        dsc
      )
    )
  ) as { data: Page<WorkspaceUserSearch> };

  const doUpdateQueryParams = useCallback(
    (newParams: { [key: string]: string | undefined }) => {
      const current = {
        page: page,
        pageSize: pageSize,
        search: searchFilter,
        asc: asc,
        dsc: dsc,
        showDeactivated: showDeactivated
      };
      // merge new properties into current
      const updatedQueryParams = {
        ...current,
        ...newParams
      };
      if (!updatedQueryParams.showDeactivated) {
        delete updatedQueryParams.showDeactivated;
      }
      updateQueryParams(updatedQueryParams);
    },
    [page, pageSize, searchFilter, asc, dsc, showDeactivated, updateQueryParams]
  );

  const onChangePage = useCallback(
    (newPage: number) => {
      doUpdateQueryParams({ page: String(newPage) });
    },
    [doUpdateQueryParams]
  );

  const kebabMenu: TableKebabMenuFormatter<WorkspaceUser> = useCallback(
    ({ row, props, closeMenu }) => {
      return (
        <WorkspaceUserRowKebabMenu
          row={row}
          props={props}
          closeMenu={closeMenu}
          setUserToUpdate={setUserToUpdate}
          setModalOpen={setModalOpen}
        />
      );
    },
    []
  );

  const onChangeSearchTerm = useCallback(
    (input: string) => {
      doUpdateQueryParams({
        search: input,
        page: "1" // reset page to 1 when changing filter criteria since the total count may change
      });
    },
    [doUpdateQueryParams]
  );

  const handleToggleShowDeactivated = useCallback(
    (isChecked?: boolean) => {
      doUpdateQueryParams({ showDeactivated: isChecked?.toString() });
    },
    [doUpdateQueryParams]
  );

  const formattedWorkspaceUsers = useMemo(() => {
    if (!workspaceUsers?.items) {
      return [];
    }

    return workspaceUsers.items
      .filter(
        item =>
          showDeactivated === "true" ||
          item.status === WorkspaceUserStatus.ACTIVE
      )
      .map(item => ({
        id: item.id,
        userId: item.user.id,
        firstName: item.user.properties?.firstName ?? "",
        lastName: item.user.properties?.lastName ?? "",
        email: item.user.email,
        isOwner: false,
        status: item.status,
        levels: item.accessLevel,
        metadata: {
          lastActive: item.metadata.updatedAt,
          createdAt: item.metadata.createdAt,
          updatedAt: item.metadata.updatedAt
        }
      }));
  }, [workspaceUsers?.items, showDeactivated]);

  return (
    <PageBodyTemplate
      heading={d("ui.settings.permissions.title")}
      breadcrumbs={breadcrumbs}
      withoutScroll
    >
      {userToUpdate &&
        modalOpen ===
          SettingsPermissionsModals.UPDATE_USER_STATUS_CONFIRMATION && (
          <UpdateWorkspaceUserStatusModal
            workspace={workspace}
            userToUpdate={userToUpdate}
            closeModal={() => {
              setUserToUpdate(undefined);
              setModalOpen(undefined);
            }}
          />
        )}
      {userToUpdate && modalOpen === SettingsPermissionsModals.UPDATE_USER && (
        <UpdateWorkspaceUserModal
          workspace={workspace}
          workspaceUser={userToUpdate}
          closeModal={() => {
            setUserToUpdate(undefined);
            setModalOpen(undefined);
          }}
          onClickUpdateStatus={() => {
            setModalOpen(
              SettingsPermissionsModals.UPDATE_USER_STATUS_CONFIRMATION
            );
          }}
        />
      )}
      {modalOpen === SettingsPermissionsModals.ADD_USERS && (
        <AddWorkspaceUsersModal
          workspace={workspace}
          closeModal={() => {
            setUserToUpdate(undefined);
            setModalOpen(undefined);
          }}
        />
      )}
      <Stack gap="100" width="100" height="100">
        <Inline width="100" spaceBetween gap="150" wrap>
          <Inline gap="150" alignment="left">
            <SearchBar
              withDebounce
              placeholder={d("ui.common.search")}
              autoFocus
              value={searchFilter ?? ""}
              handleChange={onChangeSearchTerm}
            />
            <DropdownMenu
              isOpen={filterKebabIsOpen}
              onOpenChange={setFilterKebabIsOpen}
              trigger={({ onClick }) => (
                <IconButton
                  name="filter_alt"
                  onClick={onClick}
                  color={showDeactivated === "true" ? "color" : "primary"}
                />
              )}
            >
              <DropdownItemGroup>
                <DropdownItemCheckbox
                  id="showHidden"
                  leftElement={<Icon name="visibility_off" />}
                  onChange={handleToggleShowDeactivated}
                  isChecked={showDeactivated === "true"}
                  description={d(
                    "ui.settings.permissions.showDeactivated.description"
                  )}
                >
                  {d("ui.settings.permissions.showDeactivated.label")}
                </DropdownItemCheckbox>
              </DropdownItemGroup>
            </DropdownMenu>
          </Inline>
          <ButtonGroup>
            <Button
              label={d("ui.settings.permissions.addUsers.button")}
              onClick={() => {
                setModalOpen(SettingsPermissionsModals.ADD_USERS);
              }}
            />
          </ButtonGroup>
        </Inline>
        <TableWithPagination
          isLoading={workspaceUsers === undefined}
          fillContainer
          columns={permissionsColumns}
          data={formattedWorkspaceUsers}
          itemsPerPage={workspaceUsers?.page.pageSize}
          startingPage={page ? parseInt(page) : 1}
          totalCount={workspaceUsers?.total ?? 0}
          isControlledOutside={true}
          onChangePage={onChangePage}
          handleSort={handleSetSort}
          sort={sort}
          rowKeyAccessor="userId"
          onRowClick={workspaceUser => {
            setUserToUpdate(workspaceUser);
            setModalOpen(SettingsPermissionsModals.UPDATE_USER);
          }}
          kebabMenu={kebabMenu}
        />
      </Stack>
    </PageBodyTemplate>
  );
};

const levelColumnFormatter =
  (level: `${WorkspaceAccessLevel}`) => (workspaceUser: WorkspaceUser) => {
    if (!workspaceUser.levels.includes(level)) {
      return <></>;
    }
    return (
      <Icon
        style={{
          padding: "var(--spacing-025) 0",
          opacity:
            workspaceUser.status === WorkspaceUserStatus.INACTIVE ? 0.2 : 1
        }}
        name="check"
      />
    );
  };

function getPermissionsColumns(d: Dictionary): TableColumn<WorkspaceUser>[] {
  return [
    {
      header: d("ui.common.name"),
      key: "name",
      canSort: true,
      formatter: (workspaceUser: WorkspaceUser) => (
        <Inline
          alignment="left"
          gap="100"
          style={{
            padding: "var(--spacing-050) var(--spacing-025)",
            opacity:
              workspaceUser.status === WorkspaceUserStatus.INACTIVE ? 0.5 : 1
          }}
        >
          <Avatar
            user={{
              firstName: workspaceUser?.firstName,
              lastName: workspaceUser?.lastName,
              image: workspaceUser.image || ""
            }}
            size="s"
          />
          <Stack gap="025">
            <Inline gap="050">
              <Text>
                {workspaceUser?.firstName} {workspaceUser?.lastName}
              </Text>
              {workspaceUser.isOwner && (
                <Pill label="Workspace owner" variant="neutral" />
              )}
              {workspaceUser.status === WorkspaceUserStatus.INACTIVE && (
                <Pill
                  label={d(
                    `ui.settings.permissions.status.${WorkspaceUserStatus.INACTIVE}.label`
                  )}
                />
              )}
            </Inline>
            <Text size="s" color="text-tertiary">
              {workspaceUser.email}
            </Text>
          </Stack>
        </Inline>
      )
    },
    ...Object.values(WorkspaceAccessLevel).map(level => ({
      header: d(`ui.settings.permissions.level.${level}.label`),
      key: level,
      canSort: true,
      formatter: levelColumnFormatter(level),
      options: {
        alignment: TextAlignment.CENTER
      }
    }))
  ];
}

const WorkspaceUserRowKebabMenu = ({
  row,
  props,
  closeMenu,
  setUserToUpdate,
  setModalOpen
}: {
  row: WorkspaceUser;
  props: KebabMenuProps;
  closeMenu: () => void;
  setUserToUpdate: (user: WorkspaceUser) => void;
  setModalOpen: (modal: SettingsPermissionsModals) => void;
}) => {
  const d = useDictionary();
  const { user: authUser } = useAuth();
  return (
    <KebabMenu {...props}>
      <DropdownItemGroup>
        {row.status === WorkspaceUserStatus.ACTIVE && (
          <>
            <DropdownItem
              id="edit"
              onClick={() => {
                setUserToUpdate(row);
                setModalOpen(SettingsPermissionsModals.UPDATE_USER);
                closeMenu();
              }}
              leftElement={<Icon name="edit" />}
            >
              {d(`ui.settings.permissions.updateUser.title`)}
            </DropdownItem>
            {authUser?.id !== row.userId && (
              <DropdownItem
                id="deactivate"
                onClick={() => {
                  setUserToUpdate(row);
                  setModalOpen(
                    SettingsPermissionsModals.UPDATE_USER_STATUS_CONFIRMATION
                  );
                  closeMenu();
                }}
                leftElement={
                  <Icon name="do_not_disturb_on" color="traffic-danger" />
                }
                description={d(
                  `ui.settings.permissions.status.${WorkspaceUserStatus.INACTIVE}.action.description`
                )}
              >
                <Text color="traffic-onDanger">
                  {d(
                    `ui.settings.permissions.status.${WorkspaceUserStatus.INACTIVE}.action.label`
                  )}
                </Text>
              </DropdownItem>
            )}
          </>
        )}
        {row.status === WorkspaceUserStatus.INACTIVE && (
          <DropdownItem
            id="activate"
            onClick={() => {
              setUserToUpdate(row);
              setModalOpen(
                SettingsPermissionsModals.UPDATE_USER_STATUS_CONFIRMATION
              );
              closeMenu();
            }}
            leftElement={<Icon name="add_circle" />}
            description={d(
              `ui.settings.permissions.status.${WorkspaceUserStatus.ACTIVE}.action.description`
            )}
          >
            {d(
              `ui.settings.permissions.status.${WorkspaceUserStatus.ACTIVE}.action.label`
            )}
          </DropdownItem>
        )}
      </DropdownItemGroup>
    </KebabMenu>
  );
};
