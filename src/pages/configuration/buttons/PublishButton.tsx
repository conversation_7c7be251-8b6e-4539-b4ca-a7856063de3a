import React from "react";

import {
  Button,
  ButtonVariant,
  ToastNotificationVariant
} from "@oneteam/onetheme";
import { generatePath, useLocation, useParams } from "react-router-dom";

import { PushToastNotification } from "@pages/workspace/WorkspaceLayout";

import { routeConstants } from "@src/constants/routeConstants";
import { Workspace } from "@src/types/workspace.ts";

import { usePublishWorkspaceVersion } from "./usePublishWorkspaceVersion";

export interface PublishButtonProps {
  workspace: Workspace;
  hasErrors: boolean;
  pushToastNotification: PushToastNotification;
}

export const PublishButton = ({
  workspace,
  hasErrors,
  pushToastNotification
}: PublishButtonProps) => {
  const { pathname } = useLocation();
  const { workspaceKey } = useParams();
  const { publishWorkspaceVersion } = usePublishWorkspaceVersion({ workspace });

  if (
    !pathname.startsWith(
      generatePath(routeConstants.configuration, { workspaceKey })
    ) ||
    !workspace
  ) {
    return <></>;
  }

  return (
    <Button
      width="100"
      leftIcon={{ name: "upload_file" }}
      label="Publish"
      variant={ButtonVariant.SECONDARY}
      onClick={() => {
        (document.activeElement as HTMLElement)?.blur();

        if (hasErrors) {
          pushToastNotification(
            "There are errors in the configuration",
            "Please fix them before publishing",
            ToastNotificationVariant.DANGER
          );

          return;
        }
        publishWorkspaceVersion();
      }}
    />
  );
};
