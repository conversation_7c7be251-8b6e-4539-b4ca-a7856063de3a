import { useMemo } from "react";

import { useMutation } from "@tanstack/react-query";

import { postData } from "@helpers/postData";

import { Workspace } from "@src/types/workspace";

export const usePublishWorkspaceVersion = ({
  workspace
}: {
  workspace: Workspace;
}) => {
  const versionApiPath = useMemo(() => {
    return workspace
      ? `/workspaces/${workspace.id}/configuration/versions`
      : "";
  }, [workspace]);

  const { mutate: publishWorkspaceVersion } = useMutation({
    mutationFn: () => postData(versionApiPath, {}),
    onSuccess: data => {
      console.log("version id:", data.id);
    },
    onError: error => {
      console.error("Failed to create version", error);
    }
  });

  return {
    publishWorkspaceVersion
  };
};
