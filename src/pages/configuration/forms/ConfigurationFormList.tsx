import React, { use<PERSON><PERSON>back, useMemo, useState } from "react";

import { Doc } from "@automerge/automerge-repo";
import {
  Button,
  ButtonGroup,
  ColorText,
  FontWeight,
  Inline,
  PageBodyTemplate,
  Pill,
  PillVariant,
  SearchBar,
  SelectValue,
  StatusLine,
  StatusLineVariant,
  TableColumn,
  TableWithPagination,
  Text,
  TextAlignment,
  TextSize,
  Tooltip,
  useQueryParams,
  useTableSort
} from "@oneteam/onetheme";
import {
  generatePath,
  useNavigate,
  useOutletContext,
  useParams,
  useSearchParams
} from "react-router-dom";

import { DocumentErrorHelper } from "@helpers/DocumentErrorHelper.ts";
import { PaginatedTable, Sort } from "@helpers/PaginatedTable.ts";
import { WorkspaceConfigurationHelper } from "@helpers/workspace/WorkspaceConfigurationHelper.ts";

import { ConfigurationLabelsSelect } from "@components/shared/ConfigurationLabelsSelect/ConfigurationLabelsSelect.tsx";

import { formatDate } from "@pages/collection/flows/FlowExecutionHelper.ts";

import { commonIcons } from "@src/constants/iconConstants.ts";
import { routeConstants } from "@src/constants/routeConstants.ts";
import { useBreadcrumbs } from "@src/hooks/useBreadcrumbs.tsx";
import { Dictionary, useDictionary } from "@src/hooks/useDictionary.tsx";
import { ConfigurationFormType } from "@src/types/FormConfiguration.ts";
import { LabelAvailableTo, labelColorToPill } from "@src/types/Label.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import "./ConfigurationFormList.scss";
import { ConfigurationFormListModal } from "./ConfigurationFormListModal.tsx";

export const ConfigurationFormList = () => {
  const d = useDictionary();
  const navigate = useNavigate();
  const breadcrumbs = useBreadcrumbs();
  const params = useParams();

  const { document, docErrorHelper } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    docErrorHelper: DocumentErrorHelper;
  }>();

  const { queryParams, updateQueryParams } = useQueryParams({
    initialDefaultIfNone: { page: "1" },
    useSearchParams
  });

  const columns = getColumns(d, document);

  const { status, textFilter, labels, page, pageSize, asc, dsc } = useMemo(
    () => ({
      status: queryParams.get("status") ?? undefined,
      textFilter: queryParams.get("search") ?? undefined,
      labels: queryParams.get("labels") ?? undefined,
      page: queryParams.get("page") ?? undefined,
      pageSize: queryParams.get("pageSize") ?? undefined,
      asc: queryParams.get("asc") ?? undefined,
      dsc: queryParams.get("dsc") ?? undefined
    }),
    [queryParams]
  );

  const { sort, handleSetSort } = useTableSort<ConfigurationFormType>({
    columns,
    queryParams,
    updateQueryParams: newParams => {
      updateQueryParams({
        page: newParams.page,
        pageSize: pageSize,
        search: textFilter,
        status,
        labels,
        asc: newParams.asc,
        dsc: newParams.dsc
      });
    }
  });

  const configurationForms: ConfigurationFormType[] = useMemo(() => {
    const tempForms = Object.values(document.forms ?? {});
    tempForms.forEach((form: ConfigurationFormType) => {
      form.hasErrors =
        docErrorHelper.getErrorCountByPrefix(`$.forms.${form?.id}`) > 0;
      form.foundationName =
        document.foundations.entities[form.foundationId]?.name ?? "";
      if (form.seriesId) {
        const workspaceConfigurationHelper = new WorkspaceConfigurationHelper(
          document
        );
        const series = workspaceConfigurationHelper.findSeriesConfig(
          form.seriesId
        );
        form.seriesName = series?.name;
      }
    });
    return tempForms;
  }, [document, docErrorHelper]);

  const doUpdateQueryParams = useCallback(
    (newParams: object) => {
      const current = {
        page: page,
        pageSize: pageSize,
        search: textFilter,
        status,
        labels,
        asc: asc,
        dsc: dsc
      };
      // merge new properties into current
      updateQueryParams({
        ...current,
        ...newParams
      });
    },
    [page, pageSize, textFilter, status, labels, asc, dsc, updateQueryParams]
  );

  // on change handlers
  const onChangePage = useCallback(
    (newPage: number) => {
      doUpdateQueryParams({ page: String(newPage) });
    },
    [doUpdateQueryParams]
  );

  const onChangeSearchTerm = useCallback(
    (input: string) => {
      doUpdateQueryParams({
        search: input,
        page: "1", // reset page to 1 when changing filter criteria since the total count may change
        pageSize: pageSize
      });
    },
    [doUpdateQueryParams, pageSize]
  );

  const handleLabelSelect = useCallback(
    (value?: SelectValue) => {
      doUpdateQueryParams({ labels: value?.toString() });
    },
    [doUpdateQueryParams]
  );

  const [formCreationOpen, setFormCreationOpen] = useState(false);

  const table = useMemo(() => {
    return new PaginatedTable(
      configurationForms,
      page ? parseInt(page) : 1,
      [
        (data: ConfigurationFormType) =>
          !textFilter ||
          data.name.toLowerCase().includes(textFilter.toLowerCase()),
        (data: ConfigurationFormType) => !status || data.status === status,
        (data: ConfigurationFormType) => {
          const l = labels?.split(",") ?? [];
          return (
            l.length === 0 ||
            (!!data.labels &&
              l.every(labelId => data.labels?.includes(labelId)))
          );
        }
      ],
      Sort.from(asc, dsc)
    );
  }, [configurationForms, page, asc, dsc, textFilter, status, labels]);

  return (
    <PageBodyTemplate
      heading={d("ui.configuration.forms.title")}
      breadcrumbs={breadcrumbs}
      overlay={
        formCreationOpen ? (
          <ConfigurationFormListModal
            onOpenChange={() => setFormCreationOpen(!formCreationOpen)}
          />
        ) : (
          <></>
        )
      }
      withoutScroll
    >
      <Inline gap="100" spaceBetween wrap>
        <Inline gap="100" alignment="left">
          <SearchBar
            withDebounce
            handleChange={onChangeSearchTerm}
            placeholder={d("ui.common.search")}
            value={textFilter ?? ""}
            autoFocus
            style={{ minWidth: "164px", width: "164px" }}
          />
          <ConfigurationLabelsSelect
            name="labels"
            value={labels?.split(",") ?? []}
            labelFor={LabelAvailableTo.FORM_CONFIGURATION}
            onChange={handleLabelSelect}
            placeholder={d("ui.configuration.labels.filter.placeholder")}
            width="fit"
            withSelectAll={false}
          />
        </Inline>
        <ButtonGroup alignment="right">
          <Button
            variant={!configurationForms.length ? "primary" : "secondary"}
            label={d("ui.configuration.series.manageButton")}
            leftIcon={commonIcons.series}
            onClick={() => {
              navigate(
                generatePath(routeConstants.configurationSeries, {
                  ...params
                })
              );
            }}
          />
          <Button
            variant={!configurationForms.length ? "secondary" : "primary"}
            label={d("ui.configuration.forms.creation.title")}
            onClick={() => setFormCreationOpen(true)}
          />
        </ButtonGroup>
      </Inline>

      <TableWithPagination
        isLoading={configurationForms === undefined}
        fillContainer
        columns={columns}
        data={table.dataForPage()}
        itemsPerPage={table.pageSize}
        startingPage={table.page}
        totalCount={table.size()}
        noDataText={d("ui.common.noData")}
        isControlledOutside={true}
        onChangePage={onChangePage}
        handleSort={handleSetSort}
        sort={sort}
        rowKeyAccessor="id"
        onRowClick={(configurationForm: ConfigurationFormType) =>
          navigate(
            generatePath(routeConstants.configurationForm, {
              ...params,
              configurationFormKey: configurationForm.key
            })
          )
        }
      />
    </PageBodyTemplate>
  );
};

function getColumns(
  d: Dictionary,
  document: WorkspaceDocument
): TableColumn<ConfigurationFormType>[] {
  return [
    {
      header: "",
      key: "hasErrors",
      options: {
        width: "var(--spacing-000)",
        paddingLess: true
      },
      formatter: (value: ConfigurationFormType) => {
        return value.hasErrors ? (
          <Inline className="form-list-status-line-container">
            <StatusLine
              variant={StatusLineVariant.DANGER}
              className="form-list-status-line"
            />
          </Inline>
        ) : (
          <></>
        );
      }
    },
    {
      header: d("ui.forms.fields.name.label"),
      key: "name",
      canSort: true,
      formatter: (data: ConfigurationFormType) => (
        <Inline alignment="left" gap="050">
          <Text weight={FontWeight.MEDIUM}>{data.name}</Text>
          <Pill key={data.key} label={data.key ?? ""} />
        </Inline>
      )
    },
    {
      header: d("ui.terminology.foundationConfigurationLevel"),
      key: "foundationName",
      canSort: true,
      formatter: (data: ConfigurationFormType) => (
        <Inline alignment="left" gap="100">
          <Pill label={data.foundationName ?? ""} />
        </Inline>
      )
    },
    {
      header: d("ui.terminology.series"),
      key: "series",
      formatter: (data: ConfigurationFormType) => {
        if (!data.seriesId) {
          return <></>;
        }

        const workspaceConfigurationHelper = new WorkspaceConfigurationHelper(
          document
        );
        const series = workspaceConfigurationHelper.findSeriesConfig(
          data.seriesId
        );
        return (
          <Inline alignment="left" gap="100">
            {series?.name && <Pill label={series.name} />}
          </Inline>
        );
      }
    },
    {
      header: d("ui.configuration.labels.title"),
      key: "labels",
      formatter: (data: ConfigurationFormType) => {
        if (!data.labels?.length) {
          return;
        }
        return (
          <Inline wrap gap="050">
            {data.labels.map((labelId: string) => {
              const label = document.labels[labelId];
              return (
                <Pill
                  key={label.id}
                  label={label.name}
                  {...(label.color
                    ? labelColorToPill[label.color]
                    : {
                        variant: PillVariant.NEUTRAL
                      })}
                />
              );
            })}
          </Inline>
        );
      }
    },
    {
      header: d("ui.common.lastModified"),
      key: "metadata.updatedAt",
      canSort: true,
      options: {
        alignment: TextAlignment.RIGHT,
        color: ColorText.TERTIARY,
        size: TextSize.S
      },
      formatter: (data: ConfigurationFormType) => (
        <Inline alignment="left" gap="100">
          {data.metadata && (
            <Tooltip content={data.metadata.updatedAt} delay={500}>
              <Text size="s" color="text-tertiary">
                {formatDate(data.metadata.updatedAt)}
              </Text>
            </Tooltip>
          )}
        </Inline>
      )
    }
  ];
}
