import React, { use<PERSON><PERSON>back, useMemo } from "react";

import { Doc } from "@automerge/automerge-repo";
import {
  <PERSON>ert,
  AlertVariant,
  Box,
  Floating,
  FloatingPosition,
  Form,
  Modal,
  Overlay,
  Stack
} from "@oneteam/onetheme";
import { useFormContext } from "react-hook-form";
import {
  generatePath,
  useNavigate,
  useOutletContext,
  useParams
} from "react-router-dom";

import { customNanoId } from "@helpers/customNanoIdHelper.ts";

import { ConfigurationLabelsSelect } from "@components/shared/ConfigurationLabelsSelect/ConfigurationLabelsSelect";
import { OTAIFormField } from "@components/shared/OTAIForm/OTAIFormField.tsx";

import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { routeConstants } from "@src/constants/routeConstants";
import { Dictionary, useDictionary } from "@src/hooks/useDictionary.tsx";
import {
  ConfigurationFormType,
  formConfigSchema
} from "@src/types/FormConfiguration.ts";
import { LabelAvailableTo } from "@src/types/Label";
import { Question, QuestionTypes } from "@src/types/Question.ts";
import {
  DynamicOptionTags,
  SelectQuestionProperties
} from "@src/types/QuestionProperties.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import "./ConfigurationFormListModal.scss";

interface ConfigurationFormListModalFieldsProps {
  serverError?: string;
  d: Dictionary;
}

const ConfigurationFormListModalFields = ({
  serverError,
  d
}: ConfigurationFormListModalFieldsProps) => {
  const { watch } = useFormContext();

  const foundationConfigurationSelect =
    useMemo((): Question<SelectQuestionProperties> => {
      return {
        id: "foundationId",
        identifier: "foundationIdSelect",
        text: d("ui.terminology.foundationConfigurationLevel"),
        type: QuestionTypes.SELECT,
        properties: {
          required: true,
          dynamicOptions: {
            tag: DynamicOptionTags.FOUNDATION_CONFIGURATION_ID
          }
        },
        description: d("ui.configuration.forms.creation.foundation.description")
      };
    }, [d]);

  const seriesSelect = useMemo((): Question<SelectQuestionProperties> => {
    return {
      id: "seriesId",
      identifier: "seriesIdSelect",
      text: d("ui.terminology.series"),
      type: QuestionTypes.SELECT,
      properties: {
        required: false,
        dynamicOptions: {
          tag: DynamicOptionTags.SERIES_CONFIGURATION_ID
        }
      }
    };
  }, [d]);

  return (
    <Stack gap="150" width="100" contentsWidth="100">
      <OTAIFormField
        question={foundationConfigurationSelect}
        id={"foundationId"}
        autoFocus
      />
      <Form.TextField
        label={d("ui.configuration.forms.creation.name")}
        name="name"
        required
      />
      <Form.TextField
        label={d("ui.configuration.forms.creation.key")}
        name="key"
        value={watch("key")?.toUpperCase()}
        required
      />
      <OTAIFormField question={seriesSelect} id={"seriesId"} />
      <Form.TextAreaField
        label={d("ui.configuration.forms.creation.description")}
        name="description"
      />
      <ConfigurationLabelsSelect
        isInForm
        label={d("ui.configuration.labels.title")}
        name="labels"
        labelFor={LabelAvailableTo.FORM_CONFIGURATION}
      />
      {serverError && (
        <Alert variant={AlertVariant.DANGER}>{d(serverError)}</Alert>
      )}
    </Stack>
  );
};

interface ConfigurationFormListModalProps {
  onOpenChange: () => void;
}

const emptyFormConfig: Partial<ConfigurationFormType> = {
  name: "",
  key: "",
  description: "",
  seriesId: "",
  level: 0,
  foundationId: "",
  content: []
};

export const ConfigurationFormListModal = ({
  onOpenChange
}: ConfigurationFormListModalProps) => {
  const { document, docChange } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    docChange: DocChange;
  }>();

  const navigate = useNavigate();
  const d = useDictionary();
  const params = useParams();

  const existingFormKeys: string[] = useMemo(() => {
    return Object.values(document?.forms ?? {})?.map(form => form.key);
  }, [document.forms]);

  const saveFormConfiguration = useCallback(
    ({
      name,
      key,
      description,
      foundationId,
      seriesId,
      labels = []
    }: {
      name: string;
      key: string;
      description: string;
      foundationId: string;
      seriesId: string;
      labels: string[];
    }) => {
      const id = customNanoId();
      docChange(d => {
        d.forms ??= {};
        d.forms[id] = {
          name,
          key,
          level: 0,
          id,
          description,
          metadata: {
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          labels,
          foundationId: foundationId ?? "",
          seriesId: seriesId ?? "",
          content: []
        };
      });
      onOpenChange();
      navigate(
        generatePath(routeConstants.configurationForm, {
          ...params,
          configurationFormKey: key
        })
      );
    },
    [docChange, navigate, onOpenChange, params]
  );

  return (
    <Box style={{ zIndex: 10000 }}>
      <Overlay isOpen></Overlay>
      <Floating position={FloatingPosition.CENTER}>
        <Modal
          className="configuration-form-list-modal"
          onOpenChange={onOpenChange}
          heading={d("ui.configuration.forms.creation.title")}
        >
          <Form
            schema={formConfigSchema(d, existingFormKeys)}
            submitLabel={d("ui.configuration.forms.creation.submit")}
            d={d}
            handleSubmit={saveFormConfiguration}
            handleCancel={onOpenChange}
            defaultValues={emptyFormConfig}
          >
            <ConfigurationFormListModalFields d={d} />
          </Form>
        </Modal>
      </Floating>
    </Box>
  );
};
