import React, { PropsWithChildren, useEffect, useMemo, useRef } from "react";

import {
  Alert,
  Box,
  Button,
  Card,
  FontWeight,
  Form,
  Icon,
  IconSize,
  Inline,
  Stack,
  Text,
  TextSize
} from "@oneteam/onetheme";
import { UseFormReturn } from "react-hook-form";
import { generatePath, useNavigate, useParams } from "react-router-dom";

import {
  getResourceEntitiesInOrder,
  getResourceLength,
  mapOverResource
} from "@helpers/OrderedMapNoState.ts";

import { ConfigurationFoundationLine } from "@pages/configuration/foundations/ConfigurationFoundationLine/ConfigurationFoundationLine";

import { commonIcons } from "@src/constants/iconConstants";
import { routeConstants } from "@src/constants/routeConstants";
import { useDictionary } from "@src/hooks/useDictionary";
import {
  FoundationConfiguration,
  FoundationFormType
} from "@src/types/FoundationConfiguration";
import { Resource } from "@src/types/documentTypes.ts";

import { ConfigurationFoundationBlock } from "../foundations/ConfigurationFoundationBlock/ConfigurationFoundationBlock";

export interface ConfigurationDashboardFoundationPanelProps {
  foundations: Resource<FoundationConfiguration>;
}

export const ConfigurationDashboardFoundationPanel = (
  props: PropsWithChildren<ConfigurationDashboardFoundationPanelProps>
) => {
  const { foundations } = props;
  const d = useDictionary();
  const navigate = useNavigate();
  const params = useParams();

  const getPathToFoundation = () =>
    generatePath(routeConstants.configurationFoundation, {
      ...params
    });

  const navigateToFoundationLevel = (id: string) => {
    const path = getPathToFoundation();
    navigate({
      pathname: path,
      hash: `foundation=${id}`
    });
  };

  const formRef = useRef<UseFormReturn<FoundationFormType> | null>(null);
  const setForm = (form: UseFormReturn<FoundationFormType>) => {
    formRef.current = form;
  };

  const foundationsArray = useMemo(
    () => mapOverResource(foundations, entity => entity),
    [foundations]
  );

  const foundationsInOrder = useMemo(
    () => getResourceEntitiesInOrder(foundations),
    [foundations]
  );

  useEffect(() => {
    formRef.current?.setValue?.("foundations", foundationsInOrder);
  }, [foundationsInOrder]);

  const renderContent = () => {
    if (getResourceLength(foundations) === 1) {
      const path = getPathToFoundation();
      return (
        <Box
          style={{
            height: "500px",
            containerType: "inline-size"
            // background: "var(--color-surface-primary)"
          }}
          alignment="center"
        >
          <Stack gap="100" contentsWidth="fit" alignment="center">
            <Alert variant="danger">
              {d("ui.configuration.foundations.dashboardPanel.empty")}
            </Alert>
            <Button
              label={d("ui.configuration.foundations.dashboardPanel.emptyCTA")}
              leftIcon={commonIcons.foundations}
              rightIcon={{ name: "chevron_right" }}
              onClick={() => navigate(path)}
              variant="secondary"
            />
          </Stack>
        </Box>
      );
    }

    return (
      <Card
        style={{
          background: "var(--color-surface-primary)",
          height: "500px",
          overflow: "scroll",
          containerType: "inline-size"
        }}
        className="configuration-dashboard-panel__content-container"
      >
        <Stack alignment={"top-center"}>
          {foundationsArray.map(
            (foundationConfiguration: FoundationConfiguration, i: number) => {
              return (
                <React.Fragment
                  key={`foundation-${foundationConfiguration.id}`}
                >
                  {/* line shouldn't show for root */}
                  {i !== 0 && (
                    <ConfigurationFoundationLine
                      prevFoundationName={
                        i === 0
                          ? foundationsArray[0]?.name
                          : foundationsArray[i - 1]?.name
                      }
                      foundationConfiguration={foundationConfiguration}
                    />
                  )}
                  <ConfigurationFoundationBlock
                    id={foundationConfiguration.id}
                    index={i}
                    accessor={`foundations.${i}`}
                    isDisabled={i === 0}
                    isRenamable={false}
                    handlers={{
                      handleOnClick: navigateToFoundationLevel
                    }}
                    size="container"
                  />
                </React.Fragment>
              );
            }
          )}
        </Stack>
      </Card>
    );
  };

  return (
    <Card size="large">
      <Stack gap="200">
        <Box alignment="left">
          <Inline gap="050" alignment="center">
            <Icon {...commonIcons.foundations} size={IconSize.LARGE} />
            <Text weight={FontWeight.MEDIUM} size={TextSize.L}>
              {d("ui.configuration.foundations.dashboardPanel.title")}
            </Text>
          </Inline>
        </Box>
        <Box>
          <Form<FoundationFormType>
            handleSubmit={() => {}}
            d={d}
            hideFormButtons
            defaultValues={{ foundations: foundationsInOrder }}
            disabled={true}
            setForm={setForm}
          >
            {renderContent()}
          </Form>
        </Box>
      </Stack>
    </Card>
  );
};
