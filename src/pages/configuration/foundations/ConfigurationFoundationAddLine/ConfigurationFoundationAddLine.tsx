import React, { useState } from "react";

import { WhiteboardAddButton, WhiteboardLine } from "@oneteam/onetheme";

interface FoundationAddLineProps {
  startCreatingFoundation: () => void;
}

export const ConfigurationFoundationAddLine = ({
  startCreatingFoundation
}: FoundationAddLineProps) => {
  const [isHighlighted, setIsHighlighted] = useState(false);
  return (
    <WhiteboardLine
      elementPosition="bottom"
      height={50}
      isHighlighted={isHighlighted}
      onMouseEnter={() => setIsHighlighted(true)}
      onMouseLeave={() => setIsHighlighted(false)}
      element={() => (
        <WhiteboardAddButton
          isHighlighted={isHighlighted}
          onClick={startCreatingFoundation}
        />
      )}
    />
  );
};
