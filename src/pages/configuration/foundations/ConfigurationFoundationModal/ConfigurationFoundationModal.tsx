import React, { useCallback, useMemo, useState } from "react";

import { Doc } from "@automerge/automerge-repo";
import { updateText } from "@automerge/automerge/next";
import { Form, Heading, Stack, TextAreaField } from "@oneteam/onetheme";
import { useFormContext } from "react-hook-form";
import { useOutletContext } from "react-router-dom";

import { getResourceEntitiesInOrder } from "@helpers/OrderedMapNoState";
import { getByPath, initialSetupName } from "@helpers/configurationFormHelper";

import { FloatingModal } from "@components/shared/FloatingModal/FloatingModal.tsx";
import { UniqueIdentifierField } from "@components/shared/UniqueIdentifier";

import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { useConfigurationFormError } from "@src/hooks/formConfiguration/useConfigurationFormError";
import { useCheckFoundationDuplicateIdentifier } from "@src/hooks/uniqueIdentifier/useCheckFoundationDuplicateIdentifier";
import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { FormPath } from "@src/types/Form";
import { FoundationConfiguration } from "@src/types/FoundationConfiguration.ts";
import { WorkspaceDocument } from "@src/types/documentTypes";

export const ConfigurationFoundationModal = ({
  accessor,
  onClose,
  docChange,
  id,
  disabled = false
}: {
  accessor: string;
  onClose: (foundationConfiguration: FoundationConfiguration) => void;
  docChange: DocChange;
  id: string;
  disabled?: boolean;
}) => {
  const [width, setWidth] = useState<string | undefined>();

  const d = useDictionary();
  const { getValues } = useFormContext(); // will update the fields on the main page
  const docPath = useMemo(() => ["foundations", "entities", id], [id]);
  const { document } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
  }>();

  const foundationConfiguration = useMemo(() => {
    return getByPath<FoundationConfiguration>(document, docPath);
  }, [docPath, document]);

  const isRootFoundation = useMemo(
    () => foundationConfiguration?.id === document.foundations.order[0],
    [document.foundations.order, foundationConfiguration?.id]
  );

  const { autogenerateIdentifier } = useCheckFoundationDuplicateIdentifier();

  const { getServerError } = useConfigurationFormError(docPath);

  const getErrors = useCallback(() => {
    const error = getServerError("identifier");
    if (error) {
      return error.message;
    }
    return undefined;
  }, [getServerError]);

  if (!id || !document.foundations?.entities?.[id]) {
    return <></>;
  }
  return (
    <FloatingModal
      className="configuration-foundation-modal"
      onClose={() => {
        onClose(getValues(accessor));
      }}
      width={width}
      setWidth={setWidth}
    >
      <Stack gap="100" contentsWidth="100">
        <Heading size="s">
          <Form.Renamable
            disabled={disabled}
            name={`${accessor}.name`}
            onChange={text => {
              if (text?.length > 500) {
                return;
              }
              docChange(d => {
                const foundationConfiguration = getByPath(
                  d,
                  docPath
                ) as FoundationConfiguration;
                if (!foundationConfiguration) {
                  console.error("Foundation not found", docPath);
                  return;
                }

                foundationConfiguration.name = text;

                foundationConfiguration.identifier = autogenerateIdentifier(
                  foundationConfiguration,
                  getResourceEntitiesInOrder(document.foundations)
                );
              });
            }}
            controlFocus={getValues(`${accessor}.name`)?.includes(
              initialSetupName
            )}
          />
        </Heading>
        <UniqueIdentifierField
          disabled={
            isRootFoundation ? !!foundationConfiguration.identifier : false
          }
          label={d("ui.configuration.forms.question.identifier.label")}
          description={d(
            "ui.configuration.forms.question.identifier.description"
          )}
          name={`${accessor}.identifier`}
          maxLength={100}
          width="100"
          configPath={docPath as FormPath}
          autoGeneratedIdentifier={""}
          value={foundationConfiguration.identifier}
          error={getErrors()}
          onChange={value => {
            docChange(d => {
              const foundationConfiguration = getByPath(
                d,
                docPath
              ) as FoundationConfiguration;

              foundationConfiguration.identifier = value;
            });
          }}
          identifier={foundationConfiguration.identifier}
          onRefresh={() => {
            docChange(d => {
              const foundationConfiguration = getByPath(
                d,
                docPath
              ) as FoundationConfiguration;

              const newIdentifier = autogenerateIdentifier(
                foundationConfiguration,
                getResourceEntitiesInOrder(document.foundations)
              );

              foundationConfiguration.identifier = newIdentifier;
            });
          }}
        />
        <TextAreaField
          label={d("ui.common.description")}
          name={`${accessor}.description`}
          onChange={e => {
            docChange(d => {
              updateText(d, [...docPath, "description"], e);
            });
          }}
          disabled={disabled}
        />
      </Stack>
    </FloatingModal>
  );
};
