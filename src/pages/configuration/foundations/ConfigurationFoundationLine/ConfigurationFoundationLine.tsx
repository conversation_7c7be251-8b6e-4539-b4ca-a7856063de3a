import React, { useCallback } from "react";

import {
  PillSelect,
  PillSelectVariant,
  SelectValue,
  WhiteboardLine,
  WhiteboardLineElement
} from "@oneteam/onetheme";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import {
  FoundationConfiguration,
  FoundationRelationship
} from "@src/types/FoundationConfiguration.ts";

import "./ConfigurationFoundationLine.scss";

export const ConfigurationFoundationLine = ({
  foundationConfiguration,
  prevFoundationName
}: {
  foundationConfiguration: FoundationConfiguration;
  prevFoundationName: string;
}) => {
  const d = useDictionary();

  const selectPill: WhiteboardLineElement = useCallback(
    () => (
      <PillSelect
        // Disabled until more relationships are accepted
        disabled
        variant={PillSelectVariant.LIGHT}
        required
        value={foundationConfiguration.relationship}
        options={Object.values(FoundationRelationship).map(value => ({
          value: value,
          label: d(`ui.configuration.foundations.relationship.${value}.label`),
          description: d(
            `ui.configuration.foundations.relationship.${value}.description`,
            {
              prevName: prevFoundationName,
              currName: foundationConfiguration.name
            }
          )
        }))}
        handleChange={(value?: SelectValue) => console.log(value)}
      />
    ),
    [
      d,
      foundationConfiguration.name,
      foundationConfiguration.relationship,
      prevFoundationName
    ]
  );
  return (
    <WhiteboardLine
      className="configuration-foundation-line"
      element={selectPill}
    />
  );
};
