// https://tkdodo.eu/blog/react-query-and-forms
// https://pinjarirehan.medium.com/how-to-handle-scroll-position-like-a-pro-in-react-efa86dfc68a9
// https://www.reddit.com/r/reactjs/comments/111mrek/react_hook_form_tanstack_usequery_async/
import React, { useCallback, useMemo, useState } from "react";

import {
  Alert,
  AlertBackground,
  AlertIcon,
  AlertVariant,
  Box,
  Floating,
  FloatingPosition,
  Form,
  Loading,
  useUrlHash
} from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import {
  addItem,
  getIndexAtId,
  getResourceEntitiesInOrder,
  mapOverResource
} from "@helpers/OrderedMapNoState.ts";
import { getByPath } from "@helpers/configurationFormHelper.ts";
import { customNanoId } from "@helpers/customNanoIdHelper.ts";

import { WhiteboardPageBodyTemplate } from "@components/shared/WhiteboardPageBodyTemplate/WhiteboardPageBodyTemplate.tsx";

import { ConfigurationFoundationList } from "@pages/configuration/foundations/ConfigurationFoundationList.tsx";
import { ConfigurationFoundationModal } from "@pages/configuration/foundations/ConfigurationFoundationModal/ConfigurationFoundationModal";
import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { useCheckFoundationDuplicateIdentifier } from "@src/hooks/uniqueIdentifier/useCheckFoundationDuplicateIdentifier";
import { useBreadcrumbs } from "@src/hooks/useBreadcrumbs.tsx";
import { Dictionary, useDictionary } from "@src/hooks/useDictionary.tsx";
import { useTenantConfig } from "@src/hooks/useTenantConfig.tsx";
import {
  FoundationConfiguration,
  FoundationFormType,
  FoundationRelationship,
  foundationConfigurationListSchema
} from "@src/types/FoundationConfiguration.ts";
import { Resource } from "@src/types/documentTypes.ts";
import { Workspace } from "@src/types/workspace";

import { ConfigurationFoundationAddLine } from "./ConfigurationFoundationAddLine/ConfigurationFoundationAddLine";

interface FoundationsConfigurationFormProps {
  foundations: Resource<FoundationConfiguration>;
  docChange: DocChange;
}

export const ConfigurationFoundationForm = ({
  foundations,
  docChange
}: FoundationsConfigurationFormProps) => {
  const d = useDictionary();
  const breadcrumbs = useBreadcrumbs();
  const { workspace } = useOutletContext<{
    workspace: Workspace;
  }>();
  const tenantConfig = useTenantConfig();

  const [error] = useState("");
  const { updateUrlHash, urlHashDetail } = useUrlHash();

  const selectedFoundationId = useMemo(() => {
    const foundationId = urlHashDetail?.get("foundation");
    if (foundationId === "none") {
      return undefined;
    }
    return foundationId || undefined;
  }, [urlHashDetail]);

  const isRootFoundationSelected = useMemo(
    () =>
      !!selectedFoundationId && foundations.order?.[0] === selectedFoundationId,
    [foundations.order, selectedFoundationId]
  );

  const updateSelectedFoundation = useCallback(
    (id?: string) => {
      updateUrlHash("foundation", "none");
      if (id) {
        setTimeout(() => updateUrlHash("foundation", id), 0);
      }
    },
    [updateUrlHash]
  );

  const handleOnClick = useCallback(
    (id: string) => {
      return updateSelectedFoundation(
        selectedFoundationId === id ? undefined : id
      );
    },
    [selectedFoundationId, updateSelectedFoundation]
  );

  const foundationsInOrder = useMemo(
    () => getResourceEntitiesInOrder(foundations),
    [foundations]
  );

  const { autogenerateIdentifier } = useCheckFoundationDuplicateIdentifier();

  const addFoundation = useCallback(
    () =>
      docChange(doc => {
        const resource = getByPath<Resource<FoundationConfiguration>>(doc, [
          "foundations"
        ]);

        const foundationConfiguration: FoundationConfiguration = {
          id: customNanoId(),
          identifier: "",
          name: uniqueFoundationName(d, foundations),
          description: "",
          relationship: FoundationRelationship.ONE_TO_MANY,
          metadata: {
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        };

        const newIdentifier = autogenerateIdentifier(
          foundationConfiguration,
          resource.order.map(id => resource.entities[id])
        );

        foundationConfiguration.identifier = newIdentifier;

        addItem(foundationConfiguration, resource);
      }),
    [autogenerateIdentifier, d, docChange, foundations]
  );

  if (!workspace?.id) {
    return (
      <Box height="100" contentsHeight="fill">
        <Loading size={24} />
      </Box>
    );
  }

  return (
    <Form<FoundationFormType>
      schema={foundationConfigurationListSchema(d, tenantConfig?.locale)}
      handleSubmit={() => {}}
      d={d}
      hideFormButtons
      defaultValues={{ foundations: foundationsInOrder }}
    >
      <WhiteboardPageBodyTemplate
        name={d("ui.configuration.foundations.title")}
        isRenamable={false}
        breadcrumbs={breadcrumbs}
        floatingLayer={({ parentRef }) => (
          <>
            {/*show edit modal if a foundationConfiguration is selected */}
            <Floating position={FloatingPosition.RIGHT} parentRef={parentRef}>
              <ConfigurationFoundationModal
                id={`${selectedFoundationId ?? ""}`}
                accessor={`foundations.${getIndexAtId(selectedFoundationId ?? "", foundations)}`}
                docChange={docChange}
                onClose={() => {
                  updateSelectedFoundation(undefined);
                }}
                disabled={isRootFoundationSelected}
              />
            </Floating>
          </>
        )}
      >
        <ConfigurationFoundationList
          handleOnClick={handleOnClick}
          selectedFoundationId={selectedFoundationId}
          foundations={foundationsInOrder}
          docChange={docChange}
        />
        <ConfigurationFoundationAddLine
          startCreatingFoundation={addFoundation}
        />
        {error && (
          <Alert
            variant={AlertVariant.DANGER}
            background={AlertBackground.TRANSPARENT}
            icon={AlertIcon.NONE}
          >
            {error}
          </Alert>
        )}
      </WhiteboardPageBodyTemplate>
    </Form>
  );
};

function uniqueFoundationName(
  d: Dictionary,
  foundations: Resource<FoundationConfiguration>
): string {
  const baseName = d("ui.configuration.foundations.placeholderName");
  const existingNames = mapOverResource(
    foundations,
    foundation => foundation.name
  );

  let newName = baseName;
  let index = 1;

  while (existingNames.includes(newName)) {
    newName = `${baseName} ${index++}`;
  }

  return newName;
}
