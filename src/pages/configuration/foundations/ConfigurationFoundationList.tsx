// https://stackoverflow.com/questions/77852334/re-render-form-if-array-changes-with-react-hook-form
import React, { useCallback, useEffect } from "react";

import { Stack } from "@oneteam/onetheme";
import { useFormContext } from "react-hook-form";
import { useOutletContext } from "react-router-dom";

import { ConfigurationFoundationLine } from "@pages/configuration/foundations/ConfigurationFoundationLine/ConfigurationFoundationLine";
import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { useCheckFoundationDuplicateIdentifier } from "@src/hooks/uniqueIdentifier/useCheckFoundationDuplicateIdentifier";
import {
  FoundationConfiguration,
  FoundationFormType
} from "@src/types/FoundationConfiguration";
import { WorkspaceDocument } from "@src/types/documentTypes";
import { Workspace } from "@src/types/workspace";

import { ConfigurationFoundationBlock } from "./ConfigurationFoundationBlock/ConfigurationFoundationBlock";

interface FoundationConfigurationListProps {
  handleOnClick: (id: string) => void;
  selectedFoundationId: string | undefined;
  foundations: FoundationFormType["foundations"];
  docChange: DocChange;
}

export const ConfigurationFoundationList = ({
  handleOnClick,
  selectedFoundationId,
  foundations,
  docChange
}: FoundationConfigurationListProps) => {
  const { setValue, watch } = useFormContext<FoundationFormType>();
  const fields: FoundationConfiguration[] = Object.values(watch("foundations"));
  const { document } = useOutletContext<{
    workspace: Workspace;
    document: WorkspaceDocument;
  }>();

  useEffect(() => {
    setValue("foundations", foundations);
  }, [foundations, setValue]);

  const handleDeleteFoundationLevel = useCallback(
    (foundationId: string, formIdsForFoundation?: string[]) => {
      docChange(d => {
        if (formIdsForFoundation?.length) {
          formIdsForFoundation.forEach(formId => {
            delete d.forms[formId];
          });
        }
        delete d.foundations.entities[foundationId];
        d.foundations.order = d.foundations.order.filter(
          (id: string) => id !== foundationId
        );
      });
    },
    [docChange]
  );

  const { autogenerateIdentifier } = useCheckFoundationDuplicateIdentifier();

  return (
    <Stack alignment={"top-center"}>
      {document?.foundations?.order?.map((foundationId: string, i: number) => {
        const foundationConfiguration =
          document.foundations.entities?.[foundationId];
        if (!foundationConfiguration || !foundationConfiguration.id) {
          return <></>;
        }
        return (
          <React.Fragment key={`foundation-${foundationConfiguration.id}`}>
            {/* line shouldn't show for root */}
            {i !== 0 && (
              <ConfigurationFoundationLine
                prevFoundationName={
                  i === 0 ? fields[0]?.name : fields[i - 1]?.name
                }
                foundationConfiguration={foundationConfiguration}
              />
            )}
            {/* root is disabled */}
            <ConfigurationFoundationBlock
              id={foundationConfiguration.id}
              index={i}
              accessor={`foundations.${i}`}
              handlers={{
                handleRename: e => {
                  docChange(d => {
                    d.foundations.entities[foundationId].name = e.name;
                  });

                  // separate doc changes because autogenerateIdentifier relies on an updated name
                  docChange(d => {
                    const foundationConfiguration =
                      d.foundations.entities[foundationId];
                    d.foundations.entities[foundationId].identifier =
                      autogenerateIdentifier(foundationConfiguration, fields);
                  });
                },
                handleOnClick: () => {
                  handleOnClick(foundationConfiguration.id);
                },
                handleDelete: (foundationId, formIdsForFoundation) => {
                  handleDeleteFoundationLevel(
                    foundationId,
                    formIdsForFoundation
                  );
                }
              }}
              isSelected={selectedFoundationId === foundationConfiguration.id}
              isDisabled={i === 0}
            />
          </React.Fragment>
        );
      })}
    </Stack>
  );
};
