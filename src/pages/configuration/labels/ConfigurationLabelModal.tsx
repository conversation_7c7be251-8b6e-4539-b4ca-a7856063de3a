import React, { useCallback, useEffect, useMemo, useState } from "react";

import { Doc } from "@automerge/automerge-repo";
import {
  Alert,
  AlertVariant,
  Box,
  Card,
  Floating,
  FloatingPosition,
  Form,
  Heading,
  Modal,
  Overlay,
  Pill,
  Stack
} from "@oneteam/onetheme";
import { useFormContext } from "react-hook-form";
import { useOutletContext } from "react-router-dom";

import {
  DocumentErrorHelper,
  ErrorFieldKeyMap
} from "@helpers/DocumentErrorHelper.ts";
import { customNanoId } from "@helpers/customNanoIdHelper.ts";
import { modalZIndex } from "@helpers/modalHelper.ts";
import { valueUniqueness } from "@helpers/stringHelper.ts";

import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { Dictionary, useDictionary } from "@src/hooks/useDictionary.tsx";
import { useTenantConfig } from "@src/hooks/useTenantConfig.tsx";
import {
  LabelAvailableTo,
  LabelColor,
  LabelConfiguration,
  labelColorToOptionColor,
  labelColorToPill,
  labelConfigSchema
} from "@src/types/Label";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import "./ConfigurationLabelModal.scss";

interface ConfigurationLabelModalFieldsProps {
  serverError: string;
  d: Dictionary;
  labelConfigToEditId?: string;
  existingAvailableTo?: LabelConfiguration["availableTo"];
}

const ConfigurationLabelModalFields = ({
  serverError,
  d,
  labelConfigToEditId,
  existingAvailableTo
}: ConfigurationLabelModalFieldsProps) => {
  const { docErrorHelper } = useOutletContext<{
    docErrorHelper: DocumentErrorHelper;
  }>();

  const { getValues, clearErrors, setError, watch } = useFormContext();
  const name = watch("name");
  const color = watch("color");

  const errors: ErrorFieldKeyMap[] = useMemo(() => {
    if (!labelConfigToEditId) {
      return [];
    }

    return (
      docErrorHelper.getDictionaryMessagesForErrors({
        prefix: `$.labels.${labelConfigToEditId}`,
        resourceName: "labels"
      }) ?? []
    );
  }, [docErrorHelper, labelConfigToEditId]);

  const clearErrorsExcept = useCallback(
    (fieldToKeep: string) => {
      const allFields = Object.keys(getValues());
      const fieldsToClear = allFields.filter(field => field !== fieldToKeep);
      clearErrors(fieldsToClear);
    },
    [clearErrors, getValues]
  );

  useEffect(() => {
    if (!labelConfigToEditId) {
      return;
    }
    clearErrorsExcept("intervals");

    errors?.forEach(error => {
      setError(`${error.field}`, { message: d(error.message) });
    });
  }, [
    labelConfigToEditId,
    clearErrors,
    d,
    errors,
    setError,
    clearErrorsExcept
  ]);

  const handleChange = useCallback(
    (accessor: string) => {
      clearErrors(accessor);
    },
    [clearErrors]
  );

  return (
    <Stack gap="150" width="100" contentsWidth="100">
      <Form.TextField
        label={d("ui.common.name")}
        name="name"
        required
        autoFocus
        onChange={() => handleChange("name")}
      />
      <Form.TextAreaField
        label={d("ui.common.description")}
        name="description"
        onChange={() => handleChange("description")}
      />
      <Form.CheckboxGroup
        required
        label={d("ui.configuration.labels.availableTo.label")}
        description={d("ui.configuration.labels.availableTo.description")}
        name="availableTo"
        options={Object.values(LabelAvailableTo).map(value => {
          return {
            label: d(`ui.configuration.labels.availableTo.options.${value}`),
            value,
            disabled: existingAvailableTo?.includes(value) ?? false
          };
        })}
        onChange={() => handleChange("availableTo")}
      />
      <Form.ColorGroup
        label={d("ui.configuration.labels.color.label")}
        name="color"
        value={color}
        onChange={() => handleChange("color")}
        options={Object.values(LabelColor).map(value => {
          const color = labelColorToOptionColor[value as LabelColor];
          return {
            label: d(`ui.configuration.labels.color.options.${value}`),
            color,
            value
          };
        })}
        allowClear={false}
      />
      <Card size="small" surface="primary">
        <Stack gap="050">
          <Heading size="xs">{d("ui.common.preview")}</Heading>
          {name && (
            <Pill
              {...(color ? labelColorToPill[color as LabelColor] : {})}
              label={name ?? "-"}
            />
          )}
        </Stack>
      </Card>
      {serverError && (
        <Alert variant={AlertVariant.DANGER}>{d(serverError)}</Alert>
      )}
    </Stack>
  );
};

interface ConfigurationLabelModalProps {
  onOpenChange: () => void;
  labelConfigToEdit: LabelConfiguration | null;
}

const emptyLabelConfig: Partial<LabelConfiguration> = {
  name: "",
  description: "",
  availableTo: Object.values(LabelAvailableTo),
  color: LabelColor.COLOR_1
};

export const ConfigurationLabelModal = ({
  onOpenChange,
  labelConfigToEdit
}: ConfigurationLabelModalProps) => {
  const { docChange, document } = useOutletContext<{
    docChange: DocChange;
    document: Doc<WorkspaceDocument>;
  }>();
  const [serverError, setServerError] = useState("");
  const d = useDictionary();
  const tenantConfig = useTenantConfig();
  const localeString = tenantConfig?.locale;

  const isEdit = useMemo(() => !!labelConfigToEdit, [labelConfigToEdit]);

  const saveLabelConfigurationHelper = useCallback(
    (labelConfig: LabelConfiguration) => {
      if (isEdit) {
        if (!labelConfigToEdit) {
          console.error(
            `[saveLabelConfigurationHelper]: labelConfigToEdit is null in edit mode`
          );
          return;
        }
        labelConfig.id = labelConfigToEdit?.id;
        docChange(d => {
          if (!d.labels) {
            console.error(
              `[saveLabelConfigurationHelper]: no labels in document`
            );
            return;
          }
          if (!d.labels?.[labelConfig.id]) {
            console.error(
              `[saveLabelConfigurationHelper]: no label found with id ${labelConfig.id}`
            );
            return;
          }
          const currentLabel = d.labels[labelConfig.id];

          d.labels[labelConfig.id] = {
            ...currentLabel,
            ...labelConfig,
            metadata: {
              ...currentLabel.metadata,
              updatedAt: new Date().toISOString()
            }
          };
        });
      } else {
        docChange(d => {
          d.labels ??= {};
          const newId = customNanoId();
          d.labels[newId] = {
            ...labelConfig,
            id: newId,
            metadata: {
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          };
        });
      }
    },
    [docChange, isEdit, labelConfigToEdit]
  );

  const saveLabelConfiguration = useCallback(
    (labelConfig: LabelConfiguration) => {
      try {
        saveLabelConfigurationHelper(labelConfig);
        onOpenChange();
      } catch (error) {
        console.error(`[saveLabelConfiguration]: ${error}`);
        setServerError(
          `An error occurred while saving the labels configuration.`
        );
      }
    },
    [saveLabelConfigurationHelper, onOpenChange]
  );

  const isDuplicate = useCallback(
    (name: string) => {
      const existingLabelNames = new Set<string>(
        document.labels
          ? Object.values(document.labels ?? {})
              .filter(label => labelConfigToEdit?.name !== label.name)
              .map(label => valueUniqueness(label.name, localeString))
          : []
      );
      return !existingLabelNames.has(valueUniqueness(name, localeString));
    },
    [document.labels, labelConfigToEdit?.name, localeString]
  );

  const existingAvailableTo = useMemo(() => {
    return labelConfigToEdit?.availableTo;
  }, [labelConfigToEdit]);

  return (
    <Box style={{ zIndex: modalZIndex }}>
      <Overlay isOpen />
      <Floating position={FloatingPosition.CENTER}>
        <Modal
          className="configuration-label-modal"
          onOpenChange={onOpenChange}
          heading={d(
            isEdit
              ? "ui.configuration.labels.edit.title"
              : "ui.configuration.labels.creation.title"
          )}
        >
          <Form
            schema={labelConfigSchema(d, isDuplicate)}
            submitLabel={d("ui.common.save")}
            d={d}
            handleSubmit={saveLabelConfiguration}
            handleCancel={onOpenChange}
            cancelLabel={d("ui.common.cancel")}
            defaultValues={labelConfigToEdit ?? emptyLabelConfig}
          >
            <ConfigurationLabelModalFields
              serverError={serverError}
              d={d}
              labelConfigToEditId={labelConfigToEdit?.id}
              existingAvailableTo={existingAvailableTo}
            />
          </Form>
        </Modal>
      </Floating>
    </Box>
  );
};
