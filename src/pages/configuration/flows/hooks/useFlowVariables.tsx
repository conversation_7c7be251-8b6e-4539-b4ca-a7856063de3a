import { useEffect, useMemo } from "react";

import { Doc } from "@automerge/automerge-repo";
import { useOutletContext } from "react-router-dom";

import { getVariableDefinitions } from "@helpers/flows/flowVariableHelpers";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";

import { useDictionary } from "@src/hooks/useDictionary";
import { MockFlowContext } from "@src/types/FlowConfiguration/FlowConfiguration";
import { VariableTypeDefinition } from "@src/types/FlowConfiguration/Variables";
import { WorkspaceDocument } from "@src/types/documentTypes";

export const useFlowVariables = (mockFlowContext: MockFlowContext) => {
  const { setVariableDefinitions, setVariablesByName, setVariablesByPath } =
    useConfigurationFlowContext();
  const { document } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
  }>();

  const d = useDictionary();

  const variableDefinitions: {
    [key: string]: VariableTypeDefinition;
  } = useMemo(
    () => getVariableDefinitions({ flowContext: mockFlowContext, document, d }),
    [mockFlowContext, document, d]
  );

  useEffect(() => {
    setVariableDefinitions(variableDefinitions);
    const { flatByKey, flatByPath } = getFlattenedVariableDefinition({
      variableDefinitions
    });
    setVariablesByName(flatByKey);
    setVariablesByPath(flatByPath);
  }, [
    setVariablesByName,
    setVariableDefinitions,
    variableDefinitions,
    setVariablesByPath
  ]);

  return { variableDefinitions };
};

export function getFlattenedVariableDefinition({
  variableDefinitions,
  itemName,
  itemKey
}: {
  variableDefinitions: {
    [key: string]: VariableTypeDefinition;
  };
  itemKey?: string;
  itemName?: string;
}): {
  flatByKey: {
    [key: string]: VariableTypeDefinition;
  };
  flatByPath: {
    [key: string]: VariableTypeDefinition;
  };
} {
  let flatByPath: {
    [key: string]: VariableTypeDefinition;
  } = {};
  let flatByKey: {
    [key: string]: VariableTypeDefinition;
  } = {};

  Object.entries(variableDefinitions).forEach(([key, variableDefinition]) => {
    const currItemKey = itemKey ? `${itemKey}.${key}` : key;
    const currItemName = itemName ? `${itemName}.${key}` : key;

    const item = {
      ...variableDefinition,
      __name: currItemName
    };

    const nextVariableDefinition = Object.fromEntries(
      Object.entries(variableDefinition).filter(([key]) => !key.startsWith("_"))
    ) as {
      [key: string]: VariableTypeDefinition;
    };

    let nextVariables = {
      flatByKey: {},
      flatByPath: {}
    };
    if (Object.keys(nextVariableDefinition).length > 0) {
      nextVariables = getFlattenedVariableDefinition({
        variableDefinitions: nextVariableDefinition,
        itemKey: currItemKey,
        itemName: currItemName
      });
    }

    flatByKey = {
      ...flatByKey,
      [currItemName]: item,
      ...nextVariables.flatByKey
    };
    flatByPath = {
      ...flatByPath,
      [String(variableDefinition.__path ?? currItemKey)]: item,
      ...nextVariables.flatByPath
    };
  });

  return { flatByKey, flatByPath };
}
