import { useMemo } from "react";

import { Doc } from "@automerge/automerge-repo";
import { useOutletContext } from "react-router-dom";

import {
  getInitialMockFlowContext,
  getOrderedStepIds,
  getStepIdPathToStepId
} from "@helpers/flows/flowHelpers";
import {
  getIteratorStartingVariables,
  getVariableDefinitions,
  getVariablesForStep
} from "@helpers/flows/flowVariableHelpers";

import { useDictionary } from "@src/hooks/useDictionary";
import {
  FlowConfiguration,
  MockFlowContext,
  MockVariable
} from "@src/types/FlowConfiguration/FlowConfiguration";
import {
  FlowStep,
  FlowStepId,
  FlowStepVariant,
  flowStepVariantsWithTypeConfiguration
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import {
  CommonTypedStepProperties,
  IteratorStepProperties,
  IteratorStepType
} from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";
import {
  FlowStepTypeConfiguration,
  FlowStepTypeConfigurationPrimaryIdentifier
} from "@src/types/FlowConfiguration/FlowStepTypeConfiguration/FlowStepTypeConfiguration";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";
import { Workspace } from "@src/types/workspace";

import { getFlattenedVariableDefinition } from "./useFlowVariables";

export const useGenerateMockFlowContext = ({
  configurationFlow,
  flowStepTypeConfigByPrimaryIdentifier,
  toStepId,
  workspace
}: {
  configurationFlow: FlowConfiguration;
  flowStepTypeConfigByPrimaryIdentifier?: {
    [
      key: FlowStepTypeConfigurationPrimaryIdentifier
    ]: FlowStepTypeConfiguration;
  };
  toStepId?: FlowStepId;
  workspace: Workspace;
}): MockFlowContext => {
  const d = useDictionary();

  const allSteps = useMemo(() => {
    const iteratorSteps: {
      [stepId: string]: FlowStep<unknown>;
    } = {};
    Object.values(configurationFlow.steps).forEach(step => {
      if (step.variant === FlowStepVariant.ITERATOR) {
        const childSteps =
          (step?.properties as IteratorStepProperties)?.configuration?.steps ??
          {};

        for (const [stepId, step] of Object.entries(childSteps)) {
          iteratorSteps[stepId] = step;
        }
      }
    });
    return {
      ...configurationFlow.steps,
      ...iteratorSteps
    };
  }, [configurationFlow.steps]);

  const flowSteps = useMemo(() => {
    if (!configurationFlow) {
      return [];
    }

    const toStep = toStepId
      ? (configurationFlow.triggers?.[toStepId] ??
        configurationFlow.steps[toStepId])
      : undefined;

    const triggerStepIds = Object.keys(configurationFlow.triggers ?? {});
    if (!toStep) {
      const orderedStepIds = getOrderedStepIds({
        configurationFlow
      });
      return [
        ...triggerStepIds,
        ...Object.keys(allSteps).sort(
          (a, b) => orderedStepIds.indexOf(a) - orderedStepIds.indexOf(b)
        )
      ];
    } else if (toStep && toStep.variant === FlowStepVariant.TRIGGER) {
      return triggerStepIds;
    }

    const stepIdPath = getStepIdPathToStepId({
      stepId: toStep.id,
      configurationFlow
    });

    return toStep.variant === FlowStepVariant.SET_VARIABLES
      ? [...stepIdPath, toStep.id]
      : stepIdPath;
  }, [configurationFlow, toStepId, allSteps]);

  const { document } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
  }>();

  const flowContext = useMemo(() => {
    const orderedStepIds = getOrderedStepIds({ configurationFlow });
    const flowContext: MockFlowContext = getInitialMockFlowContext({
      workspace,
      configurationFlow
    });

    flowContext.variables = (configurationFlow.startingVariables ?? [])?.reduce(
      (acc, variable) => {
        acc[variable.identifier] = {
          ...variable,
          stepIds: [],
          sourceStepId: undefined,
          availableFromStepId: configurationFlow.start,
          sourceStepVariant: FlowStepVariant.TRIGGER,
          isStartingVariable: true
        };
        return acc;
      },
      flowContext.variables
    );
    if (!flowSteps) {
      return flowContext;
    }

    for (const stepId of flowSteps) {
      const step = configurationFlow.triggers?.[stepId] ?? allSteps[stepId];

      const flowStepTypeConfig = flowStepVariantsWithTypeConfiguration.includes(
        step?.variant as FlowStepVariant
      )
        ? flowStepTypeConfigByPrimaryIdentifier?.[
            (step?.properties as CommonTypedStepProperties)
              ?.typePrimaryIdentifier
          ]
        : undefined;

      const { variables: stepVariables, stepOutputVariables } =
        getVariablesForStep({
          workspaceDocument: document,
          step,
          flowStepTypeConfig,
          flowContext,
          includeNestedStepVariables: toStepId === stepId // || toStepId is inside the step,
        }) ?? [];

      flowContext.stepOutputVariables.push(...(stepOutputVariables ?? []));

      // TODO: once starting variables are resolved properly add this back
      // if (step.variant === FlowStepVariant.TRIGGER) {
      //   // Trigger step variables are put into starting variables - skip adding them twice
      //   continue;
      // }

      for (const variable of stepVariables) {
        if (flowContext.variables[variable.identifier]) {
          const originalSourceStepId =
            flowContext.variables[variable.identifier].sourceStepId;

          const updatedSourceStepId =
            originalSourceStepId &&
            orderedStepIds.indexOf(originalSourceStepId) <
              orderedStepIds.indexOf(stepId)
              ? originalSourceStepId
              : stepId;

          const updatedAvailableFromStepId =
            step?.variant === FlowStepVariant.SET_VARIABLES
              ? flowContext.variables[variable.identifier].availableFromStepId
              : variable.availableFromStepId;

          flowContext.variables[variable.identifier] = {
            ...flowContext.variables[variable.identifier],
            stepIds: [
              ...(flowContext.variables[variable.identifier].stepIds ?? []),
              stepId
            ],
            sourceStepId: updatedSourceStepId,
            availableFromStepId: updatedAvailableFromStepId,
            properties: {
              ...flowContext.variables[variable.identifier].properties,
              ...variable.properties
            }
          };
        } else {
          flowContext.variables[variable.identifier] = variable;
        }
      }
    }

    configurationFlow.endingVariables?.forEach(variable => {
      if (flowContext.variables[variable.identifier]) {
        flowContext.variables[variable.identifier] = {
          ...flowContext.variables[variable.identifier],
          isEndingVariable: true
        };
      }
    });

    const variableDefinitions = getVariableDefinitions({
      flowContext,
      document,
      d
    });

    const { flatByPath: mappedDefinitions } = getFlattenedVariableDefinition({
      variableDefinitions
    });

    for (const stepId of flowSteps) {
      const step = configurationFlow.triggers?.[stepId] ?? allSteps[stepId];
      if (step.variant !== FlowStepVariant.ITERATOR) {
        continue;
      }

      const iteratorStep = step as FlowStep<IteratorStepProperties>;

      const iteratorStartingVariables = getIteratorStartingVariables(
        iteratorStep,
        mappedDefinitions
      );

      const iteratorStepStart = iteratorStep.properties.configuration.start;

      const aggregateOutputIdentifier =
        iteratorStep.properties.typePrimaryIdentifier ===
        IteratorStepType.AGGREGATE
          ? (iteratorStep.properties.inputs?.["resultVariableName"] as string)
          : undefined;

      const iteratorMockVariables: { [identifier: string]: MockVariable } =
        Object.fromEntries(
          iteratorStartingVariables.map(variable => [
            variable.identifier,
            {
              ...variable,
              sourceStepId: iteratorStepStart,
              availableFromStepId: iteratorStepStart,
              sourceStepVariant: step.variant,
              isStartingVariable: false,
              isEndingVariable: false,
              iteratorParentId: stepId,
              isAggregateOutput:
                aggregateOutputIdentifier === variable.identifier
            }
          ])
        );

      flowContext.variables = {
        ...flowContext.variables,
        ...iteratorMockVariables
      };
    }

    return flowContext;
  }, [
    configurationFlow,
    workspace,
    flowSteps,
    allSteps,
    flowStepTypeConfigByPrimaryIdentifier,
    document,
    toStepId,
    d
  ]);

  return flowContext;
};
