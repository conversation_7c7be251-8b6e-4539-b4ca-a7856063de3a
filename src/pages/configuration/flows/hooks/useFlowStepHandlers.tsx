import { useCallback } from "react";

import { ToastNotificationVariant } from "@oneteam/onetheme";
import { get } from "lodash";
import { useOutletContext } from "react-router-dom";

import { getByPath } from "@helpers/configurationFormHelper";
import { customNanoId } from "@helpers/customNanoIdHelper.ts";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";
import { iteratorStartId } from "@pages/configuration/flows/FlowCanvas/FlowCanvasHelper";
import {
  getUpdatedIteratorProperties,
  validateClipboard
} from "@pages/configuration/flows/FlowCanvas/FlowClipboardHelper";
import {
  DocChange,
  PushToastNotification
} from "@pages/workspace/WorkspaceLayout";

import { FlowConfiguration } from "@src/types/FlowConfiguration/FlowConfiguration";
import {
  FlowStep,
  FlowStepId,
  FlowStepVariant,
  flowStepVariantsWithTypeConfiguration
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import {
  ActionStepProperties,
  ConditionStepBranch,
  ConditionStepProperties,
  IteratorStepProperties,
  SetVariablesStepProperties,
  TriggerStepProperties
} from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";
import { FlowStepTypeConfiguration } from "@src/types/FlowConfiguration/FlowStepTypeConfiguration/FlowStepTypeConfiguration";
import { QuestionTypes } from "@src/types/Question";
import { WorkspaceDocument } from "@src/types/documentTypes";

import { useDictionary } from "../../../../hooks/useDictionary";

export type HandleAddFlowStep = (args: {
  name?: string;
  variant: `${FlowStepVariant}`;
  parentStepId?: FlowStepId;
  branchIndex?: number;
  skipTypeSelect?: boolean;
  properties?: FlowStep["properties"];
  flowStepTypeConfiguration?: FlowStepTypeConfiguration;
  path: string[];
  clipboardStep?: FlowStep;
}) => void;

export type HandlePasteFlowStep = (args: {
  name?: string;
  parentStepId?: FlowStepId;
  branchIndex?: number;
  skipTypeSelect?: boolean;
  properties?: FlowStep["properties"];
  flowStepTypeConfiguration?: FlowStepTypeConfiguration;
  path: string[];
  clipboardStep?: FlowStep;
}) => void;

export type HandleUpdateFlowStepName = (args: {
  stepId: FlowStepId;
  name?: string;
  path?: string[];
}) => void;

export type HandleUpdateFlowStep = (args: {
  stepId: FlowStepId;
  name?: string;
  variant: `${FlowStepVariant}`;
  // parentStepId?: FlowStepId;
  // branchIndex?: number;
  properties?: FlowStep["properties"];
  flowStepTypeConfiguration?: FlowStepTypeConfiguration;
  path?: string[];
}) => void;

export type HandleDeleteFlowStep = (args: {
  stepId: FlowStepId;
  variant: `${FlowStepVariant}`;
  path: string[];
}) => void;

export type HandleDuplicateFlowStep = (args: {
  stepId: FlowStepId;
  path: string[];
}) => void;

export const defaultBranch: ConditionStepBranch = {
  name: "If",
  condition: {
    lhs: "",
    operator: "=",
    rhs: ""
  },
  next: ""
};

export const useFlowStepHandlers = () => {
  const d = useDictionary();
  const { docChange, document, pushToastNotification } = useOutletContext<{
    docChange: DocChange;
    document: WorkspaceDocument;
    pushToastNotification: PushToastNotification;
  }>();
  const {
    configurationFlow,
    setSelectedStepId,
    setSelectingStepType,
    mockFlowContext,
    setPath,
    mainViewportRef
  } = useConfigurationFlowContext();

  const getCopiedStep = (
    clipboardStep: FlowStep | undefined
  ): Partial<FlowStep> => {
    if (!clipboardStep) {
      return {};
    }

    const updatedProperties =
      clipboardStep.variant === FlowStepVariant.ITERATOR
        ? getUpdatedIteratorProperties(
            clipboardStep as FlowStep<IteratorStepProperties>
          )
        : clipboardStep.properties;

    return {
      name: clipboardStep.name,
      properties: updatedProperties
    };
  };

  const handleAddFlowStep: HandleAddFlowStep = useCallback(
    ({
      name,
      variant,
      parentStepId,
      branchIndex,
      skipTypeSelect,
      properties = {},
      flowStepTypeConfiguration,
      path: pathArgument,
      clipboardStep
    }) => {
      if (!configurationFlow) {
        return;
      }
      setPath(pathArgument); //Setting context path for stepTypeModal

      // TODO: Remove when ready to support iterators in iterators
      if (variant === FlowStepVariant.ITERATOR && pathArgument.length > 0) {
        pushToastNotification(
          d("errors.configurationFlow.unsupportedStep.heading"),
          d("errors.configurationFlow.unsupportedStep.description", {
            variant: FlowStepVariant.ITERATOR
          }),
          ToastNotificationVariant.WARNING
        );
        return;
      }

      let parentStep: FlowStep | undefined;

      if (parentStepId && configurationFlow) {
        parentStep =
          configurationFlow.steps[parentStepId] ??
          configurationFlow.triggers?.[parentStepId];
      } else {
        parentStep = undefined;
      }

      if (
        pathArgument &&
        parentStepId &&
        parentStep?.variant !== FlowStepVariant.TRIGGER
      ) {
        const iteratorFlow = getByPath<FlowConfiguration>(document, [
          "flows",
          "entities",
          configurationFlow.id,
          ...pathArgument
        ]);
        parentStep = iteratorFlow.steps[parentStepId];
      }
      const isGoingOffASpecificConditionBranch =
        branchIndex !== undefined &&
        parentStep?.variant === FlowStepVariant.CONDITION &&
        branchIndex <
          (parentStep?.properties as ConditionStepProperties)?.branches?.length;

      if (
        !skipTypeSelect &&
        flowStepVariantsWithTypeConfiguration.includes(
          variant as FlowStepVariant
        )
      ) {
        setSelectingStepType({
          type: variant,
          parentStepId,
          branchIndex: isGoingOffASpecificConditionBranch
            ? branchIndex
            : undefined
        });
        setSelectedStepId(undefined);
        return;
      }

      const newStepId = customNanoId();
      const fallbackName = d(`ui.terminology.${variant}`);

      mainViewportRef?.zoomTo(1);
      docChange(doc => {
        const flow = getByPath<FlowConfiguration>(doc, [
          "flows",
          "entities",
          configurationFlow.id,
          ...pathArgument
        ]);

        const updateBeforeReturn = () => {
          setSelectingStepType(undefined);
          setSelectedStepId(newStepId);
        };

        const accessor =
          variant === FlowStepVariant.TRIGGER ? "triggers" : "steps";

        if (!flow?.[accessor]) {
          flow[accessor] = {};
        }
        let initialProperties = properties as
          | TriggerStepProperties
          | ActionStepProperties
          | ConditionStepProperties
          | SetVariablesStepProperties
          | IteratorStepProperties;

        if (
          flowStepVariantsWithTypeConfiguration.includes(
            variant as FlowStepVariant
          ) &&
          !clipboardStep
        ) {
          if (!flowStepTypeConfiguration) {
            console.error("No flow step type configuration provided");
            return;
          }
          (
            initialProperties as
              | TriggerStepProperties
              | ActionStepProperties
              | IteratorStepProperties
          ).typePrimaryIdentifier = flowStepTypeConfiguration.primaryIdentifier;
        }

        if (variant === FlowStepVariant.TRIGGER) {
          if (!flowStepTypeConfiguration) {
            console.error("No flow step type configuration provided");
            return;
          }
        }

        if (variant === FlowStepVariant.ITERATOR) {
          initialProperties = {
            ...initialProperties,
            configuration: {
              start: "",
              steps: {}
            } as FlowConfiguration
          };
        } else if (variant === FlowStepVariant.SET_VARIABLES) {
          const variableNames = Object.values(
            mockFlowContext?.variables ?? {}
          ).map(v => v.identifier);

          let uniqueName = `variable_1`;
          let variableCount = 1;
          while (variableNames.includes(uniqueName)) {
            variableCount++;
            uniqueName = `variable_${variableCount}`;
          }

          initialProperties = {
            variables: [
              {
                identifier: uniqueName,
                value: "",
                type: QuestionTypes.TEXT
              }
            ],
            ...initialProperties
          };
        } else if (variant === FlowStepVariant.CONDITION) {
          let next =
            parentStep?.variant === FlowStepVariant.TRIGGER
              ? (flow.start ?? "")
              : (parentStep?.next ?? "");
          if (isGoingOffASpecificConditionBranch) {
            next = "";
          }
          initialProperties = {
            branches: [{ ...defaultBranch, next }],
            ...initialProperties
          };
        }

        const copiedStep = getCopiedStep(clipboardStep);

        const stepOption: FlowStep = {
          id: newStepId,
          variant,
          name: name ?? fallbackName,
          properties: initialProperties,
          next: "",
          metadata: {
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          ...copiedStep
        };

        // If adding to the starting step, change the next of the first branch to point to the starting step id
        flow[accessor][newStepId] = stepOption;

        if (variant === FlowStepVariant.TRIGGER) {
          updateBeforeReturn();
          return;
        }

        // Calculate next steps and flow start
        if (
          (!parentStepId || !parentStep) &&
          !Object.keys(flow.steps).length &&
          Object.keys(flow.triggers ?? {}).length > 0
        ) {
          parentStepId = flow.triggers ? Object.keys(flow.triggers)[0] : "";
          parentStep = flow.triggers?.[parentStepId];
        }

        if (
          !parentStepId ||
          (!parentStep && parentStepId !== iteratorStartId)
        ) {
          flow.start ??= newStepId;

          if (flow.start === "") {
            flow.start = newStepId;
          }
          updateBeforeReturn();
          return;
        }

        // If the parent is a trigger or iterator, set start to the created step
        if (
          parentStep?.variant === FlowStepVariant.TRIGGER ||
          parentStepId === iteratorStartId
        ) {
          if (flow.start && variant !== FlowStepVariant.CONDITION) {
            flow.steps[newStepId].next = flow.start ?? null;
          }

          if (
            flow.start &&
            copiedStep &&
            variant === FlowStepVariant.CONDITION
          ) {
            (
              flow[accessor][newStepId] as FlowStep<ConditionStepProperties>
            ).properties.branches[0].next = flow.start ?? "";
          }

          flow.start = newStepId;
          updateBeforeReturn();
          return;
        }

        flow.start = flow.start ?? newStepId;
        if (isGoingOffASpecificConditionBranch) {
          const branch = (
            flow.steps[parentStepId].properties as ConditionStepProperties
          ).branches[branchIndex];
          const previousNextId = branch.next;
          branch.next = newStepId;
          if (previousNextId && variant == FlowStepVariant.CONDITION) {
            (
              flow.steps[newStepId] as FlowStep<ConditionStepProperties>
            ).properties.branches[0].next = previousNextId;
          } else if (previousNextId) {
            flow.steps[newStepId].next = previousNextId;
          }
          updateBeforeReturn();
          return;
        }

        // Make the parent's previous child the child of the new step
        const parentNextStepId = flow.steps[parentStepId].next;
        if (
          parentNextStepId &&
          parentNextStepId !== newStepId &&
          variant !== FlowStepVariant.CONDITION
        ) {
          flow.steps[newStepId].next = parentNextStepId;
        } else if (
          parentNextStepId &&
          parentNextStepId !== newStepId &&
          variant === FlowStepVariant.CONDITION
        ) {
          (
            flow[accessor][newStepId] as FlowStep<ConditionStepProperties>
          ).properties.branches[0].next = parentNextStepId;
        }

        // Make the child the next step of the parent
        flow.steps[parentStepId].next = newStepId;
        updateBeforeReturn();
      });
    },
    [
      configurationFlow,
      mainViewportRef,
      setPath,
      d,
      docChange,
      mockFlowContext?.variables,
      setSelectedStepId,
      setSelectingStepType,
      document,
      pushToastNotification
    ]
  );

  const handlePasteFlowStep: HandlePasteFlowStep = useCallback(
    ({
      name,
      parentStepId,
      branchIndex,
      skipTypeSelect,
      properties = {},
      flowStepTypeConfiguration,
      path: pathArgument,
      clipboardStep
    }) => {
      if (!validateClipboard(clipboardStep) || !clipboardStep?.variant) {
        pushToastNotification(
          d("errors.configurationFlow.clipboard.invalidStep.heading"),
          d("errors.configurationFlow.clipboard.invalidStep.description"),
          ToastNotificationVariant.WARNING
        );
        return;
      }
      handleAddFlowStep({
        clipboardStep,
        name,
        variant: clipboardStep?.variant,
        parentStepId,
        branchIndex,
        skipTypeSelect,
        properties,
        flowStepTypeConfiguration,
        path: pathArgument
      });
    },
    [d, handleAddFlowStep, pushToastNotification]
  );

  const handleUpdateFlowStepName: HandleUpdateFlowStepName = useCallback(
    ({ stepId, name, path }) => {
      if (!configurationFlow) {
        return;
      }
      const configurationFlowToUse = path
        ? getByPath<FlowConfiguration>(document, [
            "flows",
            "entities",
            configurationFlow?.id,
            ...path
          ])
        : configurationFlow;
      const existingStep =
        configurationFlowToUse.triggers?.[stepId] ??
        configurationFlowToUse.steps?.[stepId];

      if (!existingStep) {
        return;
      }

      docChange(doc => {
        const flow = getByPath<FlowConfiguration>(doc, [
          "flows",
          "entities",
          configurationFlow.id,
          ...(path ?? [])
        ]);

        const accessor =
          existingStep.variant === "trigger" ? "triggers" : "steps";

        if (!flow[accessor]) {
          return;
        }

        flow[accessor][stepId].name = name;
      });
    },
    [configurationFlow, docChange, document]
  );

  const handleUpdateFlowStep: HandleUpdateFlowStep = useCallback(
    ({
      stepId,
      name,
      variant,
      properties = {},
      flowStepTypeConfiguration,
      path: pathToConfiguration = []
    }) => {
      if (!configurationFlow) {
        return;
      }

      const accessor = variant === "trigger" ? "triggers" : "steps";
      const pathToSteps =
        variant === "trigger"
          ? ["triggers"]
          : [...pathToConfiguration, "steps"];

      const existingStep = get(configurationFlow, pathToSteps.join("."))?.[
        stepId
      ];

      if (!existingStep) {
        return;
      }

      docChange(doc => {
        const flow = getByPath<FlowConfiguration>(doc, [
          "flows",
          "entities",
          configurationFlow.id,
          ...pathToConfiguration
        ]);

        // Not yet support variant swap
        if (!flow[accessor] || existingStep?.variant !== variant) {
          setSelectingStepType(undefined);
          return;
        }

        if (name) {
          flow[accessor][stepId].name = name;
        }
        if (properties || flowStepTypeConfiguration) {
          if (variant === FlowStepVariant.ITERATOR) {
            const existingStepPropertiesClone = JSON.parse(
              JSON.stringify(existingStep?.properties as IteratorStepProperties)
            );
            properties = {
              configuration: existingStepPropertiesClone?.configuration ?? {
                start: "",
                steps: {}
              },
              ...existingStepPropertiesClone
            } as IteratorStepProperties;
          }

          if (
            flowStepVariantsWithTypeConfiguration.includes(
              variant as FlowStepVariant
            )
          ) {
            if (!flowStepTypeConfiguration) {
              console.error("No flow step type configuration provided");
              return;
            }
            (
              properties as
                | TriggerStepProperties
                | ActionStepProperties
                | IteratorStepProperties
            ).typePrimaryIdentifier =
              flowStepTypeConfiguration.primaryIdentifier;
          }

          if (variant === FlowStepVariant.TRIGGER) {
            if (!flowStepTypeConfiguration) {
              console.error("No flow step type configuration provided");
              return;
            }
          }

          flow[accessor][stepId].properties = properties;
        }
        setSelectedStepId(stepId);
        setSelectingStepType(undefined);
      });
    },
    [configurationFlow, docChange, setSelectedStepId, setSelectingStepType]
  );

  const isDeletableConditionStep = (
    flowConfig: FlowConfiguration,
    stepId: string
  ): boolean => {
    const conditionStep = flowConfig.steps[
      stepId
    ] as FlowStep<ConditionStepProperties>;

    if (!conditionStep || conditionStep.variant !== FlowStepVariant.CONDITION) {
      return false;
    }

    const populatedBranches = conditionStep.properties.branches?.filter(
      (branch: ConditionStepBranch) => !!branch.next
    ).length;

    const totalPopulatedBranches =
      populatedBranches + (conditionStep.next ? 1 : 0);

    if (totalPopulatedBranches > 1) {
      return false;
    }

    return true;
  };

  const handleDeleteFlowStep: HandleDeleteFlowStep = useCallback(
    ({ stepId, variant, path }) => {
      if (!configurationFlow) {
        return;
      }
      if (variant === FlowStepVariant.TRIGGER) {
        alert(d("ui.configuration.flows.step.delete.cannotDeleteTrigger"));
        return;
      }
      const existingStep = getByPath<FlowConfiguration>(document, [
        "flows",
        "entities",
        configurationFlow.id,
        ...path
      ]);

      if (!existingStep) {
        console.error("No existing item found");
        return;
      }

      mainViewportRef?.zoomTo(1);
      docChange(doc => {
        const flowConfig = getByPath<FlowConfiguration>(doc, [
          "flows",
          "entities",
          configurationFlow.id,
          ...path
        ]);

        if (!flowConfig?.steps) {
          return;
        }

        const deletableConditionStep = isDeletableConditionStep(
          flowConfig,
          stepId
        );

        if (!deletableConditionStep && variant === FlowStepVariant.CONDITION) {
          pushToastNotification(
            "Invalid deletion operation",
            d("ui.configuration.flows.step.delete.cannotDeleteVariant", {
              variant
            }),
            ToastNotificationVariant.WARNING
          );
          return;
        }

        let nextStep = flowConfig.steps[stepId].next ?? null;
        if (deletableConditionStep) {
          (
            flowConfig.steps[stepId].properties as ConditionStepProperties
          ).branches?.forEach(branch => {
            if (branch.next) {
              nextStep = branch.next;
            }
          });
        }

        if (flowConfig.start === stepId) {
          if (!nextStep) {
            flowConfig.start = "";
          } else {
            flowConfig.start = nextStep;
          }
        }

        Object.values(flowConfig.steps).forEach((step: FlowStep) => {
          if (step.next === stepId) {
            step.next = nextStep;
          }

          if (step.variant !== FlowStepVariant.CONDITION) {
            return;
          }

          const branches = (step.properties as ConditionStepProperties)
            .branches;

          branches?.forEach(branch => {
            branch.next =
              branch.next === stepId ? nextStep : (branch.next ?? null);
          });
        });

        delete flowConfig.steps[stepId];
        setSelectedStepId(undefined);

        if (Object.keys(flowConfig.steps).length === 0) {
          flowConfig.start = "";
        }
      });
    },
    [
      configurationFlow,
      d,
      docChange,
      pushToastNotification,
      document,
      setSelectedStepId,
      mainViewportRef
    ]
  );

  const handleDuplicateFlowStep: HandleDuplicateFlowStep = useCallback(
    ({ stepId, path }) => {
      if (!configurationFlow) {
        return;
      }

      mainViewportRef?.zoomTo(1);
      docChange(doc => {
        const flow = getByPath<FlowConfiguration>(doc, [
          "flows",
          "entities",
          configurationFlow.id,
          ...path
        ]);

        const currentStep = flow.steps?.[stepId];
        if (
          !currentStep ||
          currentStep.variant === FlowStepVariant.TRIGGER ||
          currentStep.variant === FlowStepVariant.CONDITION
        ) {
          return;
        }

        const newStepId = customNanoId();
        const newStep: FlowStep = {
          ...JSON.parse(JSON.stringify(currentStep)),
          id: newStepId,
          metadata: {
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          next: currentStep.next ?? ""
        };

        flow.steps[newStepId] = newStep;

        currentStep.next = newStepId;
        setSelectedStepId(newStepId);
      });
    },
    [configurationFlow, docChange, setSelectedStepId, mainViewportRef]
  );

  return {
    handleAddFlowStep,
    handlePasteFlowStep,
    handleUpdateFlowStep,
    handleUpdateFlowStepName,
    handleDeleteFlowStep,
    handleDuplicateFlowStep
  };
};
