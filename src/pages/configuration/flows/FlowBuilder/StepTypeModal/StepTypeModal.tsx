import React, { useCallback, useMemo, useState } from "react";

import {
  Box,
  Button,
  Divider,
  DropdownItem,
  DropdownItemGroup,
  Floating,
  FloatingPosition,
  Icon,
  IconType,
  Inline,
  Modal,
  Overlay,
  SearchBar,
  Select,
  Stack,
  Text
} from "@oneteam/onetheme";

import { modalZIndex } from "@helpers/modalHelper";

import { useFlowStepHandlers } from "@pages/configuration/flows/hooks/useFlowStepHandlers";

import { useDictionary } from "@src/hooks/useDictionary";
import {
  FlowStepVariant,
  flowStepVariantsWithTypeConfiguration
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import {
  FlowStepTypeCategory,
  FlowStepTypeConfiguration
} from "@src/types/FlowConfiguration/FlowStepTypeConfiguration/FlowStepTypeConfiguration";

import { useConfigurationFlowContext } from "../../ConfigurationFlowContext";
import "./StepTypeModal.scss";

export const StepTypeModal = () => {
  const d = useDictionary();
  const {
    selectingStepType,
    setSelectingStepType,
    path,
    flowStepTypeConfigByPrimaryIdentifier
  } = useConfigurationFlowContext();
  const { handleAddFlowStep, handleUpdateFlowStep } = useFlowStepHandlers();

  const [search, setSearch] = useState<string>("");
  const [category, setCategory] = useState<string | undefined>();

  const handleClose = useCallback(() => {
    setSelectingStepType(undefined);
    setSearch("");
    setCategory(undefined);
  }, [setSelectingStepType, setSearch]);

  const handleSave = (flowStepTypeConfig: FlowStepTypeConfiguration) => {
    if (selectingStepType?.type !== flowStepTypeConfig.type) {
      return;
    }
    if (selectingStepType.stepId) {
      // Update
      handleClose();
      handleUpdateFlowStep({
        name: flowStepTypeConfig.name,
        variant: flowStepTypeConfig.type,
        stepId: selectingStepType.stepId,
        properties: {
          typePrimaryIdentifier: flowStepTypeConfig.primaryIdentifier
        },
        flowStepTypeConfiguration: flowStepTypeConfig,
        path:
          selectingStepType?.type === FlowStepVariant.TRIGGER
            ? []
            : (path ?? [])
      });
      return;
    }
    if (selectingStepType?.type === FlowStepVariant.TRIGGER) {
      // Add trigger
      handleAddFlowStep({
        name: flowStepTypeConfig.name,
        variant: flowStepTypeConfig.type,
        skipTypeSelect: true,
        properties: {
          typePrimaryIdentifier: flowStepTypeConfig.primaryIdentifier,
          inputs: {}
        },
        flowStepTypeConfiguration: flowStepTypeConfig,
        path: []
      });
      handleClose();
    } else {
      // Add action, iterator
      handleAddFlowStep({
        name: flowStepTypeConfig.name,
        variant: flowStepTypeConfig.type,
        parentStepId: selectingStepType.parentStepId,
        branchIndex: selectingStepType.branchIndex,
        skipTypeSelect: true,
        properties: {
          typePrimaryIdentifier: flowStepTypeConfig.primaryIdentifier,
          inputs: {}
        },
        flowStepTypeConfiguration: flowStepTypeConfig,
        path: path ?? []
      });
      handleClose();
    }
  };

  const categoriesByName = useMemo(() => {
    const categories: Record<string, FlowStepTypeCategory> = {};
    Object.values(flowStepTypeConfigByPrimaryIdentifier ?? {}).forEach(
      flowStepTypeConfig => {
        if (
          flowStepTypeConfig.type === selectingStepType?.type &&
          flowStepTypeConfig.properties.category &&
          !flowStepTypeConfig.properties.isHidden &&
          !flowStepTypeConfig.properties.deprecated
        ) {
          const categoryName = flowStepTypeConfig.properties.category.name;
          if (!categories[categoryName]) {
            categories[categoryName] = flowStepTypeConfig.properties.category;
          }
        }
      }
    );
    // Add "Other" category if it doesn't exist
    if (!categories["Other"]) {
      categories["Other"] = {
        name: "Other",
        icon: { name: "folder" }
      };
    }
    return categories;
  }, [flowStepTypeConfigByPrimaryIdentifier, selectingStepType?.type]);

  const filteredOptionsByCategory = useMemo(() => {
    const options = Object.values(flowStepTypeConfigByPrimaryIdentifier ?? {});

    const matchesSearchTerm = (
      flowStepTypeConfig: FlowStepTypeConfiguration
    ) => {
      const searchTerms = search.toLowerCase().split(" ");
      const allWords =
        `${flowStepTypeConfig.name} ${flowStepTypeConfig.description ?? ""} ${flowStepTypeConfig.properties.category?.name}`.toLowerCase();
      for (const term of searchTerms) {
        if (!allWords.includes(term)) {
          return false;
        }
      }
      return true;
    };

    const allFilteredOptions = options.filter(
      flowStepTypeConfig =>
        flowStepTypeConfig.type === selectingStepType?.type &&
        !flowStepTypeConfig.properties.isHidden &&
        !flowStepTypeConfig.properties.deprecated &&
        (!search || matchesSearchTerm(flowStepTypeConfig)) &&
        (!category || flowStepTypeConfig.properties.category?.name === category)
    );

    const optionsByCategory: Record<string, FlowStepTypeConfiguration[]> = {};
    allFilteredOptions.forEach(flowStepTypeConfig => {
      const categoryName =
        flowStepTypeConfig.properties.category?.name || "Other";
      if (!optionsByCategory[categoryName]) {
        optionsByCategory[categoryName] = [];
      }
      optionsByCategory[categoryName].push(flowStepTypeConfig);
    });
    return optionsByCategory;
  }, [
    category,
    flowStepTypeConfigByPrimaryIdentifier,
    search,
    selectingStepType?.type
  ]);

  if (
    !selectingStepType?.type ||
    // Ensures type has is an option to have step type configuration
    !flowStepVariantsWithTypeConfiguration.includes(
      selectingStepType.type as FlowStepVariant
    )
  ) {
    return <></>;
  }

  return (
    <Box style={{ zIndex: modalZIndex }}>
      <Overlay isOpen></Overlay>
      <Floating position={FloatingPosition.CENTER}>
        <Modal
          className="step-type-modal"
          heading={d(
            `ui.configuration.flows.step.variants.${selectingStepType?.type}.${selectingStepType?.stepId ? "updateModal" : "addModal"}.heading`
          )}
          onOpenChange={() => setSelectingStepType(undefined)}
        >
          <Stack
            width="100"
            gap="100"
            style={{ height: "60vh", paddingTop: "var(--spacing-100)" }}
          >
            <Inline gap="100" alignment="left">
              <SearchBar value={search} handleChange={setSearch} autoFocus />
              <Box style={{ width: "200px" }}>
                <Select
                  value={category}
                  onChange={value => setCategory(value as string | undefined)}
                  options={Object.values(categoriesByName)
                    .sort(sortCategories)
                    .map(category => ({
                      label: category.name,
                      value: category.name,
                      properties: category
                    }))}
                  placeholder={d(
                    `ui.configuration.flows.stepType.category.placeholder`
                  )}
                  renderOptionLeftElement={({ option }) =>
                    option?.properties?.icon ? (
                      <Icon {...(option.properties.icon as IconType)} />
                    ) : undefined
                  }
                  renderLeftElement={({ selectedOption }) =>
                    selectedOption?.properties?.icon ? (
                      <Icon {...(selectedOption.properties.icon as IconType)} />
                    ) : undefined
                  }
                />
              </Box>
            </Inline>
            <Stack height="100" overflow="auto" gap="100">
              {Object.values(categoriesByName)
                .sort(sortCategories)
                .map(category => {
                  const categoryOptions =
                    filteredOptionsByCategory?.[category.name]?.sort((a, b) =>
                      a.name.localeCompare(b.name)
                    ) ?? [];

                  if (!categoryOptions.length) {
                    return <></>;
                  }
                  return (
                    <>
                      <Divider size="small" />
                      <DropdownItemGroup>
                        <Text
                          className="dropdown-item-group__title"
                          size="s"
                          weight="bold"
                          color="text-secondary"
                        >
                          <Inline alignment="left" gap="025">
                            <Icon
                              size="s"
                              color="text-secondary"
                              {...(category?.icon || { name: "folder" })}
                              style={{
                                fontSize: "var(--icon-size-s, 18px)"
                              }}
                            />
                            {category?.name ||
                              d(
                                "ui.configuration.flows.step.variants.otherCategory"
                              )}
                          </Inline>
                        </Text>
                        {categoryOptions.map(flowStepTypeConfig => (
                          <DropdownItem
                            id="view"
                            key={flowStepTypeConfig.id}
                            isSelected={
                              selectingStepType?.currentFlowStepTypePrimaryIdentifier ===
                              flowStepTypeConfig.primaryIdentifier
                            }
                            onClick={() => handleSave(flowStepTypeConfig)} // todo: switch the function by the type of the step
                            leftElement={
                              flowStepTypeConfig.properties.icon ? (
                                <Icon {...flowStepTypeConfig.properties.icon} />
                              ) : undefined
                            }
                            description={
                              flowStepTypeConfig.description?.length
                                ? flowStepTypeConfig.description
                                : undefined
                            }
                          >
                            {flowStepTypeConfig.name}
                          </DropdownItem>
                        ))}
                      </DropdownItemGroup>
                    </>
                  );
                })}
            </Stack>
            {selectingStepType.stepId && (
              <Box width="100" alignment="right">
                <Button
                  label={d("ui.common.cancel")}
                  variant="secondary"
                  onClick={() => setSelectingStepType(undefined)}
                />
              </Box>
            )}
          </Stack>
        </Modal>
      </Floating>
    </Box>
  );
};

const sortCategories = (a: FlowStepTypeCategory, b: FlowStepTypeCategory) => {
  // sort by order
  if (a.order && b.order) {
    return a.order - b.order;
  }
  // if one has order and the other doesn't, the one with order comes first
  if (a.order) {
    return -1;
  }
  if (b.order) {
    return 1;
  }
  // if neither has order, sort alphabetically
  return a.name.localeCompare(b.name);
};
