import React, { useC<PERSON>back, useEffect, useMemo, useState } from "react";

import { updateText } from "@automerge/automerge-repo";
import {
  Accordion,
  Box,
  DropdownItem,
  DropdownItemGroup,
  FloatingContentPanel,
  FloatingWithParentPosition,
  Icon,
  Inline,
  KebabMenu,
  OpenCloseIcon,
  PillSelect,
  SearchBar,
  Stack,
  TabGroup,
  Text,
  getClassNames
} from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import { getByPath } from "@helpers/automergeDocumentHelper.ts";
import { getQuestionTypeOptions } from "@helpers/configurationFormHelper";
import { getStepIdPathToStepId } from "@helpers/flows/flowHelpers";

import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { useDictionary } from "@src/hooks/useDictionary";
import {
  FlowConfiguration,
  MockVariable
} from "@src/types/FlowConfiguration/FlowConfiguration.ts";
import { FlowStepId } from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import {
  VariableConfiguration,
  VariableTypeDefinition,
  VariableTypes
} from "@src/types/FlowConfiguration/Variables";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { useConfigurationFlowContext } from "../../ConfigurationFlowContext";
import { ConfigurationFlowVariable } from "../ConfigurationFlowVariable/ConfigurationFlowVariable";
import {
  findVariableReferences,
  replaceVariableReference
} from "../ConfigurationFlowVariable/ConfigurationFlowVariableHelper";
import "./ConfigurationFlowVariablesPanel.scss";

const commonQuestionTypes = getQuestionTypeOptions();

const isVariableAvailable = ({
  selectedStepId,
  availableFromStepId,
  pathToSelectedStep,
  iteratorParentId,
  isAggregateOutput
}: {
  selectedStepId: FlowStepId | undefined;
  availableFromStepId: FlowStepId | undefined;
  pathToSelectedStep: string[];
  iteratorParentId: FlowStepId | undefined;
  isAggregateOutput: boolean | undefined;
}): boolean => {
  if ((!selectedStepId || !availableFromStepId) && !iteratorParentId) {
    return true;
  }
  if (selectedStepId === availableFromStepId) {
    return true;
  }
  if (availableFromStepId && pathToSelectedStep.includes(availableFromStepId)) {
    return true;
  }

  if (
    iteratorParentId &&
    isAggregateOutput &&
    pathToSelectedStep.includes(iteratorParentId)
  ) {
    return true;
  }

  if (selectedStepId === iteratorParentId) {
    return true;
  }
  return false;
};

export const ConfigurationFlowVariablesPanel = () => {
  const d = useDictionary();
  const {
    configurationFlow,
    selectedStepId,
    setSelectedStepId,
    variableDefinitions,
    variablesByName,
    settings
  } = useConfigurationFlowContext();

  // TODO: later add to useContext
  const [searchValue, setSearchValue] = React.useState<string>("");
  const [typeFilter, setTypeFilter] = React.useState<string | undefined>();

  const selectedTab = useMemo(() => {
    // TODO: later add to useContext
    return "variables";
  }, []);

  const pathToSelectedStep = useMemo(() => {
    if (!selectedStepId || !configurationFlow) {
      return [];
    }
    return getStepIdPathToStepId({ stepId: selectedStepId, configurationFlow });
  }, [configurationFlow, selectedStepId]);

  const filteredVariableNames = useMemo(() => {
    if (!searchValue?.length && !typeFilter) {
      return Object.keys(variablesByName ?? {});
    }
    return Object.entries(variablesByName ?? {})
      .filter(([variableName, value]) => {
        if (typeFilter && value.__type !== typeFilter) {
          return false;
        }
        return (
          !searchValue ||
          variableName.toLowerCase().includes(searchValue.toLowerCase()) ||
          value.__type?.toLowerCase().includes(searchValue.toLowerCase()) ||
          value.__path?.toLowerCase().includes(searchValue.toLowerCase()) ||
          value.__description?.toLowerCase().includes(searchValue.toLowerCase())
        );
      })
      .map(([variableName]) => variableName);
  }, [searchValue, typeFilter, variablesByName]);

  const { docChange } = useOutletContext<{
    docChange: DocChange;
  }>();

  const renameVariable = useCallback(
    (currentName: string, newName: string) => {
      if (!configurationFlow) {
        return;
      }
      const pathsToReferences = findVariableReferences(
        configurationFlow,
        currentName
      );

      if (!pathsToReferences.length) {
        return;
      }

      docChange(document => {
        for (const path of pathsToReferences) {
          const splitPath = path.split(".");
          const pathToVariable = [
            "flows",
            "entities",
            configurationFlow.id,
            "steps",
            ...splitPath
          ];
          // Update where the variable is defined
          if (pathToVariable[pathToVariable.length - 1] === "identifier") {
            updateText(document, pathToVariable, newName.trim());
            continue;
          }
          const field: string = getByPath(document, pathToVariable);
          const newValue = replaceVariableReference(
            field,
            currentName,
            newName.trim()
          );
          updateText(document, pathToVariable, newValue);
        }
      });
    },
    [configurationFlow, docChange]
  );

  const handleGoToSource = useCallback(
    (sourceStepId?: string, iteratorParentId?: string) => {
      if (iteratorParentId) {
        setSelectedStepId(iteratorParentId);
      } else if (sourceStepId) {
        setSelectedStepId(sourceStepId);
      }
    },
    [setSelectedStepId]
  );

  const variablesDisplay = useMemo(() => {
    return Object.entries(variableDefinitions ?? {}).map(
      ([key, itemDefinition]) => {
        const matchingVariables = filteredVariableNames.filter(name =>
          name.startsWith(key)
        );

        if (!matchingVariables.length) {
          return <></>;
        }

        const sourceStepId: FlowStepId | undefined =
          itemDefinition.__sourceStepId;
        const availableFromStepId: FlowStepId | undefined =
          itemDefinition.__availableFromStepId;
        const iteratorParentId: FlowStepId | undefined =
          itemDefinition.__iteratorParentId;
        const isAggregateOutput: boolean | undefined =
          itemDefinition.__isAggregateOutput;
        return (
          <VariableItem
            level={0}
            itemKey={key}
            key={key}
            definition={itemDefinition}
            path={key}
            debugMode={settings?.debugMode}
            handleGoToSource={
              sourceStepId
                ? () => handleGoToSource(sourceStepId, iteratorParentId)
                : undefined
            }
            handleRename={newName => renameVariable(key, newName)}
            isAvailable={isVariableAvailable({
              selectedStepId,
              availableFromStepId,
              pathToSelectedStep,
              iteratorParentId,
              isAggregateOutput
            })}
            filteredVariableNames={matchingVariables}
            filter={
              searchValue || typeFilter
                ? {
                    searchValue,
                    typeFilter
                  }
                : undefined
            }
          />
        );
      }
    );
  }, [
    filteredVariableNames,
    pathToSelectedStep,
    searchValue,
    selectedStepId,
    typeFilter,
    variableDefinitions,
    renameVariable,
    settings?.debugMode,
    handleGoToSource
  ]);

  return (
    <FloatingContentPanel
      className="configuration-flow-context-panel"
      heading={d("ui.configuration.flows.variables.panel.title")}
    >
      <Stack className="configuration-flow-context-panel__content" gap="100">
        <TabGroup
          options={[
            {
              value: "variables",
              label: d("ui.configuration.flows.variables.panel.variablesTab")
            }
          ]}
          value={selectedTab}
        />
        <Box
          gap="050"
          alignment="left"
          style={{
            flexDirection: typeFilter ? "column" : "row"
          }}
        >
          <Box width="fill">
            <SearchBar
              autoFocus
              placeholder={d("ui.common.search")}
              value={searchValue}
              handleChange={setSearchValue}
            />
          </Box>
          <PillSelect
            label={d("ui.configuration.flows.variables.type.label")}
            value={typeFilter}
            handleChange={value => setTypeFilter(value as string)}
            options={[
              ...commonQuestionTypes.map(type => ({
                value: type,
                label: d(`ui.configuration.forms.question.type.${type}.label`),
                description: d(
                  `ui.configuration.forms.question.type.${type}.description`
                )
              })),
              ...Object.values(VariableTypes).map((type: string) => {
                return {
                  value: type,
                  label: type
                };
              })
            ]}
          />
        </Box>

        <Box height="100" overflow="auto">
          <Stack>{variablesDisplay}</Stack>
        </Box>
        <Inline
          alignment="left"
          gap="050"
          padding="050"
          style={{
            borderRadius: "var(--border-radius-rounded-more, 12px)",
            backgroundColor:
              "var(--components-pill-prominent-neutral-color-background)"
          }}
        >
          <Icon
            name="lightbulb"
            fillStyle="filled"
            color="text-tertiary"
            size="s"
          />
          <Text size="s" color="text-secondary">
            {d("ui.configuration.flows.variables.panel.copyVariableTip")}
          </Text>
        </Inline>
      </Stack>
    </FloatingContentPanel>
  );
};

export type VariableForPanel = {
  level: number;
  handleGoToSource?: () => void;
  handleRename?: (newName: string) => void;
  itemKey: string;
  definition: VariableTypeDefinition;
  path: string;
  isAvailable?: boolean;
  filteredVariableNames: string[];
  filter?: {
    searchValue?: string;
    typeFilter?: string;
  };
  debugMode?: boolean;
};

const VariableItem = ({
  level,
  itemKey,
  definition,
  path,
  handleRename,
  handleGoToSource,
  isAvailable = true,
  filteredVariableNames,
  filter,
  debugMode
}: VariableForPanel) => {
  const nextDefinitions: VariableTypeDefinition =
    typeof definition === "object"
      ? Object.fromEntries(
          Object.keys(definition)
            .filter(k => !k.startsWith("__") && definition[k])
            .map(k => [k, definition[k] as VariableTypeDefinition]) ?? []
        )
      : {};

  if (Object.keys(nextDefinitions).length > 0) {
    return (
      <ExpandableVariablePanel
        level={level}
        itemKey={itemKey}
        definition={definition}
        path={path}
        handleGoToSource={handleGoToSource}
        isAvailable={isAvailable}
        filteredVariableNames={filteredVariableNames}
        filter={filter}
        debugMode={debugMode}
      />
    );
  }

  const variable: MockVariable = {
    identifier: `${definition?.__identifier ?? itemKey}`,
    type: definition?.__type?.split(".")[0] ?? "unknown",
    description: definition?.__description ?? "",
    sourceStepVariant: definition?.__sourceStepVariant,
    sourceStepId: definition?.__sourceStepId ?? "unknown",
    availableFromStepId: definition?.__availableFromStepId ?? "unknown",
    isStartingVariable: (definition?.__isStartingVariable ?? false) as boolean,
    isEndingVariable: (definition?.__isEndingVariable ?? false) as boolean,
    iteratorParentId: definition?.__iteratorParentId,
    isAggregateOutput: definition?.__isAggregateOutput
  };

  return (
    <ConfigurationFlowVariable
      variant="line-item"
      variable={variable}
      handleGoToSource={handleGoToSource}
      handleRename={handleRename}
      copyText={debugMode ? `{{${definition.__path}}}` : `{{${path}}}`}
      isAvailable={isAvailable}
      kebabMenu={
        level === 0 ? <VariableKebabMenu variable={variable} /> : undefined
      }
    />
  );
};

const ExpandableVariablePanel = ({
  level,
  itemKey,
  path,
  definition,
  handleGoToSource,
  isAvailable = true,
  filteredVariableNames,
  filter,
  debugMode
}: {
  level: number;
  itemKey: string;
  path: string;
  definition: VariableTypeDefinition;
  handleGoToSource?: () => void;
  isAvailable?: boolean;
  filteredVariableNames: string[];
  filter?: {
    searchValue?: string;
    typeFilter?: string;
  };
  debugMode?: boolean;
}) => {
  const [isOpen, setIsOpen] = React.useState(false);

  const isMatch: boolean | undefined = useMemo(
    () =>
      !filter?.searchValue && !filter?.typeFilter
        ? undefined
        : (filter?.typeFilter && definition.__type === filter.typeFilter) ||
          (!!filter?.searchValue &&
            filter?.searchValue?.length > 0 &&
            itemKey.toLowerCase().includes(filter?.searchValue?.toLowerCase())),
    [definition.__type, filter?.searchValue, filter?.typeFilter, itemKey]
  );

  useEffect(() => {
    if (isMatch === undefined) {
      return;
    }

    setIsOpen(!isMatch);
  }, [isMatch]);

  return (
    <Accordion
      isOpen={isOpen}
      onOpenChange={() => setIsOpen(!isOpen)}
      className="flow-variable-expandable"
      trigger={({ onClick }) => {
        const variable = {
          identifier: `${definition?.__identifier ?? itemKey}`,
          type: definition?.__type?.split(".")[0] ?? "unknown",
          description: definition?.__description ?? "",
          isStartingVariable: (definition?.__isStartingVariable ??
            false) as boolean,
          isEndingVariable: (definition?.__isEndingVariable ?? false) as boolean
        };
        return (
          <Inline gap="050" alignment="left" width="100">
            <Box
              onClick={onClick}
              style={{
                width: "var(--spacing-100, 8px)",
                marginLeft: "calc(var(--spacing-050, 2px) * -1)"
              }}
            >
              <OpenCloseIcon isOpen={isOpen} />
            </Box>
            <ConfigurationFlowVariable
              variant="line-item"
              variable={variable}
              handleGoToSource={handleGoToSource}
              handleRename={undefined}
              isAvailable={isAvailable}
              copyText={debugMode ? `{{${definition.__path}}}` : `{{${path}}}`}
              onClick={onClick}
              kebabMenu={
                level === 0 ? (
                  <VariableKebabMenu variable={variable} />
                ) : undefined
              }
            />
          </Inline>
        );
      }}
    >
      <Box
        style={{
          paddingLeft: "var(--spacing-200, 4px)"
        }}
      >
        {Object.entries(definition).map(([key, itemDefinition]) => {
          if (!itemDefinition || typeof itemDefinition === "string") {
            return <></>;
          }
          const nextDefinitions = Object.fromEntries(
            Object.entries(itemDefinition).filter(
              ([k, v]) => !k.startsWith("__") && v
            )
          );

          const newPath = `${path}.${key}`;
          const matchingVariables = filteredVariableNames.filter(name =>
            name.startsWith(newPath)
          );
          if (!matchingVariables.length) {
            return <></>;
          }
          if (Object.keys(nextDefinitions).length > 0) {
            return (
              <ExpandableVariablePanel
                level={level + 1}
                itemKey={key}
                path={newPath}
                definition={itemDefinition as VariableTypeDefinition}
                isAvailable={isAvailable}
                filteredVariableNames={matchingVariables}
                key={newPath}
                handleGoToSource={handleGoToSource}
                filter={filter}
                debugMode={debugMode}
              />
            );
          }

          return (
            <VariableItem
              level={level + 1}
              path={newPath}
              itemKey={key}
              key={newPath}
              debugMode={debugMode}
              definition={itemDefinition as VariableTypeDefinition}
              isAvailable={isAvailable}
              filteredVariableNames={matchingVariables}
              handleGoToSource={handleGoToSource}
              filter={filter}
            />
          );
        })}
      </Box>
    </Accordion>
  );
};

const VariableKebabMenu = ({ variable }: { variable: MockVariable }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { configurationFlow } = useConfigurationFlowContext();
  const { docChange } = useOutletContext<{
    docChange: DocChange;
  }>();

  const getEndingVariableFromDoc = useCallback(
    (
      d: WorkspaceDocument
    ): {
      endingVariables?: VariableConfiguration[];
      existingEndingVariable?: VariableConfiguration;
    } => {
      if (!configurationFlow) {
        return {};
      }
      const flowConfiguration = getByPath<FlowConfiguration>(d, [
        "flows",
        "entities",
        configurationFlow.id
      ]);
      if (!flowConfiguration) {
        return {};
      }
      flowConfiguration.endingVariables ??= [];
      const existingEndingVariable = flowConfiguration.endingVariables.find(
        item => item.identifier === variable.identifier
      );
      return {
        endingVariables: flowConfiguration.endingVariables,
        existingEndingVariable
      };
    },
    [configurationFlow, variable.identifier]
  );

  const handleMakeEndingVariable = useCallback(() => {
    docChange(d => {
      const { endingVariables, existingEndingVariable } =
        getEndingVariableFromDoc(d);
      if (!endingVariables) {
        return;
      }
      if (existingEndingVariable) {
        return;
      } else {
        endingVariables.push({
          type: variable.type,
          identifier: variable.identifier,
          properties: variable.properties
        });
      }
    });
  }, [docChange, getEndingVariableFromDoc, variable]);

  const handleRemoveAsEndingVariable = useCallback(() => {
    docChange(d => {
      const { endingVariables, existingEndingVariable } =
        getEndingVariableFromDoc(d);
      if (!endingVariables) {
        return;
      }
      if (existingEndingVariable) {
        endingVariables.splice(
          endingVariables.indexOf(existingEndingVariable),
          1
        );
      }
    });
  }, [docChange, getEndingVariableFromDoc]);

  const callWithClose = (callback: () => void) => () => {
    setIsOpen(false);
    callback();
  };

  return (
    <KebabMenu
      className={getClassNames([
        "nodrag",
        "nopan",
        "flow-variable-kebab-menu",
        `flow-variable__kebab--${isOpen ? "open" : "closed"}`
      ])}
      isOpen={isOpen}
      onOpenChange={setIsOpen}
      position={FloatingWithParentPosition.BOTTOM_RIGHT}
    >
      <DropdownItemGroup>
        {!variable.isEndingVariable && handleMakeEndingVariable && (
          <DropdownItem
            leftElement={<Icon name="chip_extraction" />}
            onClick={callWithClose(handleMakeEndingVariable)}
            description={"For flows in flows"}
          >
            Make ending variable
          </DropdownItem>
        )}
        {variable.isEndingVariable && handleRemoveAsEndingVariable && (
          <DropdownItem
            leftElement={<Icon name="chip_extraction" />}
            onClick={callWithClose(handleRemoveAsEndingVariable)}
            description={"For flows in flows"}
          >
            Remove as ending variable
          </DropdownItem>
        )}
      </DropdownItemGroup>
    </KebabMenu>
  );
};
