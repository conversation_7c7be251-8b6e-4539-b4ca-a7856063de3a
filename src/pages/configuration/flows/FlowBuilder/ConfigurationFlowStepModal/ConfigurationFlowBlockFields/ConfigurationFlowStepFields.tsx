import React, { use<PERSON><PERSON>back, useMemo, useState } from "react";

import { Doc } from "@automerge/automerge-repo";
import {
  Alert,
  Box,
  FontWeight,
  Heading,
  HeadingSize,
  Icon,
  Inline,
  Renamable,
  Stack,
  Text,
  WhiteboardBlock,
  getClassNames
} from "@oneteam/onetheme";
import get from "lodash/get";
import { useOutletContext } from "react-router-dom";

import { runBooleanConditionCheck } from "@helpers/conditionHelper";
import { getByPath } from "@helpers/configurationFormHelper";
import {
  getLocalStepContext,
  populateRealValuesUsingMockFlowContext
} from "@helpers/flows/flowHelpers";
import {
  getVariableMappingsFromStepTypeConfiguration,
  getVariablesForStep
} from "@helpers/flows/flowVariableHelpers";
import {
  InternalOtaiFormField,
  internalOtaiFormField
} from "@helpers/otaiFormHelper";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";
import { ConfigurationFlowVariable } from "@pages/configuration/flows/FlowBuilder/ConfigurationFlowVariable/ConfigurationFlowVariable";
import { FlowStepKebabMenu } from "@pages/configuration/flows/FlowCanvas/FlowStep/FlowStepKebabMenu/FlowStepKebabMenu";
import { useFlowStepHandlers } from "@pages/configuration/flows/hooks/useFlowStepHandlers";
import { DocChange } from "@pages/workspace/WorkspaceLayout";

import { useDictionary } from "@src/hooks/useDictionary";
import { Operators } from "@src/types/FlowConfiguration/Condition.ts";
import { FlowConfiguration } from "@src/types/FlowConfiguration/FlowConfiguration";
import {
  FlowStep,
  FlowStepVariant,
  flowStepVariantsWithTypeConfiguration
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import {
  CommonTypedStepProperties,
  ConditionStepProperties,
  FlowVariantStepProperties,
  SetVariablesStepProperties,
  TriggerStepProperties
} from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";
import { TriggerDocumentation } from "@src/types/FlowConfiguration/FlowStepTypeConfiguration/FlowStepTypeConfiguration";
import {
  TableVariableColumn,
  Variable,
  VariableConfiguration,
  VariableFieldKeys,
  VariableValue
} from "@src/types/FlowConfiguration/Variables";
import { QuestionTypes } from "@src/types/Question";
import { WorkspaceDocument } from "@src/types/documentTypes";

import { ConditionFlowStepProperties } from "../condition/ConditionFlowStepProperties";
import { SetVariablesFlowStepProperties } from "../setVariables/SetVariablesFlowStepProperties";
import "./ConfigurationFlowStepFields.scss";
import { CustomPropertyField } from "./CustomPropertyField";
import { FieldDivider } from "./FieldDivider";
import { FlowPropertyFields } from "./FlowPropertyFields";
import { RichTextContent } from "./RichTextContent";

export const ConfigurationFlowStepFields = ({
  selectedStep,
  disabled,
  onNameChange,
  openFlowStepTypeModal,
  docChange
}: {
  disabled?: boolean;
  selectedStep: FlowStep;
  docChange: DocChange;
  onNameChange: (value: string) => void;
  openFlowStepTypeModal: () => void;
}) => {
  const d = useDictionary();
  const { document } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
  }>();
  const { handleDeleteFlowStep } = useFlowStepHandlers();
  const {
    configurationFlow,
    selectingStepType,
    mockFlowContext,
    setSelectedStepId,
    path,
    settings,
    flowStepTypeConfigByPrimaryIdentifier
  } = useConfigurationFlowContext();
  const [autoFocusName, setAutoFocusName] = useState<boolean | undefined>(
    undefined
  );
  const selectedStepTypeConfig = useMemo(
    () =>
      flowStepVariantsWithTypeConfiguration.includes(
        selectedStep?.variant as FlowStepVariant
      )
        ? flowStepTypeConfigByPrimaryIdentifier?.[
            (selectedStep?.properties as CommonTypedStepProperties)
              ?.typePrimaryIdentifier
          ]
        : undefined,
    [
      flowStepTypeConfigByPrimaryIdentifier,
      selectedStep?.properties,
      selectedStep?.variant
    ]
  );

  const stepTypeContentQuestions = useMemo(() => {
    if (!selectedStepTypeConfig?.properties.configuration.content?.length) {
      return undefined;
    }
    return selectedStepTypeConfig.properties.configuration.content.map(q => {
      const question = internalOtaiFormField(q as InternalOtaiFormField);
      const stepProperties =
        selectedStep.properties as CommonTypedStepProperties;
      const answer = stepProperties?.inputs?.[q.identifier];
      return {
        ...question,
        answer: answer as string
      };
    });
  }, [selectedStepTypeConfig, selectedStep]);

  const stepTypeDocumentation = useMemo(() => {
    if (
      !selectedStepTypeConfig?.properties.configuration.documentation?.length ||
      !mockFlowContext
    ) {
      return undefined;
    }
    return selectedStepTypeConfig.properties.configuration.documentation
      .map(triggerDocumentation => {
        const resolvedTemplate = populateRealValuesUsingMockFlowContext(
          triggerDocumentation,
          getLocalStepContext({
            flowContext: mockFlowContext,
            step: selectedStep
          }),
          true
        );
        return resolvedTemplate as TriggerDocumentation;
      })
      .map(documentation => {
        return {
          ...documentation,
          richText: documentation.richText.filter(richText => {
            const isHidden = runBooleanConditionCheck(richText.hidden);
            return !isHidden;
          })
        };
      });
  }, [mockFlowContext, selectedStep, selectedStepTypeConfig]);

  // Change this to use path
  const getPathInDocument = useCallback(
    (type: "triggers" | "steps") => [
      "flows",
      "entities",
      configurationFlow?.id ?? "",
      ...(path ?? []),
      type,
      selectedStep.id,
      "properties"
    ],
    [configurationFlow?.id, selectedStep.id, path]
  );

  const updateTriggerStartingVariables = useCallback(
    ({ prevInputs = {} }: { prevInputs?: TriggerStepProperties["inputs"] }) => {
      if (!selectedStepTypeConfig || !mockFlowContext || !configurationFlow) {
        return;
      }
      docChange(d => {
        const flow = getByPath<FlowConfiguration>(d, [
          "flows",
          "entities",
          configurationFlow?.id
        ]);

        const currentStep = flow.triggers?.[
          selectedStep.id
        ] as FlowStep<TriggerStepProperties>;

        const stepContext = getLocalStepContext({
          step: currentStep,
          flowContext: mockFlowContext
        });

        if (currentStep.properties.typePrimaryIdentifier) {
          const prevStepTypeConfig =
            flowStepTypeConfigByPrimaryIdentifier?.[
              currentStep?.properties?.typePrimaryIdentifier
            ];
          const prevStepContext = getLocalStepContext({
            step: {
              ...currentStep,
              properties: {
                ...currentStep.properties,
                inputs: prevInputs
              }
            },
            flowContext: mockFlowContext
          });

          if (prevStepTypeConfig) {
            const prevStartingVariableIdentifiers =
              getVariableMappingsFromStepTypeConfiguration({
                variant: currentStep.variant,
                flowStepTypeConfig: prevStepTypeConfig
              }).map((variableMapping: Variable) => {
                const result = populateRealValuesUsingMockFlowContext(
                  variableMapping,
                  prevStepContext
                );
                return result.identifier;
              });

            let index = 0;
            flow.startingVariables?.forEach(variable => {
              // remove old starting variables
              if (
                variable.identifier &&
                prevStartingVariableIdentifiers.includes(variable.identifier)
              ) {
                flow.startingVariables?.splice(index, 1);
              } else {
                index++;
              }
            });
          }
        }

        const resolvedStartingVariables =
          getVariableMappingsFromStepTypeConfiguration({
            variant: currentStep.variant,
            flowStepTypeConfig: selectedStepTypeConfig
          }).map((variableMapping: Variable) => {
            const result = populateRealValuesUsingMockFlowContext(
              variableMapping,
              stepContext
            );
            // Starting variables should not have a value
            delete result["value"];
            result["properties"] ??= {};
            result["properties"].required = true;
            return result;
          });

        flow.startingVariables = resolvedStartingVariables;

        // TODO in OA-1894: Remove above and add below logic back
        // --- do not replace starting variables - just remove the old trigger starting variables and then these new ones
        // flow.startingVariables ??= [];
        // resolvedStartingVariables.forEach(variable => {
        //   const existingVariable = flow.startingVariables?.find(
        //     existingVariable =>
        //       existingVariable.identifier === variable.identifier
        //   );
        //   if (existingVariable) {
        //     existingVariable.properties = {
        //       ...existingVariable.properties,
        //       ...variable.properties
        //     };
        //     existingVariable.type = variable.type;
        //   } else {
        //     flow.startingVariables?.push(variable);
        //   }
        // });
      });
    },
    [
      selectedStepTypeConfig,
      mockFlowContext,
      configurationFlow,
      docChange,
      selectedStep.id,
      flowStepTypeConfigByPrimaryIdentifier
    ]
  );

  const handleSaveInput = useCallback(
    (value: VariableValue, identifier: string) => {
      const type =
        selectedStep.variant === FlowStepVariant.TRIGGER ? "triggers" : "steps";
      const pathInDocument = getPathInDocument(type);
      const prevInputs = (selectedStep.properties as CommonTypedStepProperties)
        ?.inputs;
      docChange(d => {
        const properties = getByPath<CommonTypedStepProperties>(
          d,
          pathInDocument
        );
        properties.inputs ??= {};
        if (properties.inputs[identifier] !== value) {
          properties.inputs[identifier] = Array.isArray(value)
            ? JSON.stringify(value)
            : (value ?? "");
        }
      });

      if (selectedStep.variant === FlowStepVariant.TRIGGER) {
        updateTriggerStartingVariables({ prevInputs });
      }
    },
    [selectedStep, getPathInDocument, docChange, updateTriggerStartingVariables]
  );

  const handleSaveSetVariablesField = useCallback(
    (index: number, field: VariableFieldKeys, value?: string) => {
      const pathInDocument = [...getPathInDocument("steps"), "variables"];
      docChange(doc => {
        const variables: Variable[] = getByPath<
          SetVariablesStepProperties["variables"]
        >(doc, pathInDocument);
        function reRenderProperties() {
          // TODO: fix text input not rendering updated
          const selectedStepId = selectedStep.id;
          setSelectedStepId();
          setTimeout(() => {
            setSelectedStepId(selectedStepId);
          });
        }

        if (
          field === "identifier" &&
          value &&
          value !== variables[index].identifier
        ) {
          // try to find existing variable with the same identifier
          if (mockFlowContext?.stepOutputVariables.includes(value)) {
            alert(d("ui.configuration.flows.variables.output.cannotSet"));
            reRenderProperties();
            return;
          }
          const existingVariable = mockFlowContext?.variables?.[value];
          if (existingVariable) {
            const currentValue = variables[index].value;
            variables[index] = {
              type: existingVariable.type,
              identifier: existingVariable.identifier,
              value: currentValue,
              properties:
                existingVariable.type === QuestionTypes.TABLE
                  ? {
                      columns: structuredClone(
                        existingVariable.properties?.columns
                      )
                    }
                  : {}
            };

            reRenderProperties();
            return;
          }
        }

        const currentValue = get(variables, [index, field]);

        if (currentValue !== value) {
          if (field === "type") {
            delete variables[index].properties;
            // TODO: when updating a type, look for and update other set variables with the same identifier and update them too
          }

          const [path1, path2] = field.split(".");
          if (path1 === "properties") {
            variables[index].properties ??= {};
            variables[index].properties[path2] = value;
          } else if (variables[index]) {
            let resolvedValue = value ?? "";
            if (field === "type" && !value) {
              resolvedValue = QuestionTypes.TEXT;
            }
            variables[index][path1 as "type" | "identifier" | "value"] =
              resolvedValue;
          }
        }

        variables[index].type ??= QuestionTypes.TEXT;
        // TODO: After pre-mvp, we can convert variables from array to OrderedMap in document, if needed
      });
    },
    [
      getPathInDocument,
      docChange,
      mockFlowContext?.stepOutputVariables,
      mockFlowContext?.variables,
      d,
      selectedStep.id,
      setSelectedStepId
    ]
  );

  const handleAddSetVariable = useCallback(
    (variable: Variable) => {
      const pathInDocument = getPathInDocument("steps");
      docChange(d => {
        const properties = getByPath<SetVariablesStepProperties>(
          d,
          pathInDocument
        );
        properties.variables.push(variable);
      });
    },
    [getPathInDocument, docChange]
  );

  const setVariableColumnsForStep = useCallback(
    ({
      columns,
      change,
      stepId,
      identifier
    }: {
      columns: VariableConfiguration[];
      change: { op: string; index: number };
      stepId: string;
      identifier: string;
    }) => {
      const pathInDocument = [...getPathInDocument("steps"), "variables"].with(
        4,
        stepId
      );
      docChange(d => {
        const variables: Variable[] = getByPath<
          SetVariablesStepProperties["variables"]
        >(d, pathInDocument);

        if (variables.length === 0) {
          return;
        }

        const index = variables.findIndex(
          variable => variable.identifier === identifier
        );

        if (index === -1) {
          return;
        }

        variables[index].properties ??= {};

        const properties = variables[index]
          .properties as VariableConfiguration["properties"];

        if (properties) {
          properties.columns ??= [];
          switch (change.op) {
            case TableVariableColumn.ADD:
              properties?.columns.push(columns[0]);
              break;
            case TableVariableColumn.DELETE:
              properties.columns.splice(change.index, 1);
              break;
            case TableVariableColumn.UPDATE:
              properties.columns[change.index] = columns[0];
              break;
            case TableVariableColumn.REORDER:
              properties.columns = columns;
              break;
          }
        }
      });
    },
    [getPathInDocument, docChange]
  );

  const handleSetVariableColumns = useCallback(
    ({
      variableIdentifier,
      sourceVariableStepIds,
      columns,
      change
    }: {
      variableIdentifier: string;
      sourceVariableStepIds: string[];
      columns: VariableConfiguration[];
      change: { op: string; index: number };
    }) => {
      sourceVariableStepIds.forEach(stepId =>
        setVariableColumnsForStep({
          columns,
          change,
          stepId,
          identifier: variableIdentifier
        })
      );
    },
    [setVariableColumnsForStep]
  );

  const handleRemoveSetVariable = useCallback(
    (index: number) => {
      const pathInDocument = getPathInDocument("steps");
      docChange(doc => {
        const variables = getByPath<SetVariablesStepProperties["variables"]>(
          doc,
          [...pathInDocument, "variables"]
        );

        if (variables.length === 1) {
          alert(
            d(
              "ui.configuration.flows.step.variants.setVariables.propertiesModal.delete.lastVariable"
            )
          );
          return;
        }

        variables.splice(index, 1);
      });
    },
    [d, docChange, getPathInDocument]
  );

  const customPropertiesFields = useMemo(() => {
    if (!selectedStep) {
      return <>Properties not implemented</>;
    }
    if (disabled) {
      return <></>;
    }

    if (
      flowStepVariantsWithTypeConfiguration.includes(
        selectedStep.variant as FlowStepVariant
      )
    ) {
      if (
        selectingStepType ||
        !stepTypeContentQuestions?.length ||
        selectedStepTypeConfig?.type !== selectedStep.variant
      ) {
        return <></>;
      }

      const { variables, stepOutputVariables } = getVariablesForStep({
        step: selectedStep,
        workspaceDocument: document,
        flowStepTypeConfig: selectedStepTypeConfig,
        flowContext: mockFlowContext
      });

      const outputVariables = variables.filter(variable => {
        return stepOutputVariables?.includes(variable.identifier);
      });

      return (
        <Stack
          gap="150"
          width="100"
          contentsWidth="100"
          height="100"
          spaceBetween
        >
          <Stack gap="150" width="100" contentsWidth="100" height="100">
            <FieldDivider
              label={d(
                "ui.configuration.flows.step.variants.common.propertiesModal.inputs.label"
              )}
            />
            {stepTypeContentQuestions.map(question => (
              <CustomPropertyField
                key={question.identifier}
                question={question}
                selectedStep={selectedStep}
                handleSaveInput={handleSaveInput}
                disabled={disabled}
                mockFlowContext={mockFlowContext}
                debugMode={settings?.debugMode}
                pathInDocument={[
                  ...getPathInDocument(
                    selectedStep.variant === FlowStepVariant.TRIGGER
                      ? "triggers"
                      : "steps"
                  ),
                  "inputs" // all flowStepVariantsWithTypeConfiguration have inputs
                ]}
              />
            ))}

            <FieldDivider
              label={d("ui.configuration.flows.variables.output.label")}
            />
            {outputVariables && outputVariables?.length > 0 ? (
              <Stack>
                {outputVariables.map(variable => {
                  return (
                    <ConfigurationFlowVariable
                      key={variable.identifier}
                      variable={variable}
                      variant="line-item"
                    />
                  );
                })}
              </Stack>
            ) : (
              <Text size="xs" weight="regular" color="text-tertiary">
                {d("ui.configuration.flows.variables.output.empty")}
              </Text>
            )}
          </Stack>

          {stepTypeDocumentation?.map(documentation => (
            <Stack key={documentation.title} gap="100">
              <FieldDivider label={documentation.title} />
              <RichTextContent richTextNodes={documentation.richText} />
            </Stack>
          ))}
        </Stack>
      );
    } else if (selectedStep.variant === FlowStepVariant.SET_VARIABLES) {
      return (
        <SetVariablesFlowStepProperties
          path={[...getPathInDocument("steps"), "variables"]}
          step={selectedStep as FlowStep<SetVariablesStepProperties>}
          onFieldChange={handleSaveSetVariablesField}
          onVariableAdd={handleAddSetVariable}
          onVariableRemove={handleRemoveSetVariable}
          onVariableUpdate={handleSetVariableColumns}
        />
      );
    } else if (selectedStep.variant === FlowStepVariant.CONDITION) {
      return (
        <ConditionFlowStepProperties
          step={selectedStep as FlowStep<ConditionStepProperties>}
          docAccessor={getPathInDocument("steps")}
          onBranchAdd={() => {
            docChange(d => {
              const properties = getByPath<ConditionStepProperties>(
                d,
                getPathInDocument("steps")
              );
              properties.branches.push({
                name: `If ${properties.branches.length + 1}`,
                condition: {
                  lhs: "",
                  operator: Operators.EQUALS,
                  rhs: ""
                },
                next: null
              });
            });
          }}
          onBranchRemove={
            (selectedStep.properties as ConditionStepProperties).branches
              .length === 1
              ? undefined
              : index => {
                  docChange(doc => {
                    const properties = getByPath<ConditionStepProperties>(
                      doc,
                      getPathInDocument("steps")
                    );
                    if (properties.branches.length === 1) {
                      alert(
                        d(
                          "ui.configuration.flows.step.variants.condition.propertiesModal.delete.lastBranch"
                        )
                      );
                      return;
                    } else if (properties.branches[index].next) {
                      alert(
                        d(
                          "ui.configuration.flows.step.variants.condition.propertiesModal.delete.hasNext"
                        )
                      );
                      return;
                    } else {
                      properties.branches.splice(index, 1);
                    }
                  });
                }
          }
        />
      );
    }
    return <></>;
  }, [
    selectedStep,
    disabled,
    selectingStepType,
    stepTypeContentQuestions,
    selectedStepTypeConfig,
    document,
    mockFlowContext,
    d,
    handleSaveInput,
    settings?.debugMode,
    getPathInDocument,
    handleSaveSetVariablesField,
    handleAddSetVariable,
    handleRemoveSetVariable,
    handleSetVariableColumns,
    docChange,
    stepTypeDocumentation
  ]);

  const deprecatedWarning = useMemo(() => {
    if (!selectedStepTypeConfig?.properties.deprecated) {
      return <></>;
    }

    const replacementStepTypePrimaryIdentifier =
      selectedStepTypeConfig?.properties.deprecated?.replacement;

    const replacementStepType = replacementStepTypePrimaryIdentifier
      ? flowStepTypeConfigByPrimaryIdentifier?.[
          replacementStepTypePrimaryIdentifier
        ]
      : undefined;

    return (
      <Alert variant="warning" width="100">
        <Stack>
          <Text weight="semi-bold">
            {d("ui.configuration.flows.stepType.deprecatedTypeWarning.message")}
          </Text>
          {replacementStepType && (
            <Text>
              {d(
                "ui.configuration.flows.stepType.deprecatedTypeWarning.suggestion",
                {
                  replacementStepTypeName: replacementStepType.name
                }
              )}
            </Text>
          )}
        </Stack>
      </Alert>
    );
  }, [
    d,
    selectedStepTypeConfig?.properties.deprecated,
    flowStepTypeConfigByPrimaryIdentifier
  ]);

  const flowStepTypeConfigurationDisplay = useMemo(() => {
    if (
      !flowStepVariantsWithTypeConfiguration.includes(
        selectedStep.variant as FlowStepVariant
      )
    ) {
      return;
    }

    return (
      <>
        <FieldDivider
          label={d(
            "ui.configuration.flows.step.variants.common.propertiesModal.flowStepType.label"
          )}
        />
        <WhiteboardBlock
          key={`${selectedStep.id}-type-${selectedStepTypeConfig?.primaryIdentifier ?? "none"}`}
          onClick={openFlowStepTypeModal}
          className={getClassNames(["flow-configuration-block"])}
        >
          <Stack padding="100" alignment="left" gap="050">
            <Inline width="100" alignment="left" spaceBetween>
              {selectedStepTypeConfig ? (
                <Inline alignment="center" gap="050">
                  <Inline gap="025" alignment="left">
                    {selectedStepTypeConfig?.properties?.category?.icon &&
                      selectedStepTypeConfig?.properties?.category?.icon
                        .name !==
                        selectedStepTypeConfig?.properties?.icon.name && (
                        <Icon
                          {...selectedStepTypeConfig?.properties?.category
                            ?.icon}
                          size="m"
                          color="text-secondary"
                        />
                      )}
                    {selectedStepTypeConfig?.properties?.icon && (
                      <Icon
                        size="m"
                        color="text-primary"
                        {...selectedStepTypeConfig?.properties?.icon}
                      />
                    )}
                  </Inline>
                  <Stack width="100" gap="000" alignment="left">
                    <Text size="m" weight={FontWeight.REGULAR}>
                      {selectedStepTypeConfig.name}
                    </Text>
                    {selectedStepTypeConfig?.description && (
                      <Text color="text-tertiary" size="s">
                        {selectedStepTypeConfig.description}
                      </Text>
                    )}
                  </Stack>
                </Inline>
              ) : (
                // should never happen - just in case
                <Box width="100">
                  <Text
                    size="m"
                    weight={FontWeight.REGULAR}
                    color="text-tertiary"
                  >
                    {d(
                      "ui.configuration.flows.step.variants.common.propertiesModal.flowStepType.select"
                    )}
                  </Text>
                </Box>
              )}
              <Icon size="m" name="edit" color="primary" />
            </Inline>
            {deprecatedWarning}
          </Stack>
        </WhiteboardBlock>
      </>
    );
  }, [
    d,
    deprecatedWarning,
    openFlowStepTypeModal,
    selectedStep.id,
    selectedStep.variant,
    selectedStepTypeConfig
  ]);

  return (
    <Stack
      gap="100"
      contentsWidth="100"
      height="100"
      style={{ overflowX: "hidden", overflowY: "auto" }}
    >
      <Inline>
        <Stack
          gap="050"
          alignment="left"
          style={{
            maxWidth: "100%"
          }}
          width="100"
        >
          <Heading
            size={HeadingSize.S}
            weight={FontWeight.MEDIUM}
            style={{
              maxWidth: "100%"
            }}
          >
            <Renamable
              value={selectedStep?.name}
              onChange={name => {
                onNameChange(name);
                setAutoFocusName(undefined);
              }}
              controlFocus={autoFocusName}
            />
          </Heading>
          {settings?.debugMode && (
            <Text size="s" color="text-tertiary">
              {selectedStep.id}
            </Text>
          )}
        </Stack>
        <FlowStepKebabMenu
          // handleDuplicate={() => console.log("Copy")}
          // handleEdit={() => console.log("Edit")}
          handleDelete={
            selectedStep.variant === FlowStepVariant.TRIGGER
              ? undefined
              : () =>
                  handleDeleteFlowStep({
                    stepId: selectedStep.id,
                    variant: selectedStep.variant as FlowStepVariant,
                    path: path ?? []
                  })
          }
          handleRename={() => {
            setAutoFocusName(true);

            setTimeout(() => {
              setAutoFocusName(undefined);
            });
          }}
        />
      </Inline>

      <Stack height="100" gap="100" contentsWidth="100" overflow="auto">
        {flowStepTypeConfigurationDisplay}
        {/* Custom type fields */}
        {!selectingStepType &&
          selectedStep !== undefined &&
          customPropertiesFields}
        {selectedStep?.variant === FlowStepVariant.FLOW && (
          <FlowPropertyFields
            selectedStep={selectedStep as FlowStep<FlowVariantStepProperties>}
            handleSaveInput={handleSaveInput}
            disabled={disabled}
            mockFlowContext={mockFlowContext}
            settings={settings}
            pathInDocument={[...getPathInDocument("steps"), "inputs"]}
          />
        )}
      </Stack>
    </Stack>
  );
};
