import React from "react";

import { Box, Pill } from "@oneteam/onetheme";
import {
  BaseEdge,
  Edge<PERSON>abel<PERSON><PERSON><PERSON>,
  getSimpleBezierPath
} from "@xyflow/react";

import { useFlowStepHandlers } from "@pages/configuration/flows/hooks/useFlowStepHandlers";

import { useDictionary } from "@src/hooks/useDictionary";
import { FlowStepVariant } from "@src/types/FlowConfiguration/FlowStep/FlowStep";

import { FlowAddButton } from "../FlowAddButton/FlowAddButton";
import { useFlowCanvas } from "../FlowCanvasContext";
import { EdgeProps } from "./AddButtonEdge";

export interface ConditionEdgeProps extends EdgeProps {
  data: {
    id: string;
    label: string;
    variant: FlowStepVariant;
    hasNode: boolean;
    branchIndex: number;
  };
}

export const ConditionEdge = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  data,
  hidden = false
}: ConditionEdgeProps) => {
  const ADD_BUTTON_LINE_OFFSET = 15;
  const [edgePath, labelX, labelY] = getSimpleBezierPath({
    sourceX,
    sourceY,
    targetX,
    targetY: targetY - ADD_BUTTON_LINE_OFFSET
  });
  // add straight line to end of path (for the add button)
  const modifiedEdgePath = `${edgePath} L${targetX},${targetY}`;

  const d = useDictionary();
  const { handleAddFlowStep, handlePasteFlowStep } = useFlowStepHandlers();
  const { path } = useFlowCanvas();
  return (
    <>
      <BaseEdge
        id={id}
        path={modifiedEdgePath}
        style={hidden ? { opacity: 0 } : {}}
      />
      <EdgeLabelRenderer>
        <Box
          style={{
            position: "absolute",
            transform: `translate(-50%, -50%) translate(${labelX}px, ${labelY}px)`,
            pointerEvents: "all"
          }}
        >
          <Pill
            className="flow-edge-label"
            label={d(data.label) !== data.label ? d(data.label) : data.label}
          />
        </Box>
        {data.hasNode && (
          <Box
            style={{
              position: "absolute",
              transform: `translate(-50%, -50%) translate(${targetX}px, ${targetY - ADD_BUTTON_LINE_OFFSET - 3}px)`,
              pointerEvents: "all",
              zIndex: 100
            }}
          >
            <FlowAddButton
              addStep={variant => {
                handleAddFlowStep({
                  variant,
                  parentStepId: data?.id,
                  branchIndex: data?.branchIndex,
                  path
                });
              }}
              pasteStep={async clipboardStep => {
                handlePasteFlowStep({
                  parentStepId: data?.id,
                  branchIndex: data?.branchIndex,
                  skipTypeSelect: true,
                  clipboardStep,
                  path
                });
              }}
            />
          </Box>
        )}
      </EdgeLabelRenderer>
    </>
  );
};
