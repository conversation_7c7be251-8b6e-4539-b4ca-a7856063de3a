import React, { useCallback, useEffect, useMemo } from "react";

import { Box, Stack, WhiteboardLine } from "@oneteam/onetheme";
import {
  Handle,
  NodeProps,
  Position,
  ReactFlowProvider,
  useNodeConnections
} from "@xyflow/react";
import "@xyflow/react/dist/style.css";

import { FlowStep } from "@pages/configuration/flows/FlowCanvas/FlowStep/FlowStep";
import { IteratorFlowStep } from "@pages/configuration/flows/FlowCanvas/FlowStep/IteratorFlowStep/IteratorFlowStep";
import { useFlowStepHandlers } from "@pages/configuration/flows/hooks/useFlowStepHandlers";

import {
  FlowStepId,
  FlowStep as FlowStepType,
  FlowStepVariant
} from "@src/types/FlowConfiguration/FlowStep/FlowStep";
import { IteratorStepProperties } from "@src/types/FlowConfiguration/FlowStep/FlowStepProperties";

import { useConfigurationFlowContext } from "../../ConfigurationFlowContext";
import { FlowAddButton } from "../FlowAddButton/FlowAddButton";
import { useFlowCanvas } from "../FlowCanvasContext";
import { FlowCanvasProvider } from "../FlowCanvasProvider";
import "./FlowNodes.scss";
import { FlowStepNodeProps } from "./FlowStepNodeProps";

export const FlowStepNode = (props: NodeProps<FlowStepNodeProps>) => {
  const step = useMemo(() => props.data.step, [props.data.step]);
  const { path } = useFlowCanvas();
  const {
    selectedStepId,
    setSelectedStepId,
    setSelectedStepIdWithUnfocus,
    setPath
  } = useConfigurationFlowContext();
  const {
    handleAddFlowStep,
    handlePasteFlowStep,
    handleDeleteFlowStep,
    handleUpdateFlowStepName,
    handleDuplicateFlowStep
  } = useFlowStepHandlers();

  const outgoingConnections = useNodeConnections({
    id: props.id,
    handleType: "source"
  });

  const onClick = useCallback(
    (stepId: FlowStepId) => {
      if (!stepId) {
        return;
      }

      if (stepId === selectedStepId) {
        setSelectedStepIdWithUnfocus();
        return;
      }
      setSelectedStepIdWithUnfocus(stepId);
      setSelectedStepId(undefined);
      setTimeout(() => setSelectedStepId(stepId), 0);
    },
    [selectedStepId, setSelectedStepId, setSelectedStepIdWithUnfocus]
  );

  const handleDelete = useCallback(
    (stepId: FlowStepId, variant: FlowStepVariant) => {
      if (!stepId) {
        return;
      }
      handleDeleteFlowStep({
        variant: variant as `${FlowStepVariant}`,
        stepId: stepId,
        path
      });
    },
    [handleDeleteFlowStep, path]
  );

  useEffect(() => {
    if (selectedStepId === step?.id) {
      setPath(path);
    }
  });

  if (!path) {
    return <></>;
  }

  if (!step?.id) {
    return <></>;
  }
  return (
    <Box padding="000" className="flow-step-container" alignment="center">
      <Handle type="target" position={Position.Top} style={{ opacity: 0 }} />
      {step?.variant === FlowStepVariant.ITERATOR ? (
        <FlowCanvasProvider accessor={step.id}>
          <ReactFlowProvider>
            <IteratorFlowStep
              flowStep={step as FlowStepType<IteratorStepProperties>}
              onClick={onClick}
              handleDelete={handleDelete}
              handleDuplicate={() =>
                step?.id && handleDuplicateFlowStep({ stepId: step?.id, path })
              }
              style={props.data.hidden ? { opacity: 0 } : {}}
              className={"nodrag nopan"}
              hasConnections={outgoingConnections.length > 0}
            />
          </ReactFlowProvider>
        </FlowCanvasProvider>
      ) : (
        <FlowStep
          flowStep={step}
          onClick={() => onClick(String(step?.id))}
          handleDelete={() =>
            handleDelete(String(step?.id), step?.variant as FlowStepVariant)
          }
          handleDuplicate={() =>
            handleDuplicateFlowStep({ stepId: String(step?.id), path })
          }
          isSelected={step?.id == selectedStepId}
          style={props.data.hidden ? { opacity: 0 } : {}}
          className={"nodrag nopan"}
          onNameChange={name =>
            handleUpdateFlowStepName({
              stepId: String(step?.id),
              name,
              path
            })
          }
        />
      )}
      {outgoingConnections.length === 0 && (
        <Stack alignment="center">
          <WhiteboardLine className="flow-step-last-edge" />
          <FlowAddButton
            addStep={variant => {
              // Off a regular branch
              handleAddFlowStep({
                variant,
                parentStepId: step?.id,
                branchIndex: 0,
                path
              });
            }}
            pasteStep={async clipboardStep => {
              handlePasteFlowStep({
                parentStepId: step?.id,
                branchIndex: 0,
                skipTypeSelect: true,
                clipboardStep,
                path
              });
            }}
          />
        </Stack>
      )}
      <Handle type="source" position={Position.Bottom} style={{ opacity: 0 }} />
    </Box>
  );
};
