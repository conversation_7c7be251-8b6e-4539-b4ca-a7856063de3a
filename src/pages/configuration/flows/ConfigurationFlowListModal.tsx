import React, { useCallback, useMemo, useRef } from "react";

import { Doc } from "@automerge/automerge-repo";
import {
  Box,
  Floating,
  FloatingPosition,
  Form,
  Modal,
  Overlay,
  Stack
} from "@oneteam/onetheme";
import { UseFormReturn, useFormContext } from "react-hook-form";
import {
  generatePath,
  useNavigate,
  useOutletContext,
  useParams
} from "react-router-dom";

import { customNanoId } from "@helpers/customNanoIdHelper.ts";
import { modalZIndex } from "@helpers/modalHelper.ts";

import { ConfigurationLabelsSelect } from "@components/shared/ConfigurationLabelsSelect/ConfigurationLabelsSelect";

import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { routeConstants } from "@src/constants/routeConstants";
import { Dictionary, useDictionary } from "@src/hooks/useDictionary.tsx";
import {
  Flow,
  FlowConfiguration,
  FlowConfigurationStatusType,
  flowConfigurationSchema
} from "@src/types/FlowConfiguration/FlowConfiguration";
import { LabelAvailableTo } from "@src/types/Label";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import "./ConfigurationFlowListModal.scss";

interface ConfigurationSeriesListModalFieldsProps {
  d: Dictionary;
}

const ConfigurationFlowListModalFields = ({
  d
}: ConfigurationSeriesListModalFieldsProps) => {
  const { watch } = useFormContext<Flow>();
  const labels = watch("labels");
  return (
    <Stack gap="100" width="100" contentsWidth="100">
      <Form.TextField
        label={d("ui.common.name")}
        name="name"
        required
        autoFocus
      />
      <Form.TextAreaField
        label={d("ui.common.description")}
        name="description"
      />
      <ConfigurationLabelsSelect
        isInForm
        label={d("ui.configuration.labels.title")}
        name="labels"
        labelFor={LabelAvailableTo.FLOW_CONFIGURATION}
        value={labels}
      />
    </Stack>
  );
};

export type ConfigurationFlowListModalProps = {
  onOpenChange: () => void;
  flowConfigToEdit?: FlowConfiguration;
};

const emptyFlowConfig: () => FlowConfiguration = () => ({
  name: "",
  description: "",
  id: "",
  startingVariables: [],
  endingVariables: [],
  triggers: {},
  steps: {},
  labels: [],
  status: FlowConfigurationStatusType.ACTIVE,
  metadata: {
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
});

export const ConfigurationFlowListModal = ({
  flowConfigToEdit,
  onOpenChange
}: ConfigurationFlowListModalProps) => {
  const d = useDictionary();
  const navigate = useNavigate();
  const params = useParams();
  const { docChange } = useOutletContext<{
    docChange: DocChange;
    document: Doc<WorkspaceDocument>;
  }>();
  const isEdit = useMemo(() => !!flowConfigToEdit, [flowConfigToEdit]);

  const formRef = useRef<UseFormReturn<FlowConfiguration> | null>(null);
  const setForm = (form: UseFormReturn<FlowConfiguration>) => {
    formRef.current = form;
  };

  const saveFlowConfiguration = useCallback(
    (flowConfig: FlowConfiguration) => {
      docChange(d => {
        if (isEdit) {
          if (!flowConfigToEdit) {
            console.error(
              `[saveFlowConfigurationHelper]: labelConfigToEdit is null in edit mode`
            );
            return;
          }
          flowConfig.id = flowConfigToEdit.id;
          docChange(d => {
            if (!d.flows?.entities) {
              console.error(
                `[saveFlowConfigurationHelper]: no flows in document`
              );
              return;
            }
            if (!(flowConfig.id in d.flows.entities)) {
              console.error(
                `[saveFlowConfigurationHelper]: no flows found with id ${flowConfig.id}`
              );
              return;
            }
            const currentFlowConfig = d.flows.entities[flowConfig.id];

            currentFlowConfig.name = flowConfig.name;
            currentFlowConfig.description = flowConfig.description;
            currentFlowConfig.labels = flowConfig.labels ?? [];
            currentFlowConfig.metadata.updatedAt = new Date().toISOString();
          });
          return;
        }

        if (
          d.flows === undefined ||
          d.flows?.entities === undefined ||
          d.flows?.order === undefined
        ) {
          d.flows = {
            entities: d.flows?.entities ?? {},
            order: d.flows?.order ?? []
          };
        }

        const newId = customNanoId();
        d.flows.entities[newId] = {
          ...emptyFlowConfig(),
          ...flowConfig,
          id: newId,
          metadata: {
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        };
        d.flows.order.push(newId);
        navigate(
          generatePath(routeConstants.configurationFlow, {
            ...params,
            configurationFlowId: newId
          })
        );
      });
      onOpenChange();
    },
    [docChange, onOpenChange, navigate, params, isEdit, flowConfigToEdit]
  );

  return (
    <Box style={{ zIndex: modalZIndex }}>
      <Overlay isOpen></Overlay>
      <Floating position={FloatingPosition.CENTER}>
        <Modal
          className="configuration-flow-list-modal"
          onOpenChange={onOpenChange}
          heading={d(
            isEdit
              ? "ui.configuration.flows.edit.title"
              : "ui.configuration.flows.creation.title"
          )}
        >
          <Form
            schema={flowConfigurationSchema(d)}
            submitLabel={d("ui.common.save")}
            d={d}
            defaultValues={flowConfigToEdit ?? emptyFlowConfig()}
            handleSubmit={saveFlowConfiguration}
            handleCancel={onOpenChange}
            cancelLabel={d("ui.common.cancel")}
            setForm={setForm}
          >
            <ConfigurationFlowListModalFields d={d} />
          </Form>
        </Modal>
      </Floating>
    </Box>
  );
};
