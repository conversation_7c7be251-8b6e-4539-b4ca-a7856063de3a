import React, { useCallback, useEffect, useMemo, useState } from "react";

import { Doc } from "@automerge/automerge-repo";
import {
  Alert,
  AlertVariant,
  Box,
  Floating,
  FloatingPosition,
  Form,
  Modal,
  Overlay,
  Stack
} from "@oneteam/onetheme";
import { useFormContext } from "react-hook-form";
import { useOutletContext } from "react-router-dom";

import {
  DocumentErrorHelper,
  ErrorFieldKeyMap
} from "@helpers/DocumentErrorHelper.ts";
import { customNanoId } from "@helpers/customNanoIdHelper.ts";
import { modalZIndex } from "@helpers/modalHelper.ts";
import { valueUniqueness } from "@helpers/stringHelper.ts";

import { IntervalFields } from "@pages/configuration/series/IntervalFields.tsx";
import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { Dictionary, useDictionary } from "@src/hooks/useDictionary.tsx";
import { useTenantConfig } from "@src/hooks/useTenantConfig.tsx";
import { SeriesConfig, seriesConfigSchema } from "@src/types/Series.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import "./ConfigurationSeriesListModal.scss";

interface ConfigurationSeriesListModalFieldsProps {
  serverError: string;
  setHasInterval: (value: boolean) => void;
  d: Dictionary;
  seriesConfigToEditId?: string;
}

const ConfigurationSeriesListModalFields = ({
  serverError,
  setHasInterval,
  d,
  seriesConfigToEditId
}: ConfigurationSeriesListModalFieldsProps) => {
  const { docErrorHelper } = useOutletContext<{
    docErrorHelper: DocumentErrorHelper;
  }>();

  const { getValues, clearErrors, setError } = useFormContext();

  const errors: ErrorFieldKeyMap[] = useMemo(() => {
    if (!seriesConfigToEditId) {
      return [];
    }

    return (
      docErrorHelper.getDictionaryMessagesForErrors({
        prefix: `$.series.${seriesConfigToEditId}`,
        resourceName: "series"
      }) ?? []
    );
  }, [docErrorHelper, seriesConfigToEditId]);

  const clearErrorsExcept = useCallback(
    (fieldToKeep: string) => {
      const allFields = Object.keys(getValues());
      const fieldsToClear = allFields.filter(field => field !== fieldToKeep);
      clearErrors(fieldsToClear);
    },
    [clearErrors, getValues]
  );

  useEffect(() => {
    if (!seriesConfigToEditId) {
      return;
    }
    clearErrorsExcept("intervals");

    errors?.forEach(error => {
      setError(`${error.field}`, { message: d(error.message) });
    });
  }, [
    seriesConfigToEditId,
    clearErrors,
    d,
    errors,
    setError,
    clearErrorsExcept
  ]);

  const handleChange = useCallback(
    (accessor: string) => {
      clearErrors(accessor);
    },
    [clearErrors]
  );

  return (
    <Stack gap="100" width="100" contentsWidth="100">
      <Form.TextField
        label={d("ui.common.name")}
        name="name"
        required
        autoFocus
        onChange={() => handleChange("name")}
      />
      <Form.TextAreaField
        label={d("ui.common.description")}
        name="description"
        onChange={() => handleChange("description")}
      />
      <IntervalFields
        d={d}
        seriesConfigToEditId={seriesConfigToEditId}
        setHasInterval={setHasInterval}
      />
      {serverError && (
        <Alert variant={AlertVariant.DANGER}>{d(serverError)}</Alert>
      )}
    </Stack>
  );
};

interface ConfigurationSeriesListModalProps {
  onOpenChange: () => void;
  seriesConfigToEdit: SeriesConfig | null;
}

const emptySeriesConfig: Partial<SeriesConfig> = {
  name: "",
  description: "",
  intervals: { entities: {}, order: [] }
};

export const ConfigurationSeriesListModal = ({
  onOpenChange,
  seriesConfigToEdit
}: ConfigurationSeriesListModalProps) => {
  const { docChange, document } = useOutletContext<{
    docChange: DocChange;
    document: Doc<WorkspaceDocument>;
  }>();
  const [serverError, setServerError] = useState("");
  const [hasInterval, setHasInterval] = useState(false);
  const d = useDictionary();
  const tenantConfig = useTenantConfig();
  const localeString = tenantConfig?.locale;

  const isEdit = useMemo(
    () => seriesConfigToEdit !== null,
    [seriesConfigToEdit]
  );

  const saveSeriesConfigurationHelper = useCallback(
    (seriesConfig: SeriesConfig) => {
      if (isEdit) {
        if (!seriesConfigToEdit) {
          console.error(
            `[saveSeriesConfigurationHelper]: seriesConfigToEdit is null in edit mode`
          );
          return;
        }
        seriesConfig.id = seriesConfigToEdit?.id;
        docChange(d => {
          if (!d.series) {
            console.error(
              `[saveSeriesConfigurationHelper]: no series in document`
            );
            return;
          }
          if (!(seriesConfig.id in d.series)) {
            console.error(
              `[saveSeriesConfigurationHelper]: no series found with id ${seriesConfig.id}`
            );
            return;
          }
          const currentSeries = d.series[seriesConfig.id];

          d.series[seriesConfig.id] = {
            ...currentSeries,
            ...seriesConfig,
            metadata: {
              ...currentSeries.metadata,
              updatedAt: new Date().toISOString()
            }
          };
        });
      } else {
        docChange(d => {
          d.series ??= {};
          const newId = customNanoId();
          d.series[newId] = {
            ...seriesConfig,
            id: newId,
            metadata: {
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          };
        });
      }
    },
    [docChange, isEdit, seriesConfigToEdit]
  );

  const saveSeriesConfiguration = useCallback(
    (seriesConfig: SeriesConfig) => {
      try {
        saveSeriesConfigurationHelper(seriesConfig);
        onOpenChange();
      } catch (error) {
        console.error(`[saveSeriesConfiguration]: ${error}`);
        setServerError(
          `An error occurred while saving the series configuration.`
        );
      }
    },
    [saveSeriesConfigurationHelper, onOpenChange]
  );

  const existingSeriesNames: Set<string> = useMemo(() => {
    return new Set<string>(
      document.series
        ? Object.values(document.series ?? {})
            .filter(series => seriesConfigToEdit?.name !== series.name)
            .map(series => valueUniqueness(series.name, localeString))
        : []
    );
  }, [document.series, localeString, seriesConfigToEdit?.name]);

  const isDuplicate = (name: string) => {
    return !existingSeriesNames.has(valueUniqueness(name, localeString));
  };

  return (
    <Box style={{ zIndex: modalZIndex }}>
      <Overlay isOpen></Overlay>
      <Floating position={FloatingPosition.CENTER}>
        <Modal
          className="configuration-series-list-modal"
          onOpenChange={onOpenChange}
          heading={d(
            isEdit
              ? "ui.configuration.series.edit.title"
              : "ui.configuration.series.creation.title"
          )}
        >
          <Form
            schema={seriesConfigSchema(d, isDuplicate, localeString)}
            submitLabel={d("ui.common.save")}
            d={d}
            handleSubmit={saveSeriesConfiguration}
            handleCancel={onOpenChange}
            cancelLabel={d("ui.common.cancel")}
            defaultValues={seriesConfigToEdit ?? emptySeriesConfig}
            disabled={!hasInterval}
          >
            <ConfigurationSeriesListModalFields
              serverError={serverError}
              setHasInterval={setHasInterval}
              d={d}
              seriesConfigToEditId={seriesConfigToEdit?.id}
            />
            {!hasInterval && (
              <Alert variant={AlertVariant.DANGER}>
                {d("ui.configuration.series.interval.intervalRequired")}
              </Alert>
            )}
          </Form>
        </Modal>
      </Floating>
    </Box>
  );
};
