import { useCallback, useEffect, useRef, useState } from "react";

import { ToastNotificationVariant } from "@oneteam/onetheme";
import { useMutation } from "@tanstack/react-query";

import { postData } from "@helpers/postData.ts";

import { useDictionary } from "@src/hooks/useDictionary.tsx";

export enum MessageType {
  FlowRunnerUpdate = "FlowRunnerUpdate",
  Notification = "Notification"
}

export enum ActionType {
  VIEW_IN_FLOW_RUNNER = "VIEW_IN_FLOW_RUNNER",
  VIEW_EXECUTION_DOCUMENT = "VIEW_EXECUTION_DOCUMENT"
}

export interface Action {
  actionType: ActionType;
}

export interface ViewExecutionDocument extends Action {
  workspaceId: string;
  flowExecutionId: string;
}

export interface ViewInFlowRunner extends Action {
  flowExecutionId: string;
}

export interface Message {
  key: string;
  context?: Record<string, string>;
}

export interface NotificationMessage {
  heading: Message;
  description: Message;
  toastNotificationVariant: string;
  action?: Action | ViewExecutionDocument | ViewInFlowRunner;
}

export interface WebSocketMessage {
  type: MessageType;
  notificationMessage?: NotificationMessage;
}

interface UseWebPubSubProps {
  workspaceId: number;
  onMessage: (message: WebSocketMessage) => void;
}

interface WebPubSubHook {
  isConnected: boolean;
  error: string | null;
}

export function toToastNotificationVariant(
  value?: string
): ToastNotificationVariant | undefined {
  if (!value) {
    return undefined;
  }
  return (
    ToastNotificationVariant[value as keyof typeof ToastNotificationVariant] ??
    undefined
  );
}

/**
 * Hook to connect to Web PubSub
 * @param workspaceId
 * @param onMessage
 */
export const usePubSub = ({
  workspaceId,
  onMessage
}: UseWebPubSubProps): WebPubSubHook => {
  const d = useDictionary();
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [url, setUrl] = useState<string | null>(null);
  const retryCountRef = useRef(0);

  const { mutate: fetchToken } = useMutation({
    mutationFn: () =>
      postData("/pubSub/generateToken", { workspaceId: workspaceId }),
    onSuccess: data => {
      setUrl(data.url); // Pub sub url provided by back-end
    },
    onError: error => {
      console.error("Failed to get pub sub token", error);
      setError(d("errors.websocket.token"));
    }
  });

  useEffect(() => {
    fetchToken();
  }, [fetchToken]);

  const handleWebSocketError = useCallback(
    (err: unknown) => {
      console.error("Error connecting to Web PubSub:", err);
      if (retryCountRef.current < 3) {
        setTimeout(
          () => fetchToken(),
          2000 * Math.pow(2, retryCountRef.current) * Math.random()
        );
        retryCountRef.current += 1;
      } else {
        setError(d("errors.websocket.disconnected"));
      }
    },
    [d, fetchToken]
  );

  useEffect(() => {
    if (!url) {
      return;
    }

    let socket: WebSocket;
    try {
      socket = new WebSocket(url);

      socket.onopen = () => {
        setIsConnected(true);
        retryCountRef.current = 0;
        setError(null);
      };

      socket.onmessage = event => {
        const webSocketMessage: WebSocketMessage = JSON.parse(event.data);
        onMessage(webSocketMessage);
      };

      socket.onclose = () => {
        setIsConnected(false);
      };

      socket.onerror = err => {
        handleWebSocketError(err);
      };
    } catch (err) {
      handleWebSocketError(err);
    }

    return () => {
      socket?.close();
    };
  }, [fetchToken, handleWebSocketError, onMessage, url]);

  return { isConnected, error };
};
