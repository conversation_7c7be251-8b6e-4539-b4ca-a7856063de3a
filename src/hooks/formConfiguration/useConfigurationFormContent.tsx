import { use<PERSON>allback, useContext, useMemo } from "react";

import { Doc, Prop } from "@automerge/automerge-repo";
import { useOutletContext } from "react-router-dom";

import {
  createFirstQuestion,
  createQuestion,
  createSection,
  findFirstQuestion,
  getByPath,
  isSectionArray,
  lastContent
} from "@helpers/configurationFormHelper.ts";
import { customNanoId } from "@helpers/customNanoIdHelper.ts";
import { isQuestionContent } from "@helpers/forms/formHelper.ts";

import { ConfigurationFormContext } from "@pages/configuration/forms/ConfigurationFormContext";
import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import {
  autogenerateIdentifierHelper,
  visitChild
} from "@src/hooks/uniqueIdentifier/question";
import { Section } from "@src/types/FormConfiguration.ts";
import { Question, QuestionTypes } from "@src/types/Question.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { useConfigurationForm } from "./useConfigurationForm";

export const useConfigurationFormContent = ({
  level,
  content,
  path
}: {
  level?: number;
  content?: Section[] | Question[];
  path: Prop[];
}) => {
  const { updateQuestionHash, mode } = useContext(ConfigurationFormContext);

  const { docChange } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    docChange: DocChange;
  }>();

  const isContentQuestions = useMemo(
    () => !content?.length || isQuestionContent(content),
    [content]
  );

  const { configurationForm } = useConfigurationForm();
  const updateTextField = useCallback(
    (newName: string) => {
      docChange(d => {
        const textContainer = getByPath<{ name: string }>(d, path);
        if (!textContainer) {
          console.error("no element with path ", path.join("."));
          return;
        }
        textContainer.name = newName;
      });
    },
    [docChange, path]
  );

  const addFirst = useCallback(() => {
    const section = createFirstQuestion(0) as Section;
    docChange(d => {
      const content = getByPath<Section[]>(d, path);
      if (!content) {
        console.error("no element with path ", path.join("."));
        return;
      }
      content.unshift(section);
      updateQuestionHash(`${path.join(".")}.0.content.0.content.0`);
    });
  }, [docChange, path, updateQuestionHash]);

  const addQuestionOnly = useCallback(
    (type: QuestionTypes = QuestionTypes.TEXT) => {
      const question = createQuestion(type);
      question.identifier = autogenerateIdentifierHelper({
        question,
        text: question.text,
        withinContent: configurationForm?.content
      });
      const myPath = [...path, "content"];
      docChange(d => {
        const c = getByPath<Question[]>(d, myPath);
        if (!c) {
          console.error("no content with path", myPath);
          return;
        }
        c.splice?.(c.length, 0, question);
        updateQuestionHash(`${myPath.join(".")}.${c.length - 1}`);
      });
    },
    [path, docChange, updateQuestionHash, configurationForm?.content]
  );

  const addSectionAndQuestion = useCallback(
    (index: number, type: QuestionTypes = QuestionTypes.TEXT) => {
      const section = createFirstQuestion<Section>(level ?? 0, type);
      const question = findFirstQuestion(section);
      question.identifier = autogenerateIdentifierHelper({
        question,
        text: question.text,
        withinContent: configurationForm?.content
      });
      docChange(d => {
        const c = getByPath<Section[]>(d, lastContent(path));
        if (!c) {
          console.error("no content with path", lastContent(path));
          return;
        }
        c.splice?.(index, 0, section);
        updateQuestionHash(
          `${lastContent(path).join(".")}.${index}.content.0.content.0`
        );
      });
    },
    [docChange, level, path, updateQuestionHash, configurationForm?.content]
  );

  const addQuestion = useCallback(
    (index: number) =>
      (type: QuestionTypes = QuestionTypes.TEXT) => {
        if ((level ?? 0) > 1) {
          addQuestionOnly(type);
        } else {
          addSectionAndQuestion(index, type);
        }
      },
    [addQuestionOnly, addSectionAndQuestion, level]
  );

  const addSection = useCallback(
    (index: number) => () => {
      //never create empty level 1 sections
      const section = createSection(
        Math.min((level ?? 0) + 1, 2),
        "New Section",
        level === 0 ? [createSection(2, "General", [])] : []
      );

      docChange(d => {
        let sectionContent = content;
        if (content && !isSectionArray(content)) {
          // if subsection, need to get parent section
          const sectionPath = [...path];
          sectionPath.pop();
          sectionContent = getByPath<Section[]>(d, lastContent(sectionPath));
          if (!sectionContent) {
            console.error("no content with path", lastContent(sectionPath));
            return;
          }
        }
        const safeIndex = Math.min(
          Math.max(0, index),
          sectionContent?.length ?? 0
        );
        const c = getByPath<Section[]>(d, lastContent(path));
        if (!c) {
          console.error("no content with path", lastContent(path));
          return;
        }
        c.splice?.(safeIndex, 0, section);
      });
    },
    [content, docChange, level, path]
  );

  const removeItem = useCallback(
    (index: number) => {
      const myPath =
        typeof path[path.length - 1] == "number" ? [...path, "content"] : path;
      docChange(d => {
        const content = getByPath<Section[]>(d, myPath);
        if (!content) {
          console.error("no content with path", myPath);
          return;
        }
        content.splice(index, 1);
        updateQuestionHash("none");
      });
    },
    [docChange, path, updateQuestionHash]
  );

  const updateIdsAndIdentifiers = useCallback(
    (targetSections: Section[], withinContent: Section[] | Question[]) => {
      targetSections.forEach((section: Section) => {
        if (!section.content) {
          return;
        }

        section.id = customNanoId(10);
        if (isQuestionContent(section.content)) {
          (section.content as Question[]).forEach((question: Question) => {
            question.id = customNanoId(10);
            question.identifier = autogenerateIdentifierHelper({
              question,
              text: question.text,
              withinContent
            });

            visitChild(question, (parent: Question, child: Question) => {
              child.id = customNanoId(10);
              child.identifier = autogenerateIdentifierHelper({
                question: child,
                text: child.text,
                parentQuestion: parent,
                withinContent
              });
            });
          });
          return;
        }

        updateIdsAndIdentifiers(section.content as Section[], withinContent);
      });
    },
    []
  );

  const copySection = useCallback(
    (index: number) => {
      docChange(d => {
        const content = getByPath<Section[]>(d, path);
        if (!content || index < 0 || index >= content.length) {
          console.error(
            "no content with path",
            path,
            "or invalid index",
            index
          );
          return;
        }
        const sectionToCopy = content[index];
        const copiedSection = JSON.parse(JSON.stringify(sectionToCopy));
        updateIdsAndIdentifiers(
          [copiedSection],
          [...(configurationForm?.content ?? []), copiedSection]
        );
        content.splice(index + 1, 0, copiedSection);
      });
    },
    [docChange, path, updateIdsAndIdentifiers, configurationForm]
  );

  const copySubsection = useCallback(
    (index: number) => {
      const myPath = [...path, "content"];
      docChange(d => {
        const content = getByPath<Section[]>(d, myPath);
        if (!content || index < 0 || index >= content.length) {
          console.error(
            "no content with path",
            myPath,
            "or invalid index",
            index
          );
          return;
        }
        const sectionToCopy = content[index];
        const copiedSubsection = JSON.parse(JSON.stringify(sectionToCopy));
        updateIdsAndIdentifiers(
          [copiedSubsection],
          [...(configurationForm?.content ?? []), copiedSubsection]
        );
        content.splice(index + 1, 0, copiedSubsection);
      });
    },
    [docChange, path, updateIdsAndIdentifiers, configurationForm]
  );

  const copyQuestion = useCallback(
    (index: number, isMultiLevel?: boolean) => {
      const myPath = [...path, "content"];

      if (isMultiLevel) {
        myPath.pop();
      }

      docChange(d => {
        const content = getByPath<Question[]>(d, myPath);
        if (!content || index < 0 || index >= content.length) {
          console.error(
            "no content with path",
            myPath,
            "or invalid index",
            index
          );
          return;
        }
        const questionToCopy = content[index];

        const copiedQuestion = JSON.parse(JSON.stringify(questionToCopy));

        copiedQuestion.id = customNanoId(10);

        const getWithinContent = () => {
          if (isMultiLevel) {
            const tableColumnWithContent = content.slice();
            tableColumnWithContent.splice(index + 1, 0, copiedQuestion);
            return tableColumnWithContent;
          }

          return [...(configurationForm?.content ?? []), copiedQuestion];
        };

        copiedQuestion.identifier = autogenerateIdentifierHelper({
          question: copiedQuestion,
          text: copiedQuestion.text,
          withinContent: getWithinContent()
        });

        visitChild(copiedQuestion, (parent: Question, child: Question) => {
          child.id = customNanoId(10);
          child.identifier = autogenerateIdentifierHelper({
            question: child,
            text: child.text,
            parentQuestion: parent,
            withinContent: getWithinContent()
          });
        });

        content.splice(index + 1, 0, copiedQuestion);
      });
    },
    [docChange, path, configurationForm?.content]
  );

  return {
    updateTextField,
    content,
    addQuestion,
    addFirst,
    addSection,
    removeItem,
    copySection,
    copySubsection,
    copyQuestion,
    isQuestionContent: isContentQuestions,
    updateQuestionHash,
    mode
  };
};
