// import React is needed for using QueryClientProvider
import React from "react";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook, waitFor } from "@testing-library/react";
import { beforeEach } from "node:test";
import {
  afterAll,
  afterEach,
  beforeAll,
  describe,
  expect,
  test,
  vi
} from "vitest";

import { DynamicOptionTags } from "../../../types/QuestionProperties";
import { server } from "./node";
import { useFoundationCollectionOptions } from "./useFoundationCollectionOptions";

describe("FoundationCollectionProvider", () => {
  const queryClient = new QueryClient();

  beforeAll(() => {
    server.listen();
  });

  beforeEach(() => {
    vi.stubGlobal("window", {});
  });

  afterEach(() => {
    server.resetHandlers();
    vi.unstubAllGlobals(); // Clean up mocks after each test
  });

  afterAll(() => {
    server.close();
  });

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  test("should return foundation collection options for FOUNDATION_COLLECTION_ID tag", async () => {
    const { result } = renderHook(
      () =>
        useFoundationCollectionOptions(
          {
            tag: DynamicOptionTags.FOUNDATION_COLLECTION_ID,
            body: {
              parentFoundationConfigurationId: "test-id",
              parentFoundationKey: "key"
            }
          },
          1
        ),
      { wrapper }
    );

    await waitFor(() => expect(result.current?.isFetching).toBe(false));

    expect(result.current).toBeDefined();
    expect(result.current?.options?.sort()).toEqual(
      [
        { label: "ANZ", value: 123 },
        { label: "Commonwealth Bank", value: 124 },
        { label: "HSBC", value: 125 },
        { label: "Westpac", value: 126 },
        { label: "SunCorp Group", value: 127 }
      ].sort()
    );
  });

  test("should return foundation collection options for FOUNDATION_COLLECTION_KEY tag", async () => {
    const { result } = renderHook(
      () =>
        useFoundationCollectionOptions(
          {
            tag: DynamicOptionTags.FOUNDATION_COLLECTION_KEY,
            body: {
              parentFoundationConfigurationId: "test-id",
              parentFoundationKey: "key"
            }
          },
          1,
          true
        ),
      { wrapper }
    );

    await waitFor(() => expect(result.current?.isFetching).toBe(false));
    expect(result.current).toBeDefined();
    expect(result.current?.options?.sort()).toEqual(
      [
        { label: "ANZ", value: "ANZ" },
        { label: "Commonwealth Bank", value: "CBA" },
        { label: "HSBC", value: "HSBC" },
        { label: "Westpac", value: "WBC" },
        { label: "SunCorp Group", value: "SUNCORP" }
      ].sort()
    );
  });

  test("should log error for undefined dynamicOptions body", async () => {
    const { result } = renderHook(
      () =>
        useFoundationCollectionOptions(
          {
            tag: DynamicOptionTags.FOUNDATION_COLLECTION_ID
          },
          1
        ),
      { wrapper }
    );

    await waitFor(() => expect(result.current?.isFetching).toBe(false));
    expect(result.current?.options.length).toBe(0);
  });

  test("should log error for undefined dynamicOptions", async () => {
    const { result } = renderHook(
      () => useFoundationCollectionOptions(undefined, 1),
      {
        wrapper
      }
    );

    await waitFor(() => expect(result.current?.isFetching).toBe(false));
    expect(result.current?.options.length).toBe(0);
  });
});
