import { HttpResponse, delay, http } from "msw";

export const baseUrl = new URL(
  import.meta.env.VITE_OTAI_API_URL,
  window?.location?.origin ?? ""
).toString();

export const foundationCollectionMockAPIHandlersLoading = [
  http.get(`${baseUrl}/workspaces/:workspaceId/foundations`, async () => {
    await delay("infinite");
  })
];

export const ANZ_FOUNDATION_ID = 123;

/**
 * Mock API handlers for the foundation collection
 * Used in stories and unit tests
 */
export const foundationCollectionMockAPIHandlers = (delayMs: number) => [
  http.all("*", async () => {
    await delay(delayMs);
  }),
  http.get(`${baseUrl}/workspaces/:workspaceId/foundations`, () => {
    return HttpResponse.json({
      page: {
        pageNumber: 1,
        pageSize: 10,
        sort: {
          fields: []
        }
      },
      total: 5,
      items: [
        {
          id: ANZ_FOUNDATION_ID,
          name: "<PERSON><PERSON>",
          key: "ANZ",
          foundationConfigurationId: "cli",
          workspaceId: 26,
          parentId: 122,
          metadata: {
            createdAt: "2025-02-03T05:27:44.427635Z",
            updatedAt: "2025-02-03T05:27:44.427636Z"
          }
        },
        {
          id: 124,
          name: "Commonwealth Bank",
          key: "CBA",
          foundationConfigurationId: "cli",
          workspaceId: 26,
          parentId: 122,
          metadata: {
            createdAt: "2025-02-03T05:27:44.503106Z",
            updatedAt: "2025-02-03T05:27:44.503107Z"
          }
        },
        {
          id: 125,
          name: "HSBC",
          key: "HSBC",
          foundationConfigurationId: "cli",
          workspaceId: 26,
          parentId: 122,
          metadata: {
            createdAt: "2025-02-03T05:27:44.561857Z",
            updatedAt: "2025-02-03T05:27:44.561858Z"
          }
        },
        {
          id: 126,
          name: "Westpac",
          key: "WBC",
          foundationConfigurationId: "cli",
          workspaceId: 26,
          parentId: 122,
          metadata: {
            createdAt: "2025-02-03T05:27:44.611615Z",
            updatedAt: "2025-02-03T05:27:44.611616Z"
          }
        },
        {
          id: 127,
          name: "SunCorp Group",
          key: "SUNCORP",
          foundationConfigurationId: "cli",
          workspaceId: 26,
          parentId: 122,
          metadata: {
            createdAt: "2025-02-03T05:27:44.664027Z",
            updatedAt: "2025-02-03T05:27:44.664027Z"
          }
        }
      ]
    });
  })
];
