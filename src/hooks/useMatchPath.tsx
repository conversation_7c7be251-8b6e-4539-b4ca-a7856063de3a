import { useCallback } from "react";

import { useLocation, useParams } from "react-router-dom";

function removeTrailingSlashes(path: string): string {
  if (path === "/") {
    return path;
  }
  return path.endsWith("/") ? path.slice(0, -1) : path;
}

export const useMatchPath = () => {
  const { pathname } = useLocation();
  const params = useParams();

  const matchPath: (path: string, properties?: { exact?: boolean }) => boolean =
    useCallback(
      (path, properties) => {
        let updatedPath = path.replace("/*", "");
        Object.entries(params)?.forEach(([key, value]) => {
          updatedPath = updatedPath.replace(`:${key}`, String(value));
        });
        if (properties?.exact) {
          const pathnameWithoutSlash = removeTrailingSlashes(pathname);
          const updatedPathWithoutSlash = removeTrailingSlashes(updatedPath);
          return pathnameWithoutSlash === updatedPathWithoutSlash;
        }

        return pathname.includes(updatedPath);
      },
      [pathname, params]
    );

  return { matchPath };
};
