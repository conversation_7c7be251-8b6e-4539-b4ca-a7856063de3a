import { FoundationConfiguration } from "@src/types/FoundationConfiguration";

export function useCheckFoundationDuplicateIdentifier() {
  //should take the identifier and check if it is already used in the foundations
  //if it is, then take that identifier and append _1, _2, etc. until it finds a unique identifier
  const autogenerateIdentifier = (
    thisFoundation: FoundationConfiguration,
    foundations: FoundationConfiguration[]
  ) => {
    //remove special characters and spaces, then camelCase the name
    const newIdentifierPrefix = thisFoundation.name
      ?.replace(/[^a-zA-Z0-9 ]/g, "")
      .split(" ")
      .reduce((acc, word, index) => {
        return (
          acc +
          (index === 0 ? word.charAt(0) : word.charAt(0).toUpperCase()) +
          word.slice(1)
        );
      }, "");

    const used = new Set<string>();
    for (const foundation of foundations) {
      if (foundation.id !== thisFoundation.id) {
        used.add(foundation.identifier);
      }
    }

    let newIdentifier = newIdentifierPrefix!;
    let i = 1;
    while (used.has(newIdentifier)) {
      newIdentifier = `${newIdentifierPrefix}_${i}`;
      i++;
    }

    return newIdentifier;
  };

  return { autogenerateIdentifier };
}
