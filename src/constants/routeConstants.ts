export const appSettingsRoutePrefix = "/app";
const appSettingsRoute = `${appSettingsRoutePrefix}`;

export const workspaceRoutePrefix = "/workspace";
const workspaceRoute = `${workspaceRoutePrefix}/:workspaceKey`;
const contextRoute = {
  home: workspaceRoute,
  configuration: `${workspaceRoute}/configuration`,
  collection: `${workspaceRoute}`,
  settings: `${workspaceRoute}/settings`,
  appSettings: `${appSettingsRoute}`
};

export const routeConstants = {
  auth: "/auth",
  logout: "/logout",
  creationWorkspace: "/workspaces/new",
  configuration: contextRoute.configuration,
  configurationFormList: `${contextRoute.configuration}/forms`,
  configurationForm: `${contextRoute.configuration}/forms/k/:configurationFormKey`,
  configurationSeries: `${contextRoute.configuration}/forms/series`,
  configurationFlowList: `${contextRoute.configuration}/flows`,
  configurationFlow: `${contextRoute.configuration}/flows/:configurationFlowId`,
  configurationFoundation: `${contextRoute.configuration}/foundations`,
  configurationLabels: `${contextRoute.configuration}/labels`,
  flowRunner: `${contextRoute.collection}/flows`,
  helpConfiguration: `${contextRoute.configuration}/help`,
  collectionHome: `${contextRoute.collection}/browse`,
  home: contextRoute.home,
  settings: contextRoute.settings,
  settingsPermissions: `${contextRoute.settings}/permissions`,

  appSettings: `${contextRoute.appSettings}`,
  appSettingsApiKeys: `${contextRoute.appSettings}/api-keys`
};
