export const commonErrors = {
  length: "errors.common.length",
  duplicate: "errors.common.duplicate",
  alphanumeric: "errors.common.alphanumeric",
  alphabetSnakeCase: "errors.common.alphabetSnakeCase",
  minLength: "errors.common.minLength",
  maxLength: "errors.common.maxLength",
  required: "errors.common.required",
  greaterThan: "errors.common.greaterThan",
  lessThan: "errors.common.lessThan",
  greaterThanOrEqual: "errors.common.greaterThanOrEqual",
  lessThanOrEqual: "errors.common.lessThanOrEqual",
  unexpected: "errors.common.unexpected"
};

// Key
export const MIN_KEY_LENGTH = 2;
export const MAX_KEY_LENGTH = 20;

// Name
export const MIN_NAME_LENGTH = 2;
export const MAX_NAME_LENGTH = 100;
