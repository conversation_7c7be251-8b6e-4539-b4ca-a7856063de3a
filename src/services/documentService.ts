import { DocumentId } from "@automerge/automerge-repo";
import { StatusCodes } from "http-status-codes";
import { nanoid } from "nanoid";
import {
  AlertOperation,
  FormAlertBody,
  FormAlertBodyItem,
  FormAnnotationDocument
} from "src/documentTypes.ts";
import { fetchDocPath } from "src/helpers/fetchHelper.ts";
import { Logger } from "src/helpers/logger.ts";
import { appServer } from "src/index.ts";
import eh from "src/middleware/exceptionHandler.ts";
import {
  AlertAnnotation,
  Annotation,
  AnnotationResolvedStatus
} from "src/types/Annotation.ts";
import { TableCellLocation } from "src/types/AnnotationLocation.ts";

const log = Logger("otai:server:formsAlertSet");

const isMatchingAlertLocation = (
  locationA: AlertAnnotation["location"],
  locationB: AlertAnnotation["location"]
) => {
  return (
    locationA.variant === "question" &&
    locationB.variant === "question" &&
    locationA.questionId === locationB.questionId &&
    (locationA as TableCellLocation).rowId ===
      (locationB as TableCellLocation).rowId &&
    (locationA as TableCellLocation).columnId ===
      (locationB as TableCellLocation).columnId
  );
};

const handleFormAnswerAlertForDoc = (
  path: string[],
  doc: FormAnnotationDocument,
  alert: FormAlertBodyItem["alerts"]
) => {
  doc.annotations ??= {};

  const formAnnotations = fetchDocPath(
    doc.annotations,
    path
  ) as FormAnnotationDocument["annotations"];

  const alertLocation: Annotation["location"] = {
    variant: "question",
    ...alert.location
  };

  if (
    alert.operation === AlertOperation.REMOVE ||
    alert.operation === AlertOperation.RESOLVE
  ) {
    const removeAlertIds = Object.entries(formAnnotations)
      .filter(([, item]) => {
        if (
          item.variant !== "alert" ||
          !isMatchingAlertLocation(item.location, alertLocation)
        ) {
          return false;
        }

        const matchesMessage = !alert.message || item.message === alert.message;
        const matchesGroupIdentifier =
          !alert.groupIdentifier ||
          alert.groupIdentifier === item.groupIdentifier;

        return matchesMessage && matchesGroupIdentifier;
      })
      .map(([annotationId]) => annotationId);

    removeAlertIds.forEach(id => {
      if (alert.operation === AlertOperation.REMOVE) {
        delete formAnnotations[id];
      } else {
        formAnnotations[id].resolved = {
          status: AnnotationResolvedStatus.RESOLVED,
          actor: {
            type: "flow",
            // TODO: this should be the flow / user who resolved the alert
            flowExecutionId: "0"
          }
        };
      }
    });
  } else {
    if (!alert.location.questionId || !alert.message || !alert.type) {
      throw new Error(
        "Alert is missing some required fields: questionId, message, type"
      );
    }
    const existingAlert = Object.values(formAnnotations).find(
      annotation =>
        annotation.variant === "alert" &&
        annotation.resolved?.status !== AnnotationResolvedStatus.RESOLVED &&
        isMatchingAlertLocation(annotation.location, alertLocation) &&
        annotation.groupIdentifier === alert.groupIdentifier &&
        annotation.message === alert.message &&
        annotation.type === alert.type
    );

    if (!existingAlert) {
      const id = nanoid(10);
      const dateAsISOString = new Date().toISOString();
      const newAlert: AlertAnnotation = {
        id,
        variant: "alert",
        type: alert.type,
        message: alert.message,
        location: alertLocation,
        createdAt: dateAsISOString,
        updatedAt: dateAsISOString,
        createdBy: {
          type: "flow",
          // TODO: this should be the flow / user who created the alert
          flowExecutionId: "0"
        },
        updatedBy: {
          type: "flow",
          // TODO: this should be the flow / user who created the alert
          flowExecutionId: "0"
        }
      };
      if (alert.groupIdentifier) {
        newAlert.groupIdentifier = alert.groupIdentifier;
      }
      formAnnotations[id] = newAlert;
    }
  }
};

export const formsAlertSet = eh(async (req, res) => {
  try {
    const annotationDocumentId = req.params.annotationDocumentId as DocumentId;
    const formAlerts = req.body as FormAlertBody; // have to validate this else server can crash
    // (eg: if it's not an array), fix for loop?
    log.info("[document/alert POST]", annotationDocumentId, formAlerts);

    const docHandle =
      await appServer.repo.find<FormAnnotationDocument>(annotationDocumentId);

    docHandle.change(doc => {
      try {
        formAlerts.forEach(formAlert => {
          handleFormAnswerAlertForDoc(formAlert.path, doc, formAlert.alerts);
        });

        res.json({
          documentId: docHandle.documentId,
          message: "Alert processed successfully"
        });
      } catch (e) {
        log.error("[document/alert POST] error", e);
        res
          .status(StatusCodes.BAD_REQUEST)
          .json({ message: "Invalid request" });
      }
    });
  } catch (e) {
    log.error("[document/alert POST] error", e);
    res
      .status(StatusCodes.INTERNAL_SERVER_ERROR)
      .json({ message: "Unknown error" });
  }
});
