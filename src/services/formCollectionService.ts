import { DocumentId } from "@automerge/automerge-repo";
import { StatusCodes } from "http-status-codes";
import _ from "lodash";
import {
  FileAnswer,
  FormAnswer,
  FormAnswerBody,
  FormAnswerDocument,
  FormAnswerForEvent,
  SetFormAnswer
} from "src/documentTypes.ts";
import { broadcastSystemPresence } from "src/helpers/docHelper.ts";
import {
  answerPath,
  checkIfJsonAnswer,
  fetchAnswerValue,
  fetchDocPath,
  getJwtToken
} from "src/helpers/fetchHelper.ts";
import { Logger } from "src/helpers/logger.ts";
import { postData } from "src/helpers/postData.ts";
import { appServer } from "src/index.ts";
import eh from "src/middleware/exceptionHandler.ts";
import { EventKey } from "src/types/eventType.ts";
import { QuestionDepthType, QuestionTypes } from "src/types/questionType.ts";

const log = Logger("otai:server:formsAnswersSet");

const willAnswerDelete = (previousAnswer?: unknown) => {
  const previous = previousAnswer as FormAnswer | undefined;

  // when `forDelete` is true, it means we are checking if the answer will be deleted
  //  so check if the previous answer exists (and we ignore the incoming answer)
  return previous !== undefined;
};

// returns true if the answer will change
const willAnswerChange = (
  previousAnswer?: unknown,
  incomingAnswer?: FormAnswer
) => {
  const previous = previousAnswer as FormAnswer | undefined;

  let isDifferent = !_.isEqual(previous?.value, incomingAnswer?.value);

  if (previous?.columns && incomingAnswer?.columns) {
    isDifferent = Object.entries(incomingAnswer.columns).some(
      ([key, value]) => {
        if (
          !previous.columns?.[key] ||
          Object.keys(previous.columns[key] || {}).length === 0
        ) {
          return true;
        }
        if (!_.isEqual(previous.columns[key]?.value, value.value)) {
          return true;
        }
        return false;
      }
    );
  }

  log.info(
    "[document/answer PUT]",
    "willAnswerChange",
    "previous",
    previous?.value,
    "incoming",
    incomingAnswer?.value,
    "isDifferent",
    isDifferent
  );
  return isDifferent;
};

const willFileAnswerChange = (
  previousAnswer?: unknown,
  incomingAnswer?: FormAnswer
) => {
  const previous = previousAnswer as FormAnswer | undefined;
  const incoming = incomingAnswer?.value[0];

  if (!previous || incoming?.name === "undefined") {
    // first file added or file deleted
    return true;
  }

  if (previous?.value && incoming) {
    const previousFiles = previous.value as FileAnswer[];
    return !previousFiles.some(file => file.path === incoming.path);
  }

  return false;
};

const handleFilesAnswer = (
  docAnswer: Record<string, unknown>,
  answerValue: FormAnswer
) => {
  if (answerValue.value[0]?.name === "undefined") {
    const formAnswer = docAnswer[answerValue.questionId] as FormAnswer;
    const value = formAnswer?.value;

    if (Array.isArray(value)) {
      const index = value.findIndex(
        (f: FileAnswer) => f.path === answerValue.value[0]?.path
      );

      if (index !== -1) {
        value.splice(index, 1);
      }
    }
    return;
  }

  if (!docAnswer[answerValue.questionId]) {
    docAnswer[answerValue.questionId] = answerValue;
  } else {
    (
      (docAnswer[answerValue.questionId] as FormAnswer)?.value as FileAnswer[]
    ).push(answerValue.value[0]);
  }
};

const willTableRowsDecrease = (
  previousAnswer?: unknown,
  incomingAnswer?: FormAnswer
) => {
  const previous = previousAnswer as FormAnswer | undefined;
  const incoming = incomingAnswer;
  if (previous?.value && incoming?.value) {
    return previous.value.order.length > incoming.value.order.length;
  }
  return false;
};

const removeRow = ({
  doc,
  path,
  rowId
}: {
  doc: FormAnswerDocument;
  path: string[];
  rowId: string;
}) => {
  const pathToTable = answerPath(path).getPathToQuestion();
  const pathToRow = answerPath(path).getPathToRow();
  _.unset(doc, pathToRow);

  // Remove row from ordered map
  const pathToTableRowOrder = pathToTable.concat(["value", "order"]);
  const currentRowOrder = _.get(doc, pathToTableRowOrder) ?? [];
  _.set(
    doc,
    pathToTableRowOrder,
    currentRowOrder.filter((id: string) => id !== rowId)
  );
};

// Keeping for future use
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const removeRowIfEmpty = ({
  doc,
  path,
  rowId
}: {
  doc: FormAnswerDocument;
  path: string[];
  rowId: string;
}) => {
  const pathToRow = answerPath(path).getPathToRow();
  const row = _.get(doc, pathToRow);
  if (
    !row.columns ||
    Object.keys(row.columns).length === 0 ||
    !Object.keys(row.columns).some(key => row.columns[key].value)
  ) {
    removeRow({ doc, path, rowId });
  }
};

const handlePrepareTableAnswer = (path: string[], doc: FormAnswerDocument) => {
  // if the table answer doesn't already exist, create a new one
  const questionPath = answerPath(path).getPathToQuestion();
  const question = _.get(doc, questionPath);
  if (!question) {
    _.set(doc, questionPath, {
      questionId: path[1],
      type: QuestionTypes.TABLE,
      value: {
        order: [],
        entities: {}
      }
    } as FormAnswer);
  }

  // if row is specified - create row if it doesn't exist
  const rowId = answerPath(path).getRowId();
  if (!rowId) {
    return;
  }

  const pathToTable = answerPath(path).getPathToQuestion();
  const pathToRow = answerPath(path).getPathToRow();
  const existingRow = _.get(doc, pathToRow);

  if (!existingRow) {
    const newRow = {
      id: rowId,
      columns: {} as Record<string, FormAnswer>
    };
    _.set(doc, pathToRow, newRow);
    const currentRowOrder =
      _.get(doc, pathToTable.concat(["value", "order"])) ?? [];
    currentRowOrder.push(rowId);
  }
};

// returns truthy if the answer has changed from its previous
const handleFormAnswerForDoc = (
  path: string[],
  doc: FormAnswerDocument,
  answer: FormAnswer
) => {
  if (answer.type === QuestionTypes.TABLE) {
    handlePrepareTableAnswer(path, doc);
  }
  const docAnswer = fetchDocPath(doc.answers, path);
  const isJson = checkIfJsonAnswer(doc.answers, path);
  const answerValue =
    answer.type === QuestionTypes.TABLE || answer.type === QuestionTypes.LIST
      ? fetchAnswerValue(answer, path.slice(2))
      : answer;
  if (
    (answerValue.value == undefined || answerValue.value?.length === 0) &&
    answerValue.questionId !== undefined
  ) {
    if (!docAnswer) {
      return false;
    }

    // if value is undefined or empty array(for file question), delete the answer
    if (isJson) {
      const jsonAnswer = docAnswer[answerValue.questionId] as FormAnswer;
      const result = willAnswerDelete(jsonAnswer);
      delete jsonAnswer?.value;
      return result;
    } else if (typeof docAnswer === "object") {
      const result = willAnswerDelete(docAnswer[answerValue.questionId]);
      delete docAnswer[answerValue.questionId];
      return result;
    }

    return false;
  }

  if (!docAnswer || typeof docAnswer !== "object") {
    return false;
  }

  if (answerValue.questionId !== undefined) {
    if (answerValue.type === QuestionTypes.FILES) {
      const fileChanged = willFileAnswerChange(
        docAnswer[answerValue.questionId],
        answerValue
      );
      if (fileChanged) {
        handleFilesAnswer(docAnswer, answerValue);
      }
      return fileChanged;
    }

    if (answerValue.type === QuestionTypes.TABLE) {
      const rowsDecreased = willTableRowsDecrease(
        docAnswer[answerValue.questionId],
        answerValue
      );
      if (rowsDecreased) {
        docAnswer[answerValue.questionId] = answerValue;
        return rowsDecreased;
      }
    }

    const result = willAnswerChange(
      docAnswer[answerValue.questionId],
      answerValue
    );
    docAnswer[answerValue.questionId] = answerValue;
    return result;
  }

  if (answerValue.id !== undefined) {
    // handle set table question
    if (answerValue.columns && Object.keys(answerValue.columns).length === 0) {
      const result = willAnswerDelete(docAnswer[answerValue.id]);
      delete docAnswer[answerValue.id];
      const tableValue = fetchDocPath(doc.answers, path.slice(0, 4));
      if (
        tableValue &&
        typeof tableValue === "object" &&
        Array.isArray(tableValue.order)
      ) {
        const newOrder = tableValue.order.filter(
          item => item !== answerValue.id
        );
        tableValue.order = newOrder;
      }
      return result;
    }

    // for add new row in table
    const isExistingAnswer = docAnswer[answerValue.id] != null;
    const result =
      willAnswerChange(docAnswer[answerValue.id], answerValue) ||
      !isExistingAnswer;
    docAnswer[answerValue.id] = answerValue;
    const tableValue = fetchDocPath(doc.answers, path.slice(0, 4));
    if (
      !isExistingAnswer &&
      tableValue &&
      typeof tableValue === "object" &&
      Array.isArray(tableValue.order)
    ) {
      tableValue.order.push(answerValue.id);
    }
    return result;
  }

  return false;
};

const createColumnEvent = (
  formAnswer: FormAnswer,
  documentId: DocumentId,
  formId: string
): FormAnswerForEvent => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { type: _, ...restFormAnswer } = formAnswer;

  return {
    eventType: EventKey.UPDATE_COLLECTION_FORM_ANSWER,
    documentId: documentId.toString(),
    formId: formId,
    formAnswer: restFormAnswer
  };
};

const handleTableColumnsAnswerForEvent = (
  formAnswerForEvents: FormAnswerForEvent[],
  formAnswer: SetFormAnswer,
  documentId: DocumentId,
  formId: string,
  addedQuestion: Set<string>
) => {
  switch (answerPath(formAnswer.path).getQuestionDepth()) {
    case QuestionDepthType.QUESTION: {
      Object.keys(formAnswer.formAnswer.value?.entities as FormAnswer)?.forEach(
        rowId => {
          const path = formAnswer.path.concat(["value", "entities", rowId]);

          handleTableColumnsAnswerForEvent(
            formAnswerForEvents,
            { path, formAnswer: formAnswer.formAnswer },
            documentId,
            formId,
            addedQuestion
          );
        }
      );
      break;
    }

    case QuestionDepthType.ROW: {
      const columns: { [id: string]: FormAnswer } =
        fetchAnswerValue(formAnswer.formAnswer, formAnswer.path.slice(2))
          .columns ?? {};

      Object.values(columns).forEach(column => {
        const path = formAnswer.path.concat(["columns", column.questionId]);

        handleTableColumnsAnswerForEvent(
          formAnswerForEvents,
          { path, formAnswer: formAnswer.formAnswer },
          documentId,
          formId,
          addedQuestion
        );
      });
      break;
    }

    case QuestionDepthType.COLUMN: {
      const answer =
        formAnswer.formAnswer.type === QuestionTypes.TABLE ||
        formAnswer.formAnswer.type === QuestionTypes.LIST
          ? fetchAnswerValue(formAnswer.formAnswer, formAnswer.path.slice(2))
          : formAnswer.formAnswer;

      handleAllTypeAnswerForEvent(
        formAnswerForEvents,
        { path: formAnswer.path, formAnswer: answer },
        documentId,
        formId,
        addedQuestion,
        true
      );
      break;
    }
  }
};

const handleAllTypeAnswerForEvent = (
  formAnswerForEvents: FormAnswerForEvent[],
  formAnswer: SetFormAnswer,
  documentId: DocumentId,
  formId: string,
  addedQuestion: Set<string>,
  isTableColumn: boolean = false
) => {
  if (isTableColumn) {
    formAnswer.formAnswer.value = {
      [answerPath(formAnswer.path).getRowId()]: formAnswer.formAnswer.value
    };
  }

  if (
    !isTableColumn &&
    formAnswer.formAnswer.type !== QuestionTypes.TABLE &&
    formAnswer.formAnswer.type !== QuestionTypes.LIST &&
    answerPath(formAnswer.path).getQuestionDepth() > QuestionDepthType.QUESTION
  ) {
    formAnswer.formAnswer = {
      ...formAnswer.formAnswer,
      questionId: answerPath(formAnswer.path).getQuestionId(),
      columnId: answerPath(formAnswer.path).getColumnId()
    };
  }

  if (!addedQuestion.has(formAnswer.formAnswer.questionId)) {
    formAnswerForEvents.push(
      createColumnEvent(formAnswer.formAnswer, documentId, formId)
    );
    addedQuestion.add(formAnswer.formAnswer.questionId);
  } else if (isTableColumn) {
    const foundEvent = formAnswerForEvents.find(
      event => event.formAnswer.questionId === formAnswer.formAnswer.questionId
    );
    if (foundEvent) {
      foundEvent.formAnswer.value = {
        ...foundEvent.formAnswer.value,
        ...formAnswer.formAnswer.value
      };
    }
  }
};

const prepareFormAnswersForEvent = (
  formAnswers: FormAnswerBody,
  documentId: DocumentId,
  formId: string
): FormAnswerForEvent[] => {
  const addedQuestion: Set<string> = new Set();
  const formAnswerForEvents: FormAnswerForEvent[] = [];

  formAnswers.forEach((formAnswer: SetFormAnswer) => {
    if (
      formAnswer.formAnswer.type === QuestionTypes.TABLE ||
      answerPath(formAnswer.path).getQuestionDepth() >
        QuestionDepthType.QUESTION
    ) {
      handleTableColumnsAnswerForEvent(
        formAnswerForEvents,
        formAnswer,
        documentId,
        formId,
        addedQuestion
      );
    }

    handleAllTypeAnswerForEvent(
      formAnswerForEvents,
      formAnswer,
      documentId,
      formId,
      addedQuestion
    );
  });

  return formAnswerForEvents;
};

export const formsAnswersSet = eh(async (req, res) => {
  try {
    const answerDocumentId = req.params.answerDocumentId as DocumentId;
    const formAnswers = req.body as FormAnswerBody; // have to validate this else server can crash
    // (eg: if it's not an array), fix for loop?
    log.info("[document/answer PUT]", answerDocumentId, formAnswers);

    const docHandle =
      await appServer.repo.find<FormAnswerDocument>(answerDocumentId);
    log.info("[document/answer PUT] document ready", answerDocumentId);

    // if there is no session, it means its a system user
    // session only tells us if the person is logged in or not, so can't rely on it yet for actual user info
    if (!req.session) {
      broadcastSystemPresence(docHandle);
      log.info(
        "[document/answer PUT] broadcasted system presence",
        answerDocumentId
      );
    }

    docHandle.change(async doc => {
      try {
        const changedFormAnswers = formAnswers.filter(formAnswer => {
          const didAnswerChange = handleFormAnswerForDoc(
            formAnswer.path,
            doc,
            formAnswer.formAnswer
          );
          return didAnswerChange;
        });
        log.info(
          "[document/answer PUT] number of changed answers",
          changedFormAnswers?.length
        );

        const token = getJwtToken();
        const formId = doc.id;
        const formAnswersForEvent = prepareFormAnswersForEvent(
          changedFormAnswers,
          answerDocumentId,
          formId
        );

        // fire and forget
        postData("/document/events", formAnswersForEvent, token).catch(e => {
          log.info("[document/answer PUT] error in postData", e); // post event error will not stop the process
        });

        log.info(
          "[document/answer PUT] Answers processed successfully",
          answerDocumentId
        );
        res.json({
          documentId: docHandle.documentId,
          message: "Answers processed successfully"
        });
      } catch (e) {
        log.error("[document/answer PUT] error in docHandle", e);
        res
          .status(StatusCodes.BAD_REQUEST)
          .json({ message: "Invalid request" });
      }
    });
  } catch (e) {
    log.error("[document/answer PUT] error", e);
    res
      .status(StatusCodes.INTERNAL_SERVER_ERROR)
      .json({ message: "Unknown error" });
  }
});
