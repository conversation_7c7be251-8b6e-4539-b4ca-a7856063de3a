import React, {
  ForwardedRef,
  MutableRefObject,
  ReactNode,
  forwardRef,
  useEffect,
  useMemo,
  useRef,
  useState
} from "react";

import { Box, FermionProps, Inline } from "../../fermions/index.ts";
import {
  composeComponentClassNames,
  returnStringIfTrue
} from "../../helpers/componentHelpers.ts";
import { FloatingWithParent } from "../FloatingWithParent/FloatingWithParent.tsx";
import { FloatingWithParentPosition } from "../FloatingWithParent/FloatingWithParentTypes.ts";
import { Text } from "../Text/Text.tsx";
import "./Tooltip.scss";

const TooltipContent = forwardRef(
  (
    {
      content,
      style
    }: {
      content: string | ReactNode;
      style: React.CSSProperties;
    },
    ref: ForwardedRef<HTMLDivElement>
  ) => {
    return (
      <Inline
        className={composeComponentClassNames("tooltip-content", {}, [
          returnStringIfTrue(
            typeof content !== "string",
            "tooltip-content--custom"
          )
        ])}
        style={style}
        ref={ref}
      >
        {typeof content === "string" ? <Text>{content}</Text> : content}
      </Inline>
    );
  }
);

export interface TooltipProps {
  content: string | ReactNode;
  delay?: number;
  classNames?: string[];
  width?: FermionProps["width"];
  style?: React.CSSProperties;
  position?: `${FloatingWithParentPosition}`;
  skipGap?: boolean;
}

export const Tooltip = forwardRef<
  HTMLDivElement,
  React.PropsWithChildren<TooltipProps>
>(
  (
    {
      content,
      delay = 0,
      width,
      classNames = [],
      style: customStyle = {},
      position,
      skipGap = false,
      children
    },
    ref
  ) => {
    const isMounted = useRef(false);
    const parentRef = useRef<HTMLDivElement | null>(null);
    const makingVisible = useRef(false);

    const [isVisible, setIsVisible] = useState(false);

    const elementProps = useMemo(
      () =>
        ({
          onMouseEnter: () => {
            if (delay) {
              makingVisible.current = true;
              setTimeout(() => {
                if (isMounted.current && makingVisible.current) {
                  setIsVisible(true);
                }
              }, delay);
              return;
            }
            setIsVisible(true);
          },
          onMouseLeave: () => {
            makingVisible.current = false;
            setIsVisible(false);
          },
          position: "relative"
        }) as const,
      [delay]
    );

    useEffect(() => {
      isMounted.current = true;
      return () => {
        isMounted.current = false;
      };
    }, []);

    return (
      <Box
        classNames={["tooltip", ...classNames]}
        {...elementProps}
        width={width}
        style={customStyle}
        ref={ref || parentRef}
      >
        {children}
        {isVisible && (
          <FloatingWithParent
            parentRef={(ref as MutableRefObject<HTMLDivElement>) || parentRef}
            position={position}
            zIndex={50}
            skipGap={skipGap}
          >
            <TooltipContent
              content={content}
              style={{ opacity: isVisible ? 1 : 0 }}
            />
          </FloatingWithParent>
        )}
      </Box>
    );
  }
);
