import React, {
  InputHTMLAttributes,
  ReactNode,
  useCallback,
  useEffect,
  useMemo
} from "react";

import { debounce } from "lodash";

import { Box, Inline, Stack } from "../../fermions/index.ts";
import { CommonInputProps } from "../../helpers/componentHelpers.ts";
import { useWarningCheckForInputs } from "../../hooks/useWarningCheckForInputs.tsx";
import { ColorText } from "../../tokens/IonsInterface.ts";
import { CloseIconButton, IconButton } from "../Button/index.ts";
import { Icon } from "../Icon/Icon.tsx";
import { IconSize } from "../Icon/IconTypes.ts";
import { Label } from "../Label/Label.tsx";
import { LabelTextPosition } from "../Label/LabelTypes.ts";
import "./NumberField.scss";
import { NumberType } from "./NumberFieldTypes.ts";

export type NumberAsString = string;

export interface NumberFieldProps extends CommonInputProps {
  // Controlled
  value?: NumberAsString;
  onChange?: (value: NumberAsString) => void;
  valueSequenceId?: number; // use this to force re-render

  // Uncontrolled
  defaultValue?: NumberAsString;
  format?: (value: NumberAsString) => NumberAsString;
  allowedDecimalPlaces?: number;
  extraDecimalPlacesForPercentage?: number;

  type?: `${NumberType}`;
  min?: InputHTMLAttributes<HTMLInputElement>["min"];
  max?: InputHTMLAttributes<HTMLInputElement>["max"];
  step?: InputHTMLAttributes<HTMLInputElement>["step"];
  iconOverride?: ReactNode;
  allowClear?: boolean;

  withDebounce?: boolean;
  debounceTime?: number;

  onlyTriggerChangeWhenBlur?: boolean;
  controlFocus?: boolean;

  // For display only - not value format
  displayFormatter?: (value: NumberAsString) => string;
}

const isAcceptedInput = (value: string): boolean => {
  if (value === "-" || value === "+") {
    return true;
  }
  const numberRegex = new RegExp(/^[+-]?((\d+(\.\d*)?)|(\.\d+))$/);
  return value === "" || numberRegex.test(value);
};

const removeLeadingZeros = (wholeNumber: NumberAsString): NumberAsString => {
  return wholeNumber.replace(/^0+/, "") || "0";
};

const convertPercentageToNumber = (
  value: NumberAsString,
  extraDecimalPlacesForPercentage: number,
  allowedDecimalPlaces?: number
) => {
  if (value === "") {
    return value;
  }

  const num = Number(value);
  if (isNaN(num)) {
    return "";
  }

  const currentFractionDigits: number =
    num.toString().split(".")[1]?.length ?? 0;

  return (num / 100.0)
    .toFixed(
      Math.min(
        100,
        Math.max(
          0,
          allowedDecimalPlaces ??
            currentFractionDigits + extraDecimalPlacesForPercentage
        )
      )
    )
    .toString();
};

// Does a transformation - e.g. Removes 0's at the start of the number
const transformValueForExternal = ({
  value,
  isPercentage,
  extraDecimalPlacesForPercentage,
  allowedDecimalPlaces
}: {
  value: NumberAsString;
  isPercentage: boolean;
  extraDecimalPlacesForPercentage: number;
  allowedDecimalPlaces?: number;
}): NumberAsString => {
  if (value === "") {
    return value;
  }

  if (isPercentage) {
    value = convertPercentageToNumber(
      value,
      extraDecimalPlacesForPercentage,
      allowedDecimalPlaces
    );
  }

  const [whole, decimal] = value.split(".");
  const transformedWhole = removeLeadingZeros(whole);

  if (isNaN(Number(transformedWhole))) {
    return "";
  }

  return transformedWhole + (decimal ? `.${decimal}` : "");
};

const extractValueAsString = ({
  value,
  type,
  extraDecimalPlacesForPercentage,
  allowedDecimalPlaces
}: {
  value?: string;
  type?: string;
  extraDecimalPlacesForPercentage?: number;
  allowedDecimalPlaces?: number;
}): NumberAsString => {
  if (value === undefined || value === null || value === "") {
    return "";
  }
  if (type === NumberType.PERCENTAGE && extraDecimalPlacesForPercentage) {
    const currentDecimalPlaces = value.split(".")[1]?.length ?? 0;
    const decimalPlaces = allowedDecimalPlaces
      ? Math.max(allowedDecimalPlaces, currentDecimalPlaces)
      : currentDecimalPlaces;

    return (Number(value) * 100).toFixed(
      Math.min(
        100,
        Math.max(0, decimalPlaces - extraDecimalPlacesForPercentage)
      )
    );
  }
  return value;
};

const getSteppedValue = (
  current: NumberAsString,
  step: InputHTMLAttributes<HTMLInputElement>["step"],
  direction: "up" | "down" = "up",
  {
    min,
    max
  }: {
    min?: InputHTMLAttributes<HTMLInputElement>["min"];
    max?: InputHTMLAttributes<HTMLInputElement>["max"];
  }
) => {
  let value = Number(current ?? "0");
  if (direction === "up") {
    value =
      max !== undefined
        ? Math.min(+max, value + Number(step))
        : value + Number(step);

    if (value < Number(current)) {
      value = Number(current);
    }
  } else {
    value =
      min !== undefined
        ? Math.max(+min, value - Number(step))
        : value - Number(step);
  }
  const stepDecimalPlaces = step?.toString().split(".")[1]?.length ?? 0;
  return value.toFixed(stepDecimalPlaces);
};

export const NumberField = ({
  name,
  id,
  label = "",
  value,
  onChange,
  format,
  allowedDecimalPlaces,
  extraDecimalPlacesForPercentage = 2,
  defaultValue,
  placeholder,
  description,
  tooltip,
  required,
  hidden,
  disabled,
  autoFocus = false,
  error,
  width = "fit",
  type = NumberType.NUMBER,
  min = undefined,
  max = undefined,
  step = 1,
  iconOverride,
  allowClear = !required,
  className,
  valueSequenceId,
  withDebounce,
  debounceTime = 300,
  onlyTriggerChangeWhenBlur = false,
  controlFocus,
  onClick,
  onFocus,
  onBlur,
  onKeyDown,
  onKeyUp,
  onPaste,
  displayFormatter
}: NumberFieldProps) => {
  useWarningCheckForInputs({ value, defaultValue, onChange });
  const inputRef = React.useRef<HTMLInputElement>(null);
  const [tempValue, setTempValue] = React.useState<NumberAsString>(
    extractValueAsString({
      value: value ?? defaultValue,
      type,
      allowedDecimalPlaces,
      extraDecimalPlacesForPercentage
    })
  );

  useEffect(() => {
    if (controlFocus === true && document.activeElement !== inputRef.current) {
      inputRef.current?.focus();
    } else if (
      controlFocus === false &&
      document.activeElement === inputRef.current
    ) {
      inputRef.current?.blur();
    }
  }, [controlFocus]);

  const formatDisplayValue = useCallback(
    (rawValue: NumberAsString) => {
      if (displayFormatter) {
        return displayFormatter(rawValue);
      }
      switch (type) {
        case NumberType.ACCOUNTING: {
          const rawValueDecimalSplit = rawValue.split(".");
          return (
            Number(rawValueDecimalSplit[0]).toLocaleString() +
            (rawValueDecimalSplit?.length > 1 && rawValueDecimalSplit[1]
              ? `.${rawValueDecimalSplit[1]}`
              : "")
          );
        }
        case NumberType.PERCENTAGE:
        case NumberType.NUMBER:
        default:
          return rawValue;
      }
    },
    [displayFormatter, type]
  );

  useEffect(() => {
    // The valueSequenceId is used to force re-render the component because there may be cases where in a controlled component
    //  the value get updated to the same value, but differs from tempValue, thus causing a stale value to be displayed
    setTempValue(
      extractValueAsString({
        value: value ?? defaultValue,
        type,
        allowedDecimalPlaces,
        extraDecimalPlacesForPercentage
      })
    );
  }, [
    value,
    valueSequenceId,
    type,
    defaultValue,
    allowedDecimalPlaces,
    extraDecimalPlacesForPercentage
  ]);

  const debounceCalculate = useMemo(
    () => (onChange ? debounce(onChange, debounceTime) : () => {}),
    [debounceTime, onChange]
  );

  const isValidPercentage = useCallback((value: NumberAsString) => {
    return !isNaN(Number(value));
  }, []);

  const isPercentage = type === NumberType.PERCENTAGE;

  const handleChangeInternally = useCallback(
    (
      updatedValue: string,
      changeWithDebounce = withDebounce,
      notifyExternalChange = !onlyTriggerChangeWhenBlur,
      shouldFormat = false
    ) => {
      if (inputRef?.current && !isAcceptedInput(updatedValue)) {
        inputRef.current.value = tempValue;
        return;
      }

      if (
        inputRef?.current &&
        isPercentage &&
        updatedValue &&
        !isValidPercentage(updatedValue)
      ) {
        inputRef.current.value = tempValue;
        return;
      }

      const extractedValue = extractValueAsString({
        value: updatedValue,
        type: undefined,
        allowedDecimalPlaces,
        extraDecimalPlacesForPercentage
      });

      setTempValue(extractedValue);

      if (!onChange || !notifyExternalChange) {
        return;
      }

      const tempValueForExternal = transformValueForExternal({
        value: extractedValue,
        isPercentage,
        extraDecimalPlacesForPercentage,
        allowedDecimalPlaces
      });

      const valueForExternal =
        shouldFormat && format
          ? format(tempValueForExternal)
          : tempValueForExternal;

      if (
        valueForExternal === value ||
        (valueForExternal == "" && value == undefined)
      ) {
        return;
      }
      if (changeWithDebounce) {
        debounceCalculate(valueForExternal);
      } else {
        onChange(valueForExternal);
      }
    },
    [
      withDebounce,
      onlyTriggerChangeWhenBlur,
      isPercentage,
      isValidPercentage,
      allowedDecimalPlaces,
      onChange,
      format,
      value,
      tempValue,
      debounceCalculate,
      extraDecimalPlacesForPercentage
    ]
  );

  const handleBlur = useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      onBlur?.(e);
      handleChangeInternally(e.target.value, false, true, true);
      setTempValue(
        extractValueAsString({
          value: value ?? defaultValue,
          type,
          allowedDecimalPlaces,
          extraDecimalPlacesForPercentage
        })
      );
    },
    [
      allowedDecimalPlaces,
      defaultValue,
      extraDecimalPlacesForPercentage,
      handleChangeInternally,
      onBlur,
      type,
      value
    ]
  );

  const iconName = useMemo(() => {
    switch (type) {
      case NumberType.ACCOUNTING:
        return "attach_money";
      case NumberType.PERCENTAGE:
        return "percent";
      case NumberType.NUMBER:
      default:
        return "tag";
    }
  }, [type]);

  const handleIncrementUp = useCallback(() => {
    const incremented = getSteppedValue(tempValue, step, "up", {
      min,
      max
    });
    handleChangeInternally?.(incremented, false, true);
  }, [handleChangeInternally, max, min, step, tempValue]);

  const handleIncrementDown = useCallback(() => {
    const decremented = getSteppedValue(tempValue, step, "down", {
      min,
      max
    });
    handleChangeInternally?.(decremented, false, true);
  }, [handleChangeInternally, max, min, step, tempValue]);

  return (
    <Box classNames={["number-field", className]} width={width}>
      <Label
        htmlFor={id}
        label={label}
        required={required}
        hidden={hidden}
        description={description}
        tooltip={tooltip}
        disabled={disabled}
        textPosition={LabelTextPosition.TOP}
        error={error}
      >
        <Inline className="number-field__value" position="relative">
          {tempValue !== undefined && tempValue !== "" && (
            <Box
              position="absolute"
              className="number-field__display-value"
              onClick={() => {
                if (!disabled) {
                  inputRef.current?.focus();
                }
              }}
              onMouseDown={() => {
                if (!disabled) {
                  inputRef.current?.focus();
                }
              }}
              style={{
                maxWidth: inputRef.current?.offsetWidth
              }}
            >
              {tempValue ? formatDisplayValue(tempValue) : ""}
            </Box>
          )}
          <input
            ref={inputRef}
            className="number-field__input"
            type="text"
            inputMode="decimal"
            pattern="[+-]?([0-9]*\\.?[0-9]+|[0-9]+\\.?[0-9]*)"
            placeholder={placeholder}
            onChange={e => {
              if (inputRef?.current && !isAcceptedInput(e.target.value)) {
                inputRef.current.value = tempValue;
              } else {
                handleChangeInternally?.(e.target.value);
              }
            }}
            onBlur={handleBlur}
            onClick={onClick}
            onPaste={e => {
              onPaste?.(e);
              e.preventDefault();
              const rawValue =
                (tempValue ?? "") + e.clipboardData.getData("text/plain");
              const pastedValue = parseFloat(rawValue.replaceAll(",", ""));
              if (
                !isNaN(pastedValue) &&
                isAcceptedInput(pastedValue.toFixed())
              ) {
                handleChangeInternally?.(pastedValue.toFixed());
              }
            }}
            onKeyDown={e => {
              if (onKeyDown) {
                onKeyDown(e);
              } else if (e.key === "ArrowUp") {
                handleIncrementUp();
              } else if (e.key === "ArrowDown") {
                handleIncrementDown();
              }
              if (e.key === "Enter") {
                e.currentTarget.blur();
              }
            }}
            onFocus={onFocus}
            onKeyUp={onKeyUp}
            name={name}
            id={id}
            disabled={disabled}
            autoFocus={autoFocus}
            value={tempValue}
          />
          <Inline className="number-field__right" gap="000" alignment="right">
            {allowClear &&
              !disabled &&
              (tempValue === undefined ? !!defaultValue : !!tempValue) && (
                <CloseIconButton
                  className="number-field__clear"
                  onClick={e => {
                    handleChangeInternally("", false, true);
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  skipFocus
                  color={ColorText.PRIMARY}
                  size={IconSize.SMALL}
                  disabled={disabled}
                />
              )}
            {!disabled && (
              <Box
                className="number-field__stepper"
                padding="100"
                style={{
                  height: "16px",
                  width: "24px"
                }}
                alignment="center"
              >
                <Stack position="absolute">
                  <IconButton
                    className="number-field__stepper__icon"
                    name="keyboard_arrow_up"
                    onClick={handleIncrementUp}
                    skipFocus
                  />
                  <IconButton
                    className="number-field__stepper__icon"
                    name="keyboard_arrow_down"
                    onClick={handleIncrementDown}
                    skipFocus
                  />
                </Stack>
              </Box>
            )}
          </Inline>
          {iconOverride ?? (
            <Icon
              name={iconName}
              size={IconSize.SMALL}
              color={disabled ? ColorText.DISABLED : ColorText.TERTIARY}
            />
          )}
        </Inline>
      </Label>
    </Box>
  );
};
