import React, {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  FocusEvent<PERSON><PERSON>ler,
  KeyboardEventHandler,
  use<PERSON><PERSON>back,
  useEffect,
  useMemo
} from "react";

import {
  Box,
  Icon,
  IconType,
  Inline,
  getClassNames,
  returnStringIfTrue
} from "@oneteam/onetheme";

import { getVariableTypeDetails } from "@helpers/flows/flowVariableHelpers";

import { useConfigurationFlowContext } from "@pages/configuration/flows/ConfigurationFlowContext";

import { variableTypeIcons } from "@src/types/FlowConfiguration/Variables";

import { HandleFocusOnPartFocusLocation } from "../../VariableFieldTypes";
import "../VariableFieldPart.scss";
import { VariablePartTooltip } from "./VariablePartTooltip";

export const VariableFieldPartVariable = ({
  id,
  value: rawValue,
  // variable,
  onChange,
  onBlur,
  onKeyDown,
  handleFocusOnThisPart,
  onFocus
}: {
  id: string;
  value: string;
  // variable?: VariableTypeDefinition;
  onChange?: ChangeEventHandler<HTMLTextAreaElement>;
  onBlur?: FocusEventHandler;
  onKeyDown?: KeyboardEventHandler<HTMLTextAreaElement>;
  handleFocusOnThisPart?: (
    focusLocation: HandleFocusOnPartFocusLocation
  ) => void;
  onFocus?: FocusEventHandler<HTMLTextAreaElement>;
}) => {
  // Show the user the user-friendly name of the variable
  const { variablesByName, variablesByPath, settings } =
    useConfigurationFlowContext();

  const variable = useMemo(
    () => variablesByName?.[rawValue] ?? variablesByPath?.[rawValue],
    [rawValue, variablesByName, variablesByPath]
  );

  const isValid = useMemo(() => {
    if (variable) {
      return true;
    }

    // Check if there is a next closed variable in the path
    const variableParts = rawValue.split(".");
    if (variableParts.length <= 1) {
      return false;
    }
    // Work backwards removing the last part until we find a variable
    let foundVariable;
    for (let i = variableParts.length - 1; i > 0 && !foundVariable; i--) {
      const variablePath = variableParts.slice(0, i).join(".");
      foundVariable =
        variablesByName?.[variablePath] ?? variablesByPath?.[variablePath];
    }

    // If the next closest variable is JSON and the json has no items (a.k.a no no structure)
    if (
      foundVariable &&
      foundVariable?.__type === "json" &&
      !foundVariable?.__configuration?.properties?.items
    ) {
      return true;
    }

    return false;
  }, [rawValue, variable, variablesByName, variablesByPath]);

  const value = useMemo(() => {
    if (settings?.debugMode || !variable?.__name) {
      return rawValue;
    }
    return String(variable.__name);
  }, [rawValue, settings?.debugMode, variable?.__name]);

  const variableIconRef = React.useRef<HTMLDivElement>(null);
  const inputRef = React.useRef<HTMLTextAreaElement>(null);
  const [isOpen, setIsOpen] = React.useState(false);

  const { resolvedType } = useMemo(
    () =>
      variable
        ? getVariableTypeDetails({ type: variable?.__type })
        : {
            resolvedType: "unknown"
          },
    [variable]
  );

  const updateHeight = useCallback(() => {
    if (inputRef.current) {
      inputRef.current.style.overflow = "auto";
      inputRef.current.style.height = "inherit";
      const newHeight = `${inputRef.current?.scrollHeight}px`;
      inputRef.current.style.height = newHeight;
      inputRef.current.style.maxHeight = newHeight;
      inputRef.current.style.overflow = "hidden";
    }
  }, []);

  useEffect(() => {
    updateHeight();
  }, [updateHeight, value]);

  const variableIcon: IconType = useMemo(() => {
    const unknownVariableIcon: IconType = {
      name: "question_mark",
      fillStyle: "filled"
    };
    if (variable && variable?.__type) {
      return variableTypeIcons[resolvedType] ?? unknownVariableIcon;
    }
    return unknownVariableIcon;
  }, [variable, resolvedType]);

  return (
    <Box
      className={getClassNames([
        "variable-field-part",
        "variable-field-part-variable",
        returnStringIfTrue(!isValid, "variable-field-part-variable--invalid")
      ])}
      alignment="left"
    >
      <Inline alignment="left">
        <Box
          ref={variableIconRef}
          onClick={() => {
            handleFocusOnThisPart?.("all");
          }}
          onMouseEnter={() => {
            setIsOpen(true);
          }}
          onMouseLeave={() => {
            setIsOpen(false);
          }}
          style={{
            cursor: "default"
          }}
        >
          <Icon size="s" {...variableIcon} />
        </Box>
        <textarea
          id={id}
          ref={inputRef}
          rows={1}
          value={value}
          onChange={
            onChange
              ? (e: ChangeEvent<HTMLTextAreaElement>) => {
                  // Take out characters that are not allowed in variable names
                  const value = e.target.value.replace(/[^a-zA-Z0-9_.*-]/g, "");
                  e.target.value = value;
                  onChange?.(e);
                }
              : undefined
          }
          disabled={!onChange}
          onBlur={onBlur}
          onDrag={e => {
            e.stopPropagation();
            e.preventDefault();
          }}
          onKeyDown={onKeyDown}
          onFocus={onFocus}
        />
        {isOpen && (
          <VariablePartTooltip
            variable={variable}
            variableIconRef={variableIconRef}
            resolvedType={resolvedType}
          />
        )}
      </Inline>
    </Box>
  );
};
