import React from "react";

import {
  FloatingWithParent,
  Icon,
  Inline,
  Pill,
  Stack,
  Text
} from "@oneteam/onetheme";

import { useDictionary } from "@src/hooks/useDictionary";
import { variableTypeIcons } from "@src/types/FlowConfiguration/Variables";

import "./VariablePartTooltip.scss";

export const VariablePartTooltip = ({
  variable,
  resolvedType,
  variableIconRef
}: {
  variable?: {
    __type?: string;
    __description?: string;
  };
  resolvedType: string;
  variableIconRef: React.RefObject<HTMLDivElement>;
}) => {
  const d = useDictionary();
  return (
    <FloatingWithParent
      parentRef={variableIconRef}
      position="top-left"
      allowOutsideWindow={false}
      className="variable-part-tooltip"
    >
      {variable?.__type ? (
        <Stack
          className="variable-part-tooltip__details"
          width="100"
          gap="025"
          alignment="left"
          padding="050"
          onClick={e => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          <Pill
            label={variable?.__type?.split(".")[0]}
            icon="custom"
            customIcon={{
              ...(variableTypeIcons[resolvedType] ?? {
                name: "question_mark"
              })
            }}
          />
          {variable?.__description && (
            <Text size="s" color="text-secondary" weight="regular">
              {variable?.__description}
            </Text>
          )}
        </Stack>
      ) : (
        <Inline
          width="100"
          gap="025"
          alignment="left"
          padding="050"
          onClick={e => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          <Icon size="s" name="question_mark" fillStyle="filled" />
          <Inline width="100" gap="025" alignment="left">
            {d("ui.configuration.flows.variables.unknown")}
          </Inline>
        </Inline>
      )}
    </FloatingWithParent>
  );
};
