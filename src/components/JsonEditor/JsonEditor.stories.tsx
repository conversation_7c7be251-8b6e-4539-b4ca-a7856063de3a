import React from "react";

import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { JsonEditor, JsonEditorProps } from "./JsonEditor.tsx";

const meta: Meta<typeof JsonEditor> = {
  component: JsonEditor,
  title: "components/JsonEditor"
};

export default meta;

type Story = StoryObj<typeof JsonEditor>;

const defaultArgs: Story["args"] = {
  localJSONContent:
    JSON.stringify(
      {
        hello: "world",
        foo: "bar"
      },
      null,
      2
    ) ?? "",
  height: "200px",
  disabled: false
};

const exampleAPISchema = {
  type: "object",
  properties: {
    method: {
      type: "string",
      enum: ["POST", "PUT"]
    },
    headers: {
      type: "object",
      properties: {
        "Content-Type": {
          type: "string",
          const: "application/json"
        },
        Authorization: {
          type: "string",
          pattern: "^Bearer .+"
        }
      },
      required: ["Content-Type", "Authorization"]
    },
    body: {
      type: "object",
      properties: {
        firstName: { type: "string" },
        lastName: { type: "string" },
        email: { type: "string", format: "email" },
        startDate: { type: "string", format: "date" }
      },
      required: ["firstName", "lastName", "email"]
    }
  },
  required: ["method", "headers", "body"]
};
const exampleAPIContent = JSON.stringify(
  {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer your-api-token"
    },
    body: {
      firstName: "John",
      lastName: "Doe",
      email: "<EMAIL>",
      startDate: "2021-01-01"
    }
  },
  null,
  2
);
const exampleErrorContent = JSON.stringify(
  {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: false
    },
    body: {
      firstName: 123,
      email: "<EMAIL>",
      startDate: "2021-01-0122"
    }
  },
  null,
  2
);

const defaultRender = (args: Story["args"]) => (
  <JsonEditor {...(args as JsonEditorProps)} />
);

export const Default: Story = {
  args: { ...defaultArgs },
  render: defaultRender
};

export const ReadOnly: Story = {
  args: { ...defaultArgs, disabled: true },
  render: defaultRender
};

export const WithSchema: Story = {
  args: {
    ...defaultArgs,
    schema: exampleAPISchema,
    localJSONContent: exampleAPIContent,
    height: "500px"
  },
  render: defaultRender
};

export const WithErrors: Story = {
  args: {
    ...defaultArgs,
    schema: exampleAPISchema,
    localJSONContent: exampleErrorContent,
    height: "500px"
  },
  render: defaultRender
};
