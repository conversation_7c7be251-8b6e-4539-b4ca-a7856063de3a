import React, { useCallback, useEffect } from "react";

import MonacoEditor, { useMonaco } from "@monaco-editor/react";
import { editor } from "monaco-editor";

import { Box } from "../../fermions";

export interface JsonEditorProps {
  schema?: object;
  localJSONContent: string;
  onChange?: (value: string | undefined) => void;
  onBlur?: (value: string | undefined) => void;
  height?: string;
  disabled?: boolean;
}

export const JsonEditor = ({
  schema,
  localJSONContent,
  onChange,
  onBlur,
  height,
  disabled
}: JsonEditorProps) => {
  const monaco = useMonaco();

  useEffect(() => {
    monaco?.languages.json.jsonDefaults.setDiagnosticsOptions({
      validate: true,
      schemas: [
        {
          uri: "placeHolder",
          fileMatch: ["*"],
          schema
        }
      ]
    });
  }, [monaco, schema]);

  const handleEditorDidMount = useCallback(
    (editor: editor.IStandaloneCodeEditor) => {
      editor.onDidBlurEditorText(() => {
        onBlur?.(editor.getValue());
      });
    },
    [onBlur]
  );

  return (
    <Box className="json-editor" alignment="left" width="100" height="100">
      <MonacoEditor
        className="editor"
        defaultLanguage="json"
        defaultValue={localJSONContent}
        value={localJSONContent}
        onChange={onChange}
        onMount={handleEditorDidMount}
        height={height}
        options={{
          automaticLayout: true,
          readOnly: disabled,
          scrollBeyondLastLine: false,
          stickyScroll: {
            enabled: true
          },
          scrollbar: {
            vertical: "auto"
          }
        }}
      />
    </Box>
  );
};
