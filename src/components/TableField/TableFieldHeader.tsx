import React, { useCallback, useEffect } from "react";

import { Box, Inline } from "../../fermions";
import { getClassNames, returnStringIfTrue } from "../../helpers";
import { IconButton } from "../Button";
// import { Label, LabelTextPosition } from "../Label";
import { DraggableLine } from "../Resizable";
import { useTableFieldContext } from "./TableFieldContext";
import { TableFieldOnChangeColumnWidth } from "./TableFieldTypes";

export const TableFieldHeader = ({
  onChangeStickyColumnId,
  onChangeColumnWidth,
  columnStyling
}: {
  onChangeStickyColumnId?: (columnId: string) => void;
  onChangeColumnWidth?: TableFieldOnChangeColumnWidth;
  columnStyling: React.CSSProperties[];
}) => {
  const { tableFieldId, columns, stickyColumnIndex } = useTableFieldContext();

  const [draggingColumnIndex, setDraggingColumnIndex] = React.useState<
    number | undefined
  >(undefined);
  const [tempColumnWidth, setTempColumnWidth] = React.useState<
    number | undefined
  >(undefined);

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (draggingColumnIndex === undefined) {
        return;
      }

      let newWidth;
      const headerColumnCell = document.getElementById(
        `${tableFieldId}-header-column-${draggingColumnIndex}`
      );
      const headerColumnCellBounds = headerColumnCell?.getBoundingClientRect();

      if (headerColumnCellBounds) {
        newWidth = Math.max(e.x - headerColumnCellBounds?.left, 64);
      }

      if (newWidth) {
        setTempColumnWidth(newWidth);
      }
      e.preventDefault();
    },
    [draggingColumnIndex, tableFieldId]
  );

  useEffect(() => {
    if (!onChangeColumnWidth) {
      return;
    }
    const handleMouseUp = () => {
      if (tempColumnWidth) {
        onChangeColumnWidth(
          columns[draggingColumnIndex ?? 0].id,
          tempColumnWidth
        );
      }
      setDraggingColumnIndex(undefined);
    };

    if (draggingColumnIndex !== undefined) {
      document.body?.addEventListener("mousemove", handleMouseMove);
      document.body?.addEventListener("mouseup", handleMouseUp);
    }
    return () => {
      document.body?.removeEventListener("mousemove", handleMouseMove);
      document.body?.removeEventListener("mouseup", handleMouseUp);
    };
  }, [
    columns,
    draggingColumnIndex,
    handleMouseMove,
    onChangeColumnWidth,
    tempColumnWidth
  ]);

  return (
    <tr id={`${tableFieldId}-header-row`} className="table-field__header-row">
      <th
        id={`${tableFieldId}-header-counter-column`}
        className={getClassNames([
          "table-field__header",
          "table-field__cell",
          "table-field__cell--sticky",
          "table-field__cell--counter"
        ])}
      >
        <Box className="table-field__cell__styler" />
      </th>
      {columns
        .filter(column => !column.properties?.hidden)
        .map((column, columnIndex) => {
          const isSticky =
            stickyColumnIndex !== undefined && columnIndex <= stickyColumnIndex;
          return (
            <th
              id={`${tableFieldId}-header-column-${columnIndex}`}
              key={column.id}
              className={getClassNames([
                "table-field__cell",
                "table-field__header",
                returnStringIfTrue(isSticky, "table-field__cell--sticky")
              ])}
              style={{
                ...(columnStyling[columnIndex] ?? {}),
                ...(draggingColumnIndex === columnIndex
                  ? {
                      maxWidth: tempColumnWidth,
                      minWidth: tempColumnWidth,
                      width: tempColumnWidth
                    }
                  : {})
              }}
            >
              <Inline spaceBetween className="table-field__cell__content">
                <Inline
                  className="table-field__header__content"
                  alignment="left"
                >
                  <Box width="100">
                    {column.highlightComponents}
                    {/* <Label
                      htmlFor={column.id}
                      label={column.label}
                      required={column.properties?.required}
                      disabled={disabled || column.properties?.disabled}
                      textPosition={LabelTextPosition.TOP}
                      description={column?.description}
                      tooltip={column?.tooltip}
                    /> */}
                  </Box>
                  {onChangeStickyColumnId && (
                    <IconButton
                      className="table-field__header__sticky-button"
                      name="splitscreen_left"
                      onClick={() => {
                        if (stickyColumnIndex === columnIndex) {
                          onChangeStickyColumnId("");
                        } else {
                          onChangeStickyColumnId(column.id);
                        }
                      }}
                      color="text-secondary"
                    />
                  )}
                </Inline>
                <DraggableLine
                  className="table-field__header__drag-line"
                  isDragging={draggingColumnIndex === columnIndex}
                  setIsDragging={(isDragging: boolean) => {
                    if (isDragging) {
                      setDraggingColumnIndex(columnIndex);
                    } else {
                      setDraggingColumnIndex(undefined);
                    }
                  }}
                  disabled={
                    !onChangeColumnWidth || columnIndex === columns.length - 1
                  }
                />
              </Inline>
              <Box className="table-field__cell__styler">
                {columnIndex === stickyColumnIndex && (
                  <Box className="table-field__cell__styler__shadow" />
                )}
              </Box>
            </th>
          );
        })}
    </tr>
  );
};
