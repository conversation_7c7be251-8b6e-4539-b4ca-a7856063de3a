@use "../../tokens/tokens" as *;

@mixin input_value_classes() {
  & .text-field__value,
  & .number-field__value,
  & .select,
  & .text-area-field__value,
  & .date-picker__value,
  & .date-range-picker__value {
    @content;
  }
}

$border-size: 1px;

.table-field {
  max-width: 100%;
  @include input_wrapper();

  & thead {
    position: sticky;
    top: calc($border-size * -1);
    z-index: 5;
  }

  &__table {
    width: 100%;
    border-collapse: collapse;
  }
  &__header {
    &__content {
      padding: var(--components-table-cell-padding-vertical, 6px)
        var(--components-table-cell-padding-horizontal, 6px);
    }

    &__sticky-button {
      opacity: 0;
    }
    &:hover {
      & .table-field__header__sticky-button {
        opacity: 1;
      }
    }
  }

  &__scroll-container {
    padding-bottom: 15px;
  }

  .table-field-row-actions {
    & .table-field-row-actions__add-row {
      display: none;
    }
    &__add-row-indicator {
      &:hover {
        cursor: pointer;
        & .table-field-row-actions__add-row {
          display: flex;
        }
      }
    }
    &__kebab-menu-container {
      @include shadow_raised_slightest();
      opacity: 0;
      visibility: hidden;
      margin-right: -4px;
      padding: 1px 4px;
      background-color: var(--color-border);
      border-radius: 2px;
      z-index: 5;
    }
  }

  &__row {
    &--contains-highlighted-cells {
      position: relative;
      z-index: 4;
    }

    &:hover,
    &:has(.table-field-row-actions__kebab-menu--open),
    &:focus-within {
      & .table-field-row-actions__kebab-menu-container {
        opacity: 1;
        visibility: visible;
      }
      & .table-field__cell--counter {
        z-index: 5;
      }
    }
  }

  &__cell {
    position: relative;

    &:first-child {
      & .table-field__cell__styler {
        border-left: $border-size solid var(--color-border);
      }
    }

    &--sticky {
      position: sticky !important;
      left: calc($border-size * -1);
      z-index: 1;

      &-right {
        @extend .table-field__cell--sticky;
        right: calc($border-size * -1);
      }
    }

    &--counter {
      min-width: 28px;
      width: 28px;
      padding: var(--components-table-cell-padding-vertical, 6px)
        var(--components-table-cell-padding-horizontal, 6px);
      z-index: 2;
    }

    &__highlighted-dot {
      $dot-size: var(--spacing-100, 8px);
      position: absolute;
      bottom: calc($dot-size / 2 * -1);
      right: calc($dot-size / 2 * -1);
      z-index: 1;
      width: $dot-size;
      height: $dot-size;
      border-radius: 50%;
      background-color: var(--color-focus);
    }
  }

  &__row-actions {
    position: sticky;
    left: 0;
    right: 100%;
    gap: var(--spacing-025);
    z-index: 10;
  }

  &:has(.table-field__row--contains-highlighted-cells) {
    .table-field-row-actions__add-row-indicator {
      visibility: hidden;
    }
  }

  &:hover,
  &:focus-within {
    & .table-field__clear {
      opacity: 1;
    }
  }

  & .select-input {
    height: 100%;
  }

  @include input_value_classes() {
    height: 100%;
    background-color: transparent;

    & input {
      width: 100%;
      min-width: var(--spacing-1000);
      line-break: anywhere;
    }
    & textarea {
      line-break: anywhere;
    }
  }

  &--variant-default {
    @include input_value_classes() {
      border-radius: 0;
      border: none;
      outline: none !important;
      background-color: transparent !important; // even when disabled, it should be transparent
    }

    .select-properties-options__item {
      @include input_value_classes() {
        border: $border-size solid $color-border;
        border-radius: var(--components-inputs-border-radius, 8px);
        outline: none !important;
        background-color: transparent !important;
      }
    }

    & .table-field__header {
      background-color: var(--color-surface-primary);

      & .table-field__cell__styler {
        border-top: $border-size solid var(--color-border);
      }
    }

    & .table-field__data {
      background-color: var(--color-surface-secondary);

      &:has(input),
      &:has(select),
      &:has(textarea) {
        &:focus-within {
          z-index: 2;
          > .table-field__cell__content {
            outline-offset: -2px;
            outline: 2px solid var(--color-focus);
          }
        }
      }
    }

    & .table-field__cell {
      padding: 0;

      &__styler {
        min-height: 36px;
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        border-right: $border-size solid var(--color-border);
        border-bottom: $border-size solid var(--color-border);
        pointer-events: none;
        &__shadow {
          $shadow-size: 6px;
          position: absolute;
          height: 100%;
          right: calc($shadow-size * -1);
          width: $shadow-size;
          background-image: linear-gradient(
            to left,
            rgba(255, 255, 255, 0),
            rgb(150, 150, 150)
          );
          opacity: 0.3;
        }
      }
      &__content {
        padding: 2px;
        margin-right: $border-size;
        margin-bottom: $border-size;
        & .table-field__header__drag-line {
          margin-right: -2px;
          margin-top: -1px;
          position: absolute;
          right: 2px;
          top: 0;
          height: 100%;
          & .divider {
            height: calc(100% + 2px);
          }
        }
      }
      &--sticky {
        z-index: 3;
        background-color: var(--color-surface-primary);
      }
      &--highlighted {
        background-color: #eaf4ff;
      }
    }
  }

  &--variant-list {
    & .table-field__cell {
      &--sticky {
        background-color: var(--color-surface-secondary);
      }
      &--highlighted {
        @include input_value_classes() {
          background-color: #eaf4ff;
        }
      }
    }

    & .table-field__data {
      padding: var(--spacing-025);
    }

    & .table-field__header {
      padding: 0 var(--spacing-025);
    }
  }
}

.table-field__row--contains-highlighted-cells {
  & .table-field__cell--not-highlighted {
    & + .table-field__cell--highlighted {
      & .table-field__cell__styler {
        border-left: $border-size solid var(--color-focus);
      }
    }
  }

  & .table-field__cell--highlighted {
    &:has(+ .table-field__cell--not-highlighted) {
      & .table-field__cell__styler {
        border-right: $border-size solid var(--color-focus);
      }
    }
  }
}

.table-field__row {
  &--contains-highlighted-cells {
    &:first-child {
      & .table-field__cell--highlighted {
        & .table-field__cell__styler {
          border-top: $border-size solid var(--color-focus);
        }
      }
    }
    &:last-child,
    &:has(+ .table-field__row--does-not-contain-highlighted-cells) {
      & .table-field__cell--highlighted {
        & .table-field__cell__styler {
          border-bottom: $border-size solid var(--color-focus);
        }
      }
    }
  }

  &--does-not-contain-highlighted-cells {
    & + .table-field__row--contains-highlighted-cells {
      & .table-field__cell--highlighted {
        & .table-field__cell__styler {
          border-top: $border-size solid var(--color-focus) !important;
        }
      }
    }
  }
}
