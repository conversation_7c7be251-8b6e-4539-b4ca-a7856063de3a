import React, { useEffect, useState } from "react";

import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import _, { range } from "lodash";

import { TableField } from "./TableField.tsx";
import {
  TableFieldCellValue,
  TableFieldColumnId,
  TableFieldColumnWidths,
  TableFieldOnChangeColumnWidth,
  TableFieldRemoveRow,
  TableFieldReorderRow,
  TableFieldSetColumn,
  TableFieldSetRow,
  TableFieldSetTable,
  TableFieldValue
} from "./TableFieldTypes.ts";

const meta: Meta<typeof TableField> = {
  component: TableField,
  title: "components/TableField"
};

export default meta;

type Story = StoryObj<typeof TableField>;

const defaultArgs: Story["args"] = {
  label: "Table Story",
  required: true,
  columns: [
    {
      label: "Name",
      name: "name",
      id: "name",
      type: "text",
      properties: {
        required: true
      }
    },
    {
      label: "Age",
      name: "age",
      id: "age",
      type: "number"
    },
    {
      label: "Moved house?",
      name: "movedHouse",
      id: "movedHouse",
      type: "boolean"
    },
    {
      label: "Address",
      name: "address",
      id: "address",
      type: "text"
    },
    {
      label: "Phone",
      name: "phone",
      id: "phone",
      type: "text"
    },
    {
      label: "Option",
      name: "option",
      id: "option",
      type: "select",
      properties: {
        options: [
          { label: "Option 1", value: "option1" },
          { label: "Option 2", value: "option2" }
        ]
      }
    },
    {
      label: "Multi-Option",
      name: "multiOption",
      id: "multiOption",
      type: "multiSelect",
      properties: {
        options: [
          { label: "Option 1", value: "option1" },
          { label: "Option 2", value: "option2" }
        ]
      }
    },
    {
      label: "Date",
      name: "date",
      id: "date",
      type: "date"
    },
    {
      label: "Date range",
      name: "dateRange",
      id: "dateRange",
      type: "dateRange"
    }
  ],
  value: [
    {
      name: "John Doe",
      movedHouse: true
    },
    {
      name: "Jane Doe",
      age: "25",
      movedHouse: false
    }
  ]
};

const defaultRender = (args: Story["args"]) => {
  const [stickyColumnId, setStickyColumnId] = useState<string | undefined>(
    args?.stickyColumnId
  );
  const [value, setValue] = useState<TableFieldValue>(args?.value ?? []);
  const [columnWidths, setColumnWidths] = useState<TableFieldColumnWidths>({});

  const setTable: TableFieldSetTable = (valueToSet, options) => {
    if (options.fromRowIndex === undefined) {
      setValue(valueToSet);
      return;
    }

    const fromRowIndex = options.fromRowIndex ?? 0;
    // Starting ad the row index and column index, update the values
    const maxRows = Math.max(value.length, valueToSet.length + fromRowIndex);
    const nextValue = _.cloneDeep(value);

    range(0, maxRows).forEach(rowIndex => {
      const existingRow = nextValue[rowIndex];
      const columnValue = valueToSet[rowIndex - fromRowIndex] ?? {};

      if (existingRow) {
        nextValue[rowIndex] = { ...existingRow, ...columnValue };
      } else {
        nextValue.splice(rowIndex, 0, columnValue);
      }
    });
    setValue(nextValue);
  };

  const setRow: TableFieldSetRow = (
    rowValue = {},
    rowIndex = value.length,
    options = {}
  ) => {
    setValue(current => {
      const nextValue = _.cloneDeep(current);
      const toDelete =
        !options?.newRow || rowIndex === (value.length ?? 0) ? 1 : 0;
      const existingRow = nextValue[rowIndex];

      if (options?.keepOtherRowValues && existingRow && toDelete === 1) {
        nextValue[rowIndex] = { ...existingRow, ...rowValue };
      } else {
        nextValue.splice(rowIndex, toDelete, rowValue);
      }
      return nextValue;
    });
  };

  const removeRow: TableFieldRemoveRow = rowIndex => {
    setValue(current => {
      const nextValue = _.cloneDeep(current);
      nextValue.splice(rowIndex, 1);
      return nextValue;
    });
  };

  const reorderRow: TableFieldReorderRow = (rowIndexToMove, toRowIndex) => {
    setValue(current => {
      const nextValue = _.cloneDeep(current);
      const rowToMove = nextValue[rowIndexToMove];
      nextValue.splice(rowIndexToMove, 1);
      nextValue.splice(toRowIndex, 0, rowToMove);
      return nextValue;
    });
  };

  const setColumn: TableFieldSetColumn = (columnValues, columnId, options) => {
    setValue(current => {
      const nextValue = _.cloneDeep(current);
      const maxRows = Math.max(
        nextValue.length,
        columnValues.length + (options?.fromRowIndex ?? 0)
      );
      range(0, maxRows).forEach(rowIndex => {
        const existingRow = nextValue[rowIndex];
        if (rowIndex < (options?.fromRowIndex ?? 0)) {
          return;
        }
        const columnValue =
          columnValues[rowIndex - (options?.fromRowIndex ?? 0)] ?? undefined;

        if (existingRow) {
          if (columnValue === undefined) {
            if (!options?.keepOtherColumnValues) {
              delete existingRow[columnId];
            }
          } else {
            existingRow[columnId] = columnValue;
          }
          nextValue[rowIndex] = existingRow;
        } else {
          nextValue.push({
            [columnId]: columnValue
          });
        }
      });
      return nextValue;
    });
  };

  const setCell = (
    cellValue: TableFieldCellValue | undefined,
    rowIndex: number,
    columnId: TableFieldColumnId
  ) => {
    setValue(current => {
      const nextValue: TableFieldValue = _.cloneDeep(current);
      if (nextValue.length <= rowIndex) {
        nextValue.push(
          cellValue !== undefined ? { [columnId]: cellValue } : {}
        );
        return nextValue;
      }
      if (cellValue === undefined) {
        if (nextValue[rowIndex][columnId] === undefined) {
          return nextValue;
        }
        delete nextValue[rowIndex][columnId];
      } else {
        nextValue[rowIndex][columnId] = cellValue;
      }
      return nextValue;
    });
  };

  const onChangeColumnWidth: TableFieldOnChangeColumnWidth = (
    columnId,
    columnWidth
  ) => {
    console.log("onChangeColumnWidth", columnId, columnWidth);
    setColumnWidths(current => {
      const nextValue = _.cloneDeep(current ?? {});
      if (columnWidth === undefined) {
        delete nextValue[columnId];
        return nextValue;
      }
      nextValue[columnId] = columnWidth;
      return nextValue;
    });
  };
  useEffect(() => {
    console.log("TableField", value);
  }, [value]);

  return (
    <TableField
      {...args}
      id="tableStory"
      name={args?.name ?? "table-field"}
      value={value}
      setTable={setTable}
      setRow={setRow}
      removeRow={removeRow}
      reorderRow={reorderRow}
      setColumn={setColumn}
      setCell={setCell}
      stickyColumnId={stickyColumnId}
      onChangeStickyColumnId={setStickyColumnId}
      onChangeColumnWidth={onChangeColumnWidth}
      columnWidths={columnWidths}
      columns={args?.columns ?? []}
    />
  );
};

export const Default: Story = {
  args: { ...defaultArgs },
  render: defaultRender
};

export const VariantList: Story = {
  args: {
    ...defaultArgs,
    variant: "list",
    columns: (defaultArgs.columns ?? []).slice(0, 2)
  },
  render: defaultRender
};

export const VariantListHideHeader: Story = {
  args: {
    ...defaultArgs,
    variant: "list",
    columns: (defaultArgs.columns ?? []).slice(0, 1),
    hideHeader: true
  },
  render: defaultRender
};

export const WithStickyColumn: Story = {
  args: { ...defaultArgs, stickyColumnId: "name" },
  render: defaultRender
};

export const Disabled: Story = {
  args: { ...defaultArgs, disabled: true },
  render: defaultRender
};

export const DisabledVariantList: Story = {
  args: { ...defaultArgs, variant: "list", disabled: true },
  render: defaultRender
};

export const DisableAddRow: Story = {
  args: { ...defaultArgs, disableAddRow: true },
  render: defaultRender
};

export const DisableAddRowBetween: Story = {
  args: { ...defaultArgs, disableAddRowExceptEnd: true },
  render: defaultRender
};

export const WithMaxHeight: Story = {
  args: { ...defaultArgs, maxHeight: `${200}px` },
  render: defaultRender
};

export const WithMaxHeightAndManyItems: Story = {
  args: {
    ...defaultArgs,
    maxHeight: `${300}px`,
    value: range(0, 100).flatMap(() => defaultArgs.value ?? [])
  },
  render: defaultRender
};

export const CopyPasteTest: Story = {
  args: {
    ...defaultArgs,
    value: []
  },
  render: defaultRender
};
