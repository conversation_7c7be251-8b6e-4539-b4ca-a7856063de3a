import React, { useMemo } from "react";

import { Box } from "../../../fermions/index.ts";
import { useWarningCheckForInputs } from "../../../hooks/useWarningCheckForInputs.tsx";
import { Avatar } from "../../Avatar/Avatar.tsx";
import "../Select.scss";
import { Select, SelectProps, SelectRenderElement } from "../Select.tsx";
import { SelectOptionProps } from "../SelectOption/SelectOption.tsx";
import "./UserSelect.scss";
import {
  UserSelectOptionType,
  noOptionsLabelDefault
} from "./UserSelectTypes.ts";
import { useUserSelect } from "./useUserSelect";

export interface UserSelectProps extends Omit<SelectProps, "options"> {
  renderOptionLeftElement?: SelectOptionProps["renderLeftElement"];
  renderOptionRightElement?: SelectOptionProps["renderRightElement"];
  users?: UserSelectOptionType[];
  hideAvatars?: boolean;
}

export const UserSelect = ({
  value,
  users = [],
  noOptionsLabel = noOptionsLabelDefault,
  renderLeftElement,
  renderOptionLeftElement,
  onChange: handleChange,
  hideAvatars = false,

  onChangeSearch,
  ...props
}: UserSelectProps) => {
  useWarningCheckForInputs({
    value: value as string,
    defaultValue: props.defaultValue as string,
    onChange: handleChange
  });

  const [searchValue, setSearchValue] = React.useState("");

  const { options, renderOptionLeftAvatarElement, userSelectOptionToAvatar } =
    useUserSelect({
      users
    });

  const renderSelectedUserAvatar = useMemo(() => {
    if (renderLeftElement) {
      return renderLeftElement;
    }
    if (hideAvatars) {
      return undefined;
    }
    const renderLeftElementDefault: SelectRenderElement = ({
      selectedOption
    }) => {
      return (
        <Box
          alignment="center"
          style={{
            height: "10px",
            justifySelf: "center"
          }}
        >
          <Avatar
            size="s"
            user={
              selectedOption && !searchValue
                ? userSelectOptionToAvatar(selectedOption)
                : undefined
            }
          />
        </Box>
      );
    };
    return renderLeftElementDefault;
  }, [userSelectOptionToAvatar, hideAvatars, renderLeftElement, searchValue]);

  return (
    <Select
      value={value}
      onChange={handleChange}
      onChangeSearch={(searchValue: string) => {
        setSearchValue(searchValue);
        onChangeSearch?.(searchValue);
      }}
      options={options}
      renderLeftElement={renderSelectedUserAvatar}
      renderOptionLeftElement={
        renderOptionLeftElement || hideAvatars
          ? renderOptionLeftElement
          : renderOptionLeftAvatarElement
      }
      noOptionsLabel={noOptionsLabel}
      {...props}
    />
  );
};
