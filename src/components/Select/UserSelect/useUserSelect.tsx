import { useCallback, useMemo } from "react";
import React from "react";

import { getUserDisplayName } from "../../../helpers";
import { Avatar } from "../../Avatar/Avatar";
import {
  SelectOptionElement,
  SelectOptionType,
  SelectOptions
} from "../SelectTypes";
import { UserSelectOptionType } from "./UserSelectTypes";

export const useUserSelect = ({
  users
}: {
  users?: UserSelectOptionType[];
}) => {
  const options: SelectOptions = useMemo(() => {
    return (users ?? []).map(user => ({
      label: !user.firstName
        ? String(user.id ?? "")
        : getUserDisplayName({
            firstName: user.firstName,
            lastName: user.lastName
          }),
      value: user.id,
      description: user.description,
      disabled: user.disabled,
      tag: user.tag,
      properties: user as unknown as Record<string, unknown>
    }));
  }, [users]);

  const userSelectOptionToAvatar = useCallback(
    (option: SelectOptionType | undefined) => {
      return {
        firstName: String(option?.properties?.firstName ?? ""),
        lastName: String(option?.properties?.lastName ?? ""),
        image: option?.properties?.image
          ? String(option?.properties?.image)
          : undefined
      };
    },
    []
  );

  const renderOptionLeftAvatarElement: SelectOptionElement = useCallback(
    ({ option }) => <Avatar size="s" user={userSelectOptionToAvatar(option)} />,
    [userSelectOptionToAvatar]
  );

  return {
    options,
    renderOptionLeftAvatarElement,
    userSelectOptionToAvatar
  };
};
