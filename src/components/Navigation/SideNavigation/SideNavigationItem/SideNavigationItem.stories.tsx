import React from "react";

import type { Meta, StoryObj } from "@storybook/react";

import { SideNavigationItem } from "./SideNavigationItem";

const meta: Meta<typeof SideNavigationItem> = {
  component: SideNavigationItem,
  title: "components/Navigation/SideNavigation/SideNavigationItem",
  argTypes: {},
  args: {},
  parameters: {
    docs: {
      description: {
        component: ``
      }
    }
  }
};

export default meta;

type Story = StoryObj<typeof SideNavigationItem>;

const defaultArgs: Story["args"] = {};

const SideNavigationDefaultRenderer = (args: Story["args"]) => {
  const [isActive, setIsActive] = React.useState(true);
  return (
    <SideNavigationItem
      {...args}
      isActive={isActive}
      onClick={() => setIsActive(!isActive)}
      icon={{ name: "settings" }}
    >
      Settings
    </SideNavigationItem>
  );
};

export const Default: Story = {
  args: defaultArgs,
  render: SideNavigationDefault<PERSON>enderer
};
