import React, {
  <PERSON><PERSON><PERSON><PERSON>,
  LegacyRef,
  PropsWithChildren,
  SyntheticEvent,
  forwardRef,
  useCallback,
  useMemo
} from "react";

import {
  getClassNames,
  returnStringIfTrue
} from "../../../../helpers/componentHelpers.ts";
import { Icon } from "../../../Icon/Icon.tsx";
import { IconType } from "../../../Icon/IconTypes.ts";
import "./TopNavigationItem.scss";

interface TopNavigationItemProps {
  href?: string;
  onClick?: EventHandler<SyntheticEvent>;
  isActive?: boolean;
  isHighlight?: boolean;
  icon?: IconType;
}

export const TopNavigationItem = forwardRef(
  (
    {
      href,
      onClick,
      isActive,
      isHighlight,
      icon,
      children
    }: PropsWithChildren<TopNavigationItemProps>,
    ref: LegacyRef<HTMLLIElement>
  ) => {
    const handleOnClick = useCallback(
      (e: SyntheticEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (onClick) {
          onClick(e);
        } else if (href) {
          window.location.pathname = href;
        }
      },
      [href, onClick]
    );
    const itemType = useMemo(
      () =>
        getComputedStyle(document.body).getPropertyValue(
          "--components-navigation-top-item-type"
        ),
      []
    );

    return (
      <li
        ref={ref}
        className={getClassNames([
          "top-navigation-item",
          returnStringIfTrue(isActive, "top-navigation-item--selected"),
          returnStringIfTrue(isHighlight, "top-navigation-item--highlight")
        ])}
      >
        <div className="top-navigation-item__background">
          {itemType === "cutout" && isActive && (
            <div className="top-navigation-item__background__cutout" />
          )}
          <div className="top-navigation-item__background__content" />
        </div>
        <button
          className="top-navigation-item__anchor"
          onClick={handleOnClick}
          onMouseDown={e => e.preventDefault()}
        >
          {icon && <Icon {...icon} />}
          {children}
        </button>
      </li>
    );
  }
);
