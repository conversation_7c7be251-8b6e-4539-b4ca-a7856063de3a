import React from "react";

import type { Meta, StoryObj } from "@storybook/react";

import { TopNavigationItem } from "./TopNavigationItem";

const meta: Meta<typeof TopNavigationItem> = {
  component: TopNavigationItem,
  title: "components/Navigation/TopNavigation/TopNavigationItem",
  argTypes: {},
  args: {},
  parameters: {
    docs: {
      description: {
        component: ``
      }
    }
  }
};

export default meta;

type Story = StoryObj<typeof TopNavigationItem>;

const defaultArgs: Story["args"] = {};

const TopNavigationDefaultRenderer = (args: Story["args"]) => {
  const [isActive, setIsActive] = React.useState(true);
  return (
    <TopNavigationItem
      {...args}
      isActive={isActive}
      onClick={() => setIsActive(!isActive)}
    >
      Settings
    </TopNavigationItem>
  );
};

export const Default: Story = {
  args: defaultArgs,
  render: TopNavigationDefaultRenderer
};

const TopNavigationIconOnlyRenderer = (args: Story["args"]) => {
  const [isActive, setIsActive] = React.useState(true);
  return (
    <TopNavigationItem
      {...args}
      isActive={isActive}
      onClick={() => setIsActive(!isActive)}
      icon={{ name: "settings" }}
    />
  );
};

export const TopNavigationItemIconOnly: Story = {
  args: defaultArgs,
  render: TopNavigationIconOnlyRenderer
};

const TopNavigationIconAndTextRenderer = (args: Story["args"]) => {
  const [isActive, setIsActive] = React.useState(true);
  return (
    <TopNavigationItem
      {...args}
      isActive={isActive}
      onClick={() => setIsActive(!isActive)}
      icon={{ name: "settings" }}
    >
      Settings
    </TopNavigationItem>
  );
};

export const TopNavigationItemIconAndText: Story = {
  args: defaultArgs,
  render: TopNavigationIconAndTextRenderer
};
