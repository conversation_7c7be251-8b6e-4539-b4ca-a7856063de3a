.top-navigation-item {
  width: fit-content;
  position: relative;
  height: var(--components-navigation-top-item-height, 48px);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--components-navigation-top-item-color-text, #021429);
  font-size: var(--components-navigation-top-item-font-size, 14px);
  font-family:
    var(--components-navigation-top-item-font-family, "Noto Sans"), Inter,
    system-ui, Avenir, Helvetica, Arial, sans-serif;
  font-weight: var(--components-navigation-top-item-font-weight, 500);
  cursor: pointer;

  &__anchor {
    padding: 0px var(--components-navigation-top-item-padding-horizontal, 12px);
    gap: var(--components-navigation-top-item-gap, 2px);
    color: inherit;
    text-decoration: none;
    z-index: 1;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    border: none;
    background-color: transparent;
    outline: none;
    cursor: pointer;
    user-select: none;
  }

  &__background {
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    padding: var(--components-navigation-top-item-background-padding-top, 8px)
      var(--components-navigation-top-item-background-padding-right, 4px)
      var(--components-navigation-top-item-background-padding-bottom, 8px)
      var(--components-navigation-top-item-background-padding-left, 4px);
    overflow: hidden;

    &__content {
      width: 100%;
      height: 100%;
      background: var(
        --components-navigation-top-item-background-color-background,
        rgba(255, 255, 255, 0)
      );
      border-radius: var(
        --components-navigation-top-item-background-border-radius,
        8px
      );
    }

    &__cutout {
      $size: 20px;
      position: absolute;
      top: calc($size / -2);
      right: calc($size / -2);
      width: $size;
      height: $size;
      rotate: 45deg;
      background: var(--components-navigation-top-color-background, #fff);
    }
  }

  &--highlight,
  &:hover,
  &:focus-within {
    color: var(--components-navigation-top-item-hover-color-text, #021429);

    & .top-navigation-item__background__content {
      background: var(
        --components-navigation-top-item-background-hover-color-background,
        #ecf2f6
      );
    }
  }
  &--selected {
    color: var(--components-navigation-top-item-selected-color-text, #5600f4);
    font-weight: var(
      --components-navigation-top-item-selected-font-weight,
      500
    );
    & .top-navigation-item__background__content {
      background: var(
        --components-navigation-top-item-background-selected-color-background,
        #f4f2ff
      );
    }
    &.top-navigation-item--highlight,
    &:hover,
    &:focus-within {
      color: var(
        --components-navigation-top-item-selected-hover-color-text,
        #28007d
      );

      & .top-navigation-item__background__content {
        background: var(
          --components-navigation-top-item-background-selected-hover-color-background,
          #e5d8ff
        );
      }
    }
  }

  &:has(.top-navigation-item__anchor:focus) {
    & .top-navigation-item__background__content {
      outline-offset: -1px;
      outline: 1px solid var(--components-inputs-focus-color-border, #0083ff);
    }
  }
}
