import React from "react";

import { Button, ButtonVariant, Toolt<PERSON> } from "@oneteam/onetheme";

import { getLinkToDebugPage } from "@helpers/debug";

import { useDictionary } from "@src/hooks/useDictionary";

export const ViewAsDocumentButton = ({
  documentId,
  hideLabel
}: {
  documentId: string;
  hideLabel?: boolean;
}) => {
  // NOTE: When switching for this feature to be only on lower level environments
  // FlowRunner also has a dropdown item to View Document
  // Lookup use of ui.common.viewAsDocument
  const d = useDictionary();
  const handleClick = () => {
    const linkToDebugPage = getLinkToDebugPage(documentId);
    window.open(linkToDebugPage, "_blank");
  };

  if (hideLabel) {
    return (
      <Tooltip content={d("ui.common.viewAsDocument")}>
        <Button
          width="100"
          label=""
          leftIcon={{ name: "description" }}
          variant={ButtonVariant.SECONDARY}
          onClick={handleClick}
        />
      </Tooltip>
    );
  }

  return (
    <Button
      width="100"
      label={d("ui.common.viewAsDocument")}
      leftIcon={{ name: "description" }}
      variant={ButtonVariant.SECONDARY}
      onClick={handleClick}
    />
  );
};
