import { create } from "zustand";

// export interface CursorState {
//   cursorPosition: { x: number; y: number };
//   updatePosition: (event: MouseEvent) => void;
// }

// export const useCursorStore = create<CursorState>(set => ({
//   cursorPosition: { x: 0, y: 0 },
//   updatePosition: (event: MouseEvent) =>
//     set({ cursorPosition: { x: event.clientX, y: event.clientY } })
// }));

// document.addEventListener("mousemove", e => {
//   useCursorStore.getState().updatePosition(e);
// });

// CursorStore.ts
interface CursorState {
  cursorPosition: { x: number; y: number };
  isOnElement: boolean;
  isInitialized: boolean;
  isInputFocused: boolean;
  updatePosition: (event: MouseEvent) => void;
  setIsOnElement: (isOnElement: boolean) => void;
  setIsInputFocused: (isInputFocused: boolean) => void;
  checkIfCursorOnElement: (elements: HTMLCollection[]) => boolean;
  initialize: () => (() => void) | void;
}

export const useCursorStore = create<CursorState>((set, get) => ({
  cursorPosition: { x: 0, y: 0 },
  isOnElement: false,
  isInitialized: false,
  isInputFocused: false,

  updatePosition: (event: MouseEvent) => {
    // Skip cursor updates when an input is focused to prevent clearing typed values
    if (get().isInputFocused) {
      return;
    }
    set({ cursorPosition: { x: event.clientX, y: event.clientY } });
  },

  setIsOnElement: (isOnElement: boolean) => set({ isOnElement }),

  setIsInputFocused: (isInputFocused: boolean) => set({ isInputFocused }),

  checkIfCursorOnElement: (elements: HTMLCollection[]) => {
    const { cursorPosition, isInputFocused } = get();

    if (!cursorPosition || !elements.length) return false;

    // If input is focused, maintain the current isOnElement state to prevent scrolling changes
    if (isInputFocused) {
      return get().isOnElement;
    }

    const isOnElement = elements.some(collection =>
      Array.from(collection).some(element => {
        const rect = element.getBoundingClientRect();
        return (
          cursorPosition.x >= rect.left &&
          cursorPosition.x <= rect.right &&
          cursorPosition.y >= rect.top &&
          cursorPosition.y <= rect.bottom
        );
      })
    );

    set({ isOnElement });
    return isOnElement;
  },

  initialize: () => {
    // Prevent multiple initializations
    if (get().isInitialized) return;

    const mouseMoveHandler = (e: MouseEvent) => {
      const currentState = get();
      currentState.updatePosition(e);

      // Only check cursor position if input is not focused
      if (!currentState.isInputFocused) {
        // Get elements
        const tableQuestions = document.getElementsByClassName("table-field");
        const textAreaInputs = document.getElementsByClassName(
          "text-area-field__input"
        );

        // Check if cursor is on elements
        currentState.checkIfCursorOnElement([tableQuestions, textAreaInputs]);
      }
    };

    const handleFocus = (e: FocusEvent) => {
      if (
        e.target instanceof HTMLElement &&
        (e.target.classList.contains("text-field__input") ||
          e.target.classList.contains("text-area-field__input") ||
          e.target.classList.contains("number-field__input") ||
          e.target.tagName === "INPUT" ||
          e.target.tagName === "TEXTAREA" ||
          e.target.getAttribute("contenteditable") === "true")
      ) {
        console.log("Input focused, disabling cursor tracking");
        set({ isInputFocused: true });
      }
    };

    const handleBlur = (e: FocusEvent) => {
      if (
        e.target instanceof HTMLElement &&
        (e.target.classList.contains("text-field__input") ||
          e.target.classList.contains("text-area-field__input") ||
          e.target.classList.contains("number-field__input") ||
          e.target.tagName === "INPUT" ||
          e.target.tagName === "TEXTAREA" ||
          e.target.getAttribute("contenteditable") === "true")
      ) {
        console.log("Input blurred, enabling cursor tracking");
        set({ isInputFocused: false });
      }
    };

    document.addEventListener("mousemove", mouseMoveHandler);
    // document.addEventListener("focusin", handleFocus);
    // document.addEventListener("focusout", handleBlur);
    set({ isInitialized: true });

    // Return cleanup function
    return () => {
      document.removeEventListener("mousemove", mouseMoveHandler);
      // document.removeEventListener("focusin", handleFocus);
      // document.removeEventListener("focusout", handleBlur);
      set({ isInitialized: false });
    };
  }
}));
