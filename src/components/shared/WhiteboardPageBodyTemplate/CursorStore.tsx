import { create } from "zustand";

// export interface CursorState {
//   cursorPosition: { x: number; y: number };
//   updatePosition: (event: MouseEvent) => void;
// }

// export const useCursorStore = create<CursorState>(set => ({
//   cursorPosition: { x: 0, y: 0 },
//   updatePosition: (event: MouseEvent) =>
//     set({ cursorPosition: { x: event.clientX, y: event.clientY } })
// }));

// document.addEventListener("mousemove", e => {
//   useCursorStore.getState().updatePosition(e);
// });

// CursorStore.ts
interface CursorState {
  cursorPosition: { x: number; y: number };
  isOnElement: boolean;
  updatePosition: (event: MouseEvent) => void;
  setIsOnElement: (isOnElement: boolean) => void;
  checkIfCursorOnElement: (elements: HTMLCollection[]) => boolean;
  initialize: () => void;
}

export const useCursorStore = create<CursorState>((set, get) => ({
  cursorPosition: { x: 0, y: 0 },
  isOnElement: false,

  updatePosition: (event: MouseEvent) =>
    set({ cursorPosition: { x: event.clientX, y: event.clientY } }),

  setIsOnElement: (isOnElement: boolean) => set({ isOnElement }),

  checkIfCursorOnElement: (elements: HTMLCollection[]) => {
    const { cursorPosition } = get();

    if (!cursorPosition || !elements.length) return false;

    const isOnElement = elements.some(collection =>
      Array.from(collection).some(element => {
        const rect = element.getBoundingClientRect();
        return (
          cursorPosition.x >= rect.left &&
          cursorPosition.x <= rect.right &&
          cursorPosition.y >= rect.top &&
          cursorPosition.y <= rect.bottom
        );
      })
    );

    set({ isOnElement });
    return isOnElement;
  },

  initialize: () => {
    document.addEventListener("mousemove", e => {
      const currentState = get();
      currentState.updatePosition(e);

      // Get elements
      const tableQuestions = document.getElementsByClassName("table-field");
      const textAreaInputs = document.getElementsByClassName(
        "text-area-field__input"
      );

      // Check if cursor is on elements
      currentState.checkIfCursorOnElement([tableQuestions, textAreaInputs]);
    });
  }
}));
