import { IconType } from "@oneteam/onetheme";

import { commonIcons } from "@src/constants/iconConstants";
import { routeConstants } from "@src/constants/routeConstants";

export enum Resource {
  FORMS = "forms",
  FLOWS = "flows",
  FOUNDATIONS = "foundations",
  SERIES = "series",
  LABELS = "labels"
}

export interface SideNavigationItemType {
  name: string;
  icon?: IconType;
  href: string;
  resource?: Resource;
  state?: string;
  isActive?: (
    matchPath: (path: string, properties?: { exact?: boolean }) => boolean
  ) => boolean;
  items?: SideNavigationItemType[];
}

export interface SideNavigationContentGroup {
  name: string;
  shouldDisplayName?: boolean;
  items: SideNavigationItemType[];
}

export enum ContextProperties {
  WORKSPACE = "workspace",
  DOC_ERROR_HELPER = "docErrorHelper"
}
export interface SideNavigationContext {
  heading?: string;
  headingMaxLines?: number;
  isContext: (
    matchPath: (path: string, properties?: { exact?: boolean }) => boolean
  ) => boolean;
  content?: {
    top: SideNavigationContentGroup[];
    bottom?: SideNavigationContentGroup[];
  };
  requires?: ContextProperties[];
}

export const sideNavContexts: SideNavigationContext[] = [
  {
    requires: [],
    isContext: matchPath => matchPath(routeConstants.appSettings),
    heading: "ui.appSettings.title",
    content: {
      top: [
        {
          name: "ui.appSettings.generalMenuName",
          shouldDisplayName: true,
          items: [
            {
              name: "ui.appSettings.apiKeys.title",
              icon: { name: "keys" },
              href: routeConstants.appSettingsApiKeys,
              isActive: matchPath =>
                matchPath(routeConstants.appSettingsApiKeys, { exact: false })
            }
          ]
        }
      ],
      bottom: []
    }
  },
  {
    requires: [ContextProperties.WORKSPACE, ContextProperties.DOC_ERROR_HELPER],
    isContext: matchPath =>
      matchPath(routeConstants.home) &&
      !matchPath(routeConstants.configuration) &&
      !matchPath(routeConstants.settings),
    content: {
      top: [
        {
          name: "top",
          items: [
            {
              name: "ui.collection.browse.sideNavTitle",
              icon: { name: "search" },
              href: routeConstants.collectionHome,
              isActive: matchPath =>
                !matchPath(routeConstants.flowRunner) &&
                matchPath(routeConstants.collectionHome, { exact: false })
            }
          ]
        },
        {
          name: "middle",
          items: [
            {
              name: "ui.flow.title",
              icon: { name: "play_arrow", fillStyle: "filled" },
              href: routeConstants.flowRunner,
              isActive: matchPath => matchPath(routeConstants.flowRunner)
            }
          ]
        }
      ],
      bottom: [
        {
          name: "bottom",
          items: [
            {
              name: "ui.help.title",
              icon: { name: "lightbulb" },
              href: ""
            }
          ]
        }
      ]
    }
  },
  {
    requires: [ContextProperties.WORKSPACE, ContextProperties.DOC_ERROR_HELPER],
    isContext: matchPath => matchPath(routeConstants.configuration),
    content: {
      top: [
        {
          name: "top",
          items: [
            {
              name: "ui.configuration.dashboard.title",
              icon: { name: "construction" },
              href: routeConstants.configuration,
              isActive: matchPath =>
                matchPath(routeConstants.configuration, { exact: true })
            }
          ]
        },
        {
          name: "middle",
          items: [
            {
              name: "ui.configuration.forms.title",
              icon: commonIcons.forms,
              href: routeConstants.configurationFormList,
              resource: Resource.FORMS,
              isActive: matchPath =>
                matchPath(routeConstants.configurationFormList)
            },
            {
              name: "ui.configuration.flows.title",
              icon: commonIcons.flows,
              href: routeConstants.configurationFlowList,
              resource: Resource.FLOWS,
              isActive: matchPath =>
                matchPath(routeConstants.configurationFlowList)
            },
            {
              name: "ui.configuration.foundations.title",
              icon: commonIcons.foundations,
              href: routeConstants.configurationFoundation,
              resource: Resource.FOUNDATIONS,
              isActive: matchPath =>
                matchPath(routeConstants.configurationFoundation)
            }
          ]
        }
      ],
      bottom: [
        {
          name: "ui.configuration.navigation.extrasMenuName",
          shouldDisplayName: true,
          items: [
            {
              name: "ui.configuration.labels.title",
              icon: commonIcons.labels,
              href: routeConstants.configurationLabels,
              resource: Resource.LABELS,
              isActive: matchPath =>
                matchPath(routeConstants.configurationLabels)
            }
          ]
        },
        {
          name: "help",
          items: [
            {
              name: "ui.help.title",
              icon: { name: "lightbulb" },
              href: ""
            }
          ]
        }
      ]
    }
  },
  {
    requires: [ContextProperties.WORKSPACE, ContextProperties.DOC_ERROR_HELPER],
    isContext: matchPath => matchPath(routeConstants.settings),
    content: {
      top: [
        {
          name: "ui.settings.navigation.generalMenuName",
          shouldDisplayName: true,
          items: [
            {
              name: "ui.settings.navigation.detailsMenuLabel",
              icon: { name: "settings" },
              href: routeConstants.settings,
              isActive: matchPath =>
                matchPath(routeConstants.settings, { exact: true })
            },
            // User access will be done here in the future
            {
              name: "ui.settings.navigation.permissionsMenuLabel",
              icon: { name: "person" },
              href: routeConstants.settingsPermissions,
              isActive: matchPath =>
                matchPath(routeConstants.settingsPermissions, { exact: true })
            }
          ]
        }
        // External API secrets will be done here in the future
        // {
        //   name: "Security",
        //   shouldDisplayName: true,
        //   items: [
        //     {
        //       name: "Access keys",
        //       icon: { name: "key" },
        //       href: ""
        //     }
        //   ]
        // }
      ],
      bottom: [
        {
          name: "bottom",
          items: [
            {
              name: "ui.help.title",
              icon: { name: "lightbulb" },
              href: ""
            }
          ]
        }
      ]
    }
  }
];
