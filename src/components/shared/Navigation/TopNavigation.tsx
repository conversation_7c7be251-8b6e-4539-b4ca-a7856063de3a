import React, { useCallback, useMemo, useState } from "react";

import {
  Avatar,
  AvatarSize,
  Box,
  DropdownItem,
  DropdownItemGroup,
  DropdownMenu,
  FloatingWithParentPosition,
  Icon,
  Inline,
  TopNavigation as OneThemeTopNavigation,
  Tooltip,
  TopNavigationItem,
  TopNavigationItemGroup
} from "@oneteam/onetheme";
import { generatePath, useLocation, useParams } from "react-router-dom";

import { OTAI_REDIRECT_URL_KEY, oneTeamRoutes } from "@helpers/otNavigate.ts";

import { useWorkspaceAccessLevel } from "@pages/settings/permissions/WorkspaceAccessLevelContext/WorkspaceAccessLevelContext.tsx";

import {
  AuthUser,
  UserLastActiveTabs
} from "@src/authentication/AuthContext.tsx";
import { routeConstants } from "@src/constants/routeConstants.ts";
import { useAuth } from "@src/hooks/useAuth.tsx";
import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { useMatchPath } from "@src/hooks/useMatchPath.tsx";
import { useNavigateOnClick } from "@src/hooks/useNavigateOnClick.tsx";
import { Workspace } from "@src/types/workspace.ts";

import "./TopNavigation.scss";
import { WorkspaceMenu } from "./WorkspaceMenu.tsx";
import logo from "./logo.png";
import { topNavigationItems } from "./topNavigationItems.ts";

interface TopNavigationProps {
  user: AuthUser | null;
  workspace: Workspace | null;
}

export const TopNavigation = ({ user, workspace }: TopNavigationProps) => {
  const d = useDictionary();
  const navigateOnClick = useNavigateOnClick();
  const [isOpen, setIsOpen] = useState(false);
  const { matchPath } = useMatchPath();
  const { workspaceKey } = useParams();
  const { pathname, search, hash } = useLocation();
  const { lastActiveTabs, updateLastActiveTabs } = useAuth();
  const { currentLevel } = useWorkspaceAccessLevel();

  const userMenu = useMemo(() => {
    if (!user) {
      return <></>;
    }
    return (
      <DropdownMenu
        trigger={({ onClick }) => (
          <Box onClick={onClick}>
            <Avatar user={user} size={AvatarSize.M} />
          </Box>
        )}
        className="user-menu"
        isOpen={isOpen}
        onOpenChange={(state: boolean) => {
          setIsOpen(state);
        }}
        position={FloatingWithParentPosition.BOTTOM_RIGHT}
      >
        <DropdownItemGroup>
          <DropdownItem
            id="settings"
            onClick={() => console.log("Navigate to user settings")}
            leftElement={<Icon name="account_circle" />}
          >
            Account settings
          </DropdownItem>
          <DropdownItem
            id="logout"
            leftElement={<Icon name="logout" />}
            onClick={() => {
              localStorage.clear();
              sessionStorage.clear();
              localStorage.setItem(OTAI_REDIRECT_URL_KEY, window.location.href);
              window.location.href = oneTeamRoutes.logout;
            }}
          >
            Logout
          </DropdownItem>
        </DropdownItemGroup>
      </DropdownMenu>
    );
  }, [user, isOpen]);

  const activeTopNavigationItem = useMemo(
    () => topNavigationItems.find(({ isActive }) => isActive?.(matchPath)),
    [matchPath]
  );

  const topNavigationPaths = useMemo(
    () => topNavigationItems.map(({ href }) => href),
    []
  );

  const accessibleTopNavigationItems = useMemo(
    () =>
      topNavigationItems.filter(({ accessLevel }) =>
        currentLevel?.includes(accessLevel)
      ),
    [currentLevel]
  );

  const navigateToTopNavigationItem = useCallback(
    (href: string) => (e: React.PointerEvent) => {
      // If the href is the exact same as the current path, do nothing
      if (matchPath(href, { exact: true }) && !e.metaKey) {
        return;
      }

      const lastActivePathAtDestination = lastActiveTabs[href];
      // Ensure collection tabs are not considered as the same tab to configuration tabs when navigating
      let isNavigateToSameTab = matchPath(href, { exact: false });
      if (href === routeConstants.home) {
        isNavigateToSameTab =
          !matchPath(routeConstants.configuration, {
            exact: false
          }) && !matchPath(routeConstants.settings, { exact: false });
      }

      if (activeTopNavigationItem) {
        const currentActiveTab = `${pathname}${search}${hash}`;
        // Only keep last active tabs for top navigation items, remove side navigation active tabs
        const topNavigationLastActiveTabs = Object.fromEntries(
          Object.entries(lastActiveTabs).filter(([key]) =>
            topNavigationPaths.includes(key)
          )
        );
        updateLastActiveTabs({
          ...topNavigationLastActiveTabs,
          [activeTopNavigationItem.href]: currentActiveTab
        } as UserLastActiveTabs);
      }

      if (!isNavigateToSameTab && lastActivePathAtDestination) {
        navigateOnClick(lastActivePathAtDestination)(e);
      } else {
        navigateOnClick(generatePath(href, { workspaceKey }))(e);
      }
    },
    [
      activeTopNavigationItem,
      hash,
      lastActiveTabs,
      matchPath,
      navigateOnClick,
      pathname,
      search,
      topNavigationPaths,
      updateLastActiveTabs,
      workspaceKey
    ]
  );

  return (
    <OneThemeTopNavigation>
      <Inline height="100" width="100" gap="200" alignment="left">
        <Inline
          className="menu-inline"
          height="100"
          gap="300"
          alignment="center"
        >
          <WorkspaceMenu />
          <Box className="menu-inline__logo-box" onClick={navigateOnClick("/")}>
            <img
              src={logo}
              alt={d("ui.navigation.altOneTeamAILogo")}
              className="menu-inline__logo-box__logo-img"
            />
          </Box>
        </Inline>
        {workspace && (
          <TopNavigationItemGroup>
            {accessibleTopNavigationItems.map(({ href, label }) => (
              <TopNavigationItem
                key={href}
                onClick={navigateToTopNavigationItem(href)}
                isActive={activeTopNavigationItem?.href === href}
              >
                {d(label)}
              </TopNavigationItem>
            ))}
          </TopNavigationItemGroup>
        )}
      </Inline>
      <Inline height="100" gap="200" alignment="center">
        <Box>
          <Tooltip
            content={d("ui.navigation.appSettingsLabel")}
            delay={500}
            position={FloatingWithParentPosition.BOTTOM_RIGHT}
          >
            <TopNavigationItem
              icon={{ name: "settings" }}
              isActive={matchPath(routeConstants.appSettings, { exact: false })}
              onClick={navigateToTopNavigationItem(routeConstants.appSettings)}
            />
          </Tooltip>
        </Box>
        <Box>{userMenu}</Box>
      </Inline>
    </OneThemeTopNavigation>
  );
};
