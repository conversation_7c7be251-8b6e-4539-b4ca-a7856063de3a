import React, {
  SyntheticEvent,
  useCallback,
  useEffect,
  useMemo,
  useState
} from "react";

import {
  Box,
  CustomDropdownMenuTrigger,
  DropdownItem,
  DropdownItemGroup,
  DropdownMenu,
  Icon,
  IconButton,
  IconSize,
  Pill,
  SearchBar,
  Text,
  TextSize,
  Tooltip
} from "@oneteam/onetheme";
import partition from "lodash/partition";
import { generatePath, useLocation, useParams } from "react-router-dom";

import { oneTeamRoutes } from "@helpers/otNavigate";

import { routeConstants } from "@src/constants/routeConstants";
import { useAuth } from "@src/hooks/useAuth";
import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { useGetWorkspaces } from "@src/hooks/useGetWorkspaces.tsx";
import { useMatchPath } from "@src/hooks/useMatchPath.tsx";
import { useNavigateOnClick } from "@src/hooks/useNavigateOnClick";
import { Workspace } from "@src/types/workspace.ts";

import "./WorkspaceMenu.scss";
import logoOneTeam from "./logoOneTeam.svg";

export const WorkspaceMenu = () => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const d = useDictionary();
  const navigateOnClick = useNavigateOnClick();
  const { pathname } = useLocation();
  const params = useParams();
  const { data: workspaces, isFetched: workspacesFetched } = useGetWorkspaces();
  const [search, setSearch] = useState<string>("");
  const { updateLastActiveTabs } = useAuth();
  const { matchPath } = useMatchPath();
  const [highlightedIndex, setHighlightedIndex] = useState<number>(0);

  const navigateToWorkspace = useCallback(
    (e: React.MouseEvent, workspace: Workspace) => {
      let path = pathname.replace(
        `/${params.workspaceKey}`,
        `/${workspace.key}`
      );
      if (params.configurationFormKey) {
        path = generatePath(routeConstants.configurationFormList, {
          workspaceKey: workspace.key
        });
      } else if (params.configurationFlowId) {
        path = generatePath(routeConstants.configurationFlowList, {
          workspaceKey: workspace.key
        });
      } else if (matchPath(routeConstants.flowRunner)) {
        path = generatePath(routeConstants.flowRunner, {
          workspaceKey: workspace.key
        });
      } else if (
        !matchPath(routeConstants.configuration) &&
        !matchPath(routeConstants.settings) &&
        matchPath(routeConstants.collectionHome)
      ) {
        path = generatePath(routeConstants.collectionHome, {
          workspaceKey: workspace.key
        });
      }

      updateLastActiveTabs({});
      navigateOnClick(path, {
        callbackWithoutNewTab: () => {
          setIsOpen(false);
          setSearch("");
          setHighlightedIndex(0);
        }
      })(e);
    },
    [
      matchPath,
      navigateOnClick,
      params.configurationFlowId,
      params.configurationFormKey,
      params.workspaceKey,
      pathname,
      updateLastActiveTabs
    ]
  );

  const workspaceList = useMemo(() => {
    const workspaceSelected = (workspaceKey: string) => {
      const absolutePath = generatePath(routeConstants.home, { workspaceKey });
      return (
        pathname === absolutePath || pathname.startsWith(absolutePath + "/")
      );
    };

    if (!workspacesFetched) {
      return null;
    }

    const [selectedWorkspace, otherWorkspaces] = partition(
      workspaces,
      (workspace: Workspace) => workspaceSelected(workspace.key)
    );

    return [...selectedWorkspace, ...otherWorkspaces]
      .filter(
        (workspace: Workspace) =>
          !search ||
          workspace.name.toLowerCase().includes(search.toLowerCase()) ||
          workspace.key.toLowerCase().includes(search.toLowerCase())
      )
      .toSorted((a, b) => {
        if (search) {
          return b.id - a.id;
        }
        return 0;
      })
      .map((workspace: Workspace, idx: number) => (
        <DropdownItem
          key={workspace.key}
          isSelected={workspaceSelected(workspace.key)}
          rightElement={<Pill label={workspace.key} />}
          onClick={e => {
            navigateToWorkspace(e as React.MouseEvent, workspace);
          }}
          isHighlighted={highlightedIndex === idx}
          className="workspace-menu__workspace-item"
        >
          <Tooltip content={workspace.name} delay={500}>
            <Text truncate maxLines={1}>
              {workspace.name}
            </Text>
          </Tooltip>
        </DropdownItem>
      ));
  }, [
    workspacesFetched,
    workspaces,
    pathname,
    search,
    highlightedIndex,
    navigateToWorkspace
  ]);

  const workspaceTrigger: CustomDropdownMenuTrigger = useCallback(
    ({ onClick }) => (
      <Tooltip content={d("ui.navigation.workspaceMenuLabel")} delay={500}>
        <IconButton
          label={d("ui.navigation.workspaceMenuLabel")}
          showLabelTooltip={false}
          name="apps"
          onClick={onClick}
          size={IconSize.LARGE}
        />
      </Tooltip>
    ),
    [d]
  );

  useEffect(() => {
    const scrollToItem = (itemIndex: number) => {
      if (!Number.isInteger(itemIndex)) {
        return;
      }
      const selector = `#workspace-menu__workspace-group .workspace-menu__workspace-item:nth-child(${itemIndex + 1})`;
      window.document
        .querySelector(selector)
        ?.scrollIntoView({ behavior: "instant", block: "center" });
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "k" && e?.metaKey) {
        setIsOpen(!isOpen);
        setHighlightedIndex(0);
      }

      if (!isOpen) {
        return;
      }
      if (e.key === "Enter") {
        // navigate to the highlighted workspace
        const workspace = workspaceList?.[highlightedIndex];
        if (workspace != null) {
          workspace.props.onClick();
        }
        return;
      }

      const totalWorkspaces = workspaceList?.length ?? 0;
      if (totalWorkspaces === 0) {
        return;
      }

      if (e.key === "ArrowDown") {
        setHighlightedIndex(prev => {
          const nextItemIdx = (prev + 1) % totalWorkspaces;
          scrollToItem(nextItemIdx);
          return nextItemIdx;
        });
      }
      if (e.key === "ArrowUp") {
        setHighlightedIndex(prev => {
          const nextItemIdx = (prev - 1 + totalWorkspaces) % totalWorkspaces;
          scrollToItem(nextItemIdx);
          return nextItemIdx;
        });
      }
      if (e.key === "Enter") {
        const workspace = workspaceList?.[highlightedIndex];
        if (workspace != null) {
          workspace.props.onClick();
        }
      }
    };
    window.document.addEventListener("keydown", handleKeyDown);
    return () => {
      window.document.removeEventListener("keydown", handleKeyDown);
    };
  });

  const onSearchChanged = useCallback((value: string) => {
    setSearch(value);
    setHighlightedIndex(0);
  }, []);

  return (
    <DropdownMenu
      className="workspace-menu"
      isOpen={isOpen}
      onOpenChange={() => setIsOpen(!isOpen)}
      trigger={workspaceTrigger}
    >
      <DropdownItemGroup
        maxHeight={228}
        maxWidth={300}
        title={d("ui.navigation.workspaceMenuTitle")}
        id="workspace-menu__workspace-group"
      >
        <Box padding="025" width="100">
          <SearchBar
            value={search}
            handleChange={onSearchChanged}
            autoFocus={isOpen}
            width="100"
          />
        </Box>
        <Box overflow="auto">{workspaceList}</Box>
      </DropdownItemGroup>
      <DropdownItemGroup hasDivider>
        <DropdownItem
          onClick={
            navigateOnClick(routeConstants.creationWorkspace) as (
              e: SyntheticEvent<Element, Event>
            ) => void
          }
          leftElement={<Icon name="add" />}
        >
          <Text size={TextSize.M}>{d("ui.navigation.createWorkspace")}</Text>
        </DropdownItem>
      </DropdownItemGroup>
      <DropdownItemGroup hasDivider>
        <DropdownItem
          id="switch-to-oneteam"
          leftElement={
            <img
              src={logoOneTeam}
              alt={d("ui.navigation.altOneTeamLogo")}
              className="workspace-menu__ot-logo"
            />
          }
          onClick={
            navigateOnClick(undefined, {
              callbackWithoutNewTab: () => {
                window.location.href = oneTeamRoutes.home;
              },
              callbackWithNewTab: () => {
                window.open(oneTeamRoutes.home, "_blank");
              }
            }) as (e: SyntheticEvent<Element, Event>) => void
          }
        >
          {d("ui.navigation.switchToOneTeam")}
        </DropdownItem>
      </DropdownItemGroup>
    </DropdownMenu>
  );
};
