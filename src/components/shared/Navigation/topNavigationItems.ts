import { routeConstants } from "@src/constants/routeConstants";
import { WorkspaceAccessLevel } from "@src/types/WorkspaceUser";

export interface TopNavigationItemType {
  label: string;
  href: string;
  accessLevel: WorkspaceAccessLevel;
  isActive?: (
    matchPath: (path: string, properties?: { exact?: boolean }) => boolean
  ) => boolean;
}

export const topNavigationItems: TopNavigationItemType[] = [
  {
    href: routeConstants.home,
    label: "ui.collection.title",
    accessLevel: WorkspaceAccessLevel.COLLECTION,
    isActive: matchPath =>
      // In a workspace context, the collection home is the default route
      matchPath(routeConstants.home) &&
      !matchPath(routeConstants.configuration) &&
      !matchPath(routeConstants.settings)
  },
  {
    href: routeConstants.configuration,
    label: "ui.configuration.title",
    accessLevel: WorkspaceAccessLevel.CONFIGURATION,
    isActive: matchPath => matchPath(routeConstants.configuration)
  },
  {
    href: routeConstants.settings,
    label: "ui.settings.title",
    accessLevel: WorkspaceAccessLevel.SETTINGS,
    isActive: matchPath => matchPath(routeConstants.settings)
  }
];
