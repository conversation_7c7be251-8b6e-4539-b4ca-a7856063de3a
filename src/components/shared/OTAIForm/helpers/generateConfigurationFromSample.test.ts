import { describe, expect, it } from "vitest";

import { Question, QuestionTypes } from "../../../../types/Question";
import {
  JSONQuestionProperties,
  ListQuestionProperties,
  TableQuestionProperties
} from "../../../../types/QuestionProperties";
import { generateSchemaFromSampleData } from "./generateConfigurationFromSample";

const sampleInput = {
  success: true,
  listOfJson: [
    { label: "hello", abc: "abc" },
    { label: "hello", description: "some desc" }
  ],
  listOfPrimitives: ["hello", "hello", "some desc"],
  listOfLists: [["hello"], ["hello", "some desc"]],
  listOfListOfDifJson: [
    [{ label: "hello", abc: "abc" }],
    [{ label: "hello", description: "some desc" }]
  ]
};

const findField = (items: Question[], id: string) =>
  items?.find(item => item.identifier === id);

const expectIdAndIdentifier = (item: Question | undefined) => {
  expect(item).toBeDefined();
  expect(item?.identifier).toBeDefined();
  expect(item?.id).toBeDefined();
};

// Mock dictionary function
function d(key: string) {
  return key;
}

describe("generateSchemaFromSampleData", () => {
  it("should generate correct schema for the sample input", () => {
    const schema = generateSchemaFromSampleData({
      sampleData: sampleInput,
      options: { useTableForJsonLists: false },
      identifier: "identifier",
      d
    });
    expect(schema.type).toBe("json");
    const items = (schema.properties as JSONQuestionProperties).items;

    // Check top-level fields for identifier and id
    [
      "success",
      "listOfJson",
      "listOfPrimitives",
      "listOfLists",
      "listOfListOfDifJson"
    ].forEach(key => {
      expectIdAndIdentifier(findField(items, key));
    });

    // Primitive
    expect(findField(items, "success")?.type).toBe(QuestionTypes.BOOLEAN);

    // List of JSONs
    const listOfJson = findField(items, "listOfJson");
    expect(["table", "list"]).toContain(listOfJson?.type);

    // List of primitives
    const listOfPrimitives = findField(items, "listOfPrimitives");
    expect(listOfPrimitives?.type).toBe("list");
    expect(
      ((listOfPrimitives?.properties as ListQuestionProperties)?.items ?? [])[0]
        ?.type
    ).toBe(QuestionTypes.TEXT);

    // List of lists
    const listOfLists = findField(items, "listOfLists");
    expect(listOfLists?.type).toBe("list");
    expect(
      ((listOfLists?.properties as ListQuestionProperties)?.items ?? [])[0]
        ?.type
    ).toBe("list");

    // List of list of different JSONs
    const listOfListOfDifJson = findField(items, "listOfListOfDifJson");
    const nestedList = ((
      listOfListOfDifJson?.properties as ListQuestionProperties
    )?.items ?? [])[0];
    const nestedJson = ((nestedList?.properties as ListQuestionProperties)
      ?.items ?? [])[0];
    const nestedJsonProps =
      (nestedJson?.properties as JSONQuestionProperties)?.items ?? [];
    expect(nestedJson?.type).toBe("json");
    expect(
      nestedJsonProps.some((q: Question) => q.identifier === "label")
    ).toBe(true);
    expect(
      nestedJsonProps.some((q: Question) =>
        ["abc", "description"].includes(q.identifier)
      )
    ).toBe(true);
  });

  it.each([
    [123, QuestionTypes.NUMBER],
    ["a", QuestionTypes.TEXT],
    [undefined, QuestionTypes.TEXT]
  ])(
    "should generate schema for primitive sample data: %p",
    (sample, expectedType) => {
      const schema = generateSchemaFromSampleData({
        sampleData: sample,
        options: { useTableForJsonLists: true },
        identifier: "val",
        d
      });
      expectIdAndIdentifier(schema);
      expect(schema.type).toBe(expectedType);
    }
  );

  it("should generate schema for array of primitives", () => {
    const schema = generateSchemaFromSampleData({
      sampleData: ["a", "b", "c"],
      options: { useTableForJsonLists: true },
      identifier: "arr",
      d
    });
    expectIdAndIdentifier(schema);
    expect(schema.type).toBe("list");
    const item = ((schema.properties as ListQuestionProperties)?.items ??
      [])[0];
    expectIdAndIdentifier(item);
    expect(item?.type).toBe(QuestionTypes.TEXT);
  });

  it("should generate schema for array of objects", () => {
    const schema = generateSchemaFromSampleData({
      sampleData: [{ foo: "bar" }, { foo: "baz" }],
      options: { useTableForJsonLists: true },
      identifier: "arrObj",
      d
    });
    expectIdAndIdentifier(schema);
    expect(schema.type).toBe("table");
    const columns =
      (schema.properties as TableQuestionProperties)?.columns ?? [];
    expectIdAndIdentifier(columns[0]);
    expect(columns[0]?.type).toBe(QuestionTypes.TEXT);
  });
});
