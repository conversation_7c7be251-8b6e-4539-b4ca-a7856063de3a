import { customNanoId } from "@helpers/customNanoIdHelper.ts";

import { Dictionary } from "@src/hooks/useDictionary.tsx";
import { Question, QuestionTypes } from "@src/types/Question";
import { JSONQuestionProperties } from "@src/types/QuestionProperties";

type SamplePrimitive = string | boolean | number;
type SampleJsonOrList =
  | object
  | Array<object>
  | Array<string>
  | Array<boolean>
  | Array<number>;
export type SampleData =
  | undefined
  | SamplePrimitive
  | SampleJsonOrList
  | {
      [key: string]: SampleData;
    };

// supported types: text, number, boolean, list, json
export const generateSchemaFromSampleData = ({
  sampleData,
  level = 0,
  options,
  identifier,
  d
}: {
  sampleData: SampleData;
  level?: number; // Used for recursion, not needed in the final output
  options: {
    useTableForJsonLists?: boolean; // If true, will use table for json lists
  };
  identifier: string; // Identifier for the question
  d: Dictionary;
}): Question => {
  if (sampleData === undefined || sampleData === null) {
    // Handle undefined or null values
    return {
      type: QuestionTypes.TEXT,
      identifier,
      text: d("ui.configuration.forms.question.type.text.label"),
      properties: {
        required: false
      },
      id: identifier
    };
  } else if (Array.isArray(sampleData)) {
    // get type from first item - if all items are the same type, it will be used
    const firstItem = sampleData[0] as SampleData;

    const firstItemConfiguration = generateSchemaFromSampleData({
      sampleData: firstItem,
      level: level + 1,
      options,
      identifier: customNanoId(),
      d
    });

    // TODO: use all item definitions to determine supported types in list
    const allItemDefinitions = sampleData.map(item =>
      generateSchemaFromSampleData({
        sampleData: item,
        level: level + 1,
        options,
        identifier: customNanoId(),
        d
      })
    );

    const allItemsAreJson = allItemDefinitions.every(
      def => def.type === "json"
    );

    // If all items in the list are json
    if (allItemsAreJson) {
      const allColumns = allItemDefinitions.flatMap(
        def => (def.properties as JSONQuestionProperties)?.items ?? []
      );
      // Remove duplicates by identifier
      const uniqueColumnsMap = new Map(
        allColumns.map(item => [item.identifier, item])
      );

      const mergedItems = Array.from(uniqueColumnsMap.values());

      if (options?.useTableForJsonLists) {
        return {
          type: QuestionTypes.TABLE,
          properties: {
            columns: mergedItems
          },
          identifier,
          id: identifier,
          text: d("ui.configuration.forms.question.type.table.label")
        };
      } else {
        const jsonItems: Question<JSONQuestionProperties> = {
          type: QuestionTypes.JSON,
          properties: {
            items: mergedItems
          },
          identifier,
          id: identifier,
          text: d("ui.configuration.forms.question.type.json.label")
        };
        return {
          type: QuestionTypes.LIST,
          properties: {
            // Json and Lists
            items: [jsonItems]
          },
          identifier,
          id: identifier,
          text: d("ui.configuration.forms.question.type.list.label")
        };
      }
    }

    return {
      type: QuestionTypes.LIST,
      properties: {
        // Json and Lists
        items: [firstItemConfiguration]
      },
      identifier,
      id: identifier,
      text: d("ui.configuration.forms.question.type.list.label")
    };
  } else if (typeof sampleData === "object") {
    // TODO: make it a section!!
    const result = {
      type: QuestionTypes.JSON,
      properties: {
        items: [] as Question[]
      },
      identifier,
      id: identifier,
      text: d("ui.configuration.forms.question.type.json.label")
    };

    for (const [key, value] of Object.entries(sampleData)) {
      result.properties?.items?.push({
        ...generateSchemaFromSampleData({
          sampleData: value,
          level: level + 1,
          options,
          identifier: key,
          d
        }),
        text: key
      });
    }

    return result;
  } else {
    // Handle primitive values
    const result = {
      type: QuestionTypes.TEXT,
      identifier,
      text: d("ui.configuration.forms.question.type.text.label"),
      properties: {
        required: false
      },
      id: identifier
    };
    if (typeof sampleData === "number") {
      result.type = QuestionTypes.NUMBER;
      result.text = d("ui.configuration.forms.question.type.number.label");
    } else if (typeof sampleData === "boolean") {
      result.type = QuestionTypes.BOOLEAN;
      result.text =
        QuestionTypes.BOOLEAN.charAt(0).toUpperCase() +
        QuestionTypes.BOOLEAN.slice(1);
    }
    return result;
  }
};
