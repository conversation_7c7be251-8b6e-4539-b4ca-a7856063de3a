import React, { useCallback, useMemo } from "react";

import {
  Box,
  JsonEditor,
  Label,
  Stack,
  TabGroup,
  Text
} from "@oneteam/onetheme";

import { customNanoId } from "@helpers/customNanoIdHelper.ts";

import { ConfigurationQuestionBlock } from "@components/forms/configuration/question/ConfigurationQuestionBlock/ConfigurationQuestionBlock";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { ConfigurationFormMode } from "@src/types/FormConfiguration.ts";
import { Question } from "@src/types/Question";
import { SchemaAnswer } from "@src/types/collection/CollectionForm.ts";

import { OTAIFormField } from "../OTAIFormField";
import { OTAIFormFieldCommonProps } from "../OTAIFormType";
import { generateSchemaFromSampleData } from "../helpers/generateConfigurationFromSample";
import "./OTAIFormFieldTable.scss";

enum SchemaQuestionTabs {
  SAMPLE = "sample",
  SCHEMA = "schema",
  PREVIEW = "preview"
}

export const OTAIFormFieldSchemaQuestion = ({
  id,
  commonProps,
  onBlur: onBlurProp,
  value
}: {
  id: string;
  commonProps: OTAIFormFieldCommonProps;
  onBlur?: (value: object, identifier: string) => void;
  value: SchemaAnswer | undefined;
}) => {
  const d = useDictionary();
  const [tab, setTab] = React.useState(SchemaQuestionTabs.SAMPLE);

  const [sample, setSample] = React.useState<string>(
    JSON.stringify(structuredClone(value?.sample), null, 2) ?? "{}"
  );

  const [errorInSchemaGen, setErrorInSchemaGen] = React.useState<boolean>(
    sample === "{}"
  );

  const schema = useMemo(() => value?.schema, [value?.schema]);

  const handleSaveData = useCallback(
    (sample: unknown, schema?: Question) => {
      const valueToUpdate = {
        sample: sample ?? {},
        schema: schema ?? {}
      };
      setSample(JSON.stringify(valueToUpdate.sample, null, 2));
      onBlurProp?.(valueToUpdate, id);
    },
    [onBlurProp, id]
  );

  const onBlur = useCallback(
    (sample: string) => {
      try {
        const sampleAsJson = JSON.parse(sample);
        const configuration = generateSchemaFromSampleData({
          sampleData: sampleAsJson,
          options: {
            useTableForJsonLists: false
          },
          identifier: customNanoId(),
          d
        });
        setErrorInSchemaGen(false);
        handleSaveData(sampleAsJson, configuration);
      } catch (error) {
        console.error("Error parsing sample JSON:", error);
        setErrorInSchemaGen(true);
        handleSaveData({});
      }
    },
    [handleSaveData, d]
  );

  return (
    <Stack gap="025" width="100">
      <Label
        label={commonProps.label ?? ""}
        description={commonProps.description}
        required={commonProps.required}
      />
      <Box width="100" overflow="auto" gap="100">
        {!errorInSchemaGen && (
          <TabGroup
            options={[
              {
                value: SchemaQuestionTabs.SAMPLE,
                label: d(
                  "ui.configuration.forms.question.type.schema.tabHeaders.sample"
                )
              },
              ...(!errorInSchemaGen
                ? [
                    {
                      value: SchemaQuestionTabs.SCHEMA,
                      label: d(
                        "ui.configuration.forms.question.type.schema.tabHeaders.schema"
                      )
                    },
                    {
                      value: SchemaQuestionTabs.PREVIEW,
                      label: d(
                        "ui.configuration.forms.question.type.schema.tabHeaders.preview"
                      )
                    }
                  ]
                : [])
            ]}
            value={tab}
            handleChange={(value: string) => {
              setTab(value as SchemaQuestionTabs);
            }}
          />
        )}
        <Box>
          {tab === SchemaQuestionTabs.SAMPLE && (
            <Stack overflow="auto" gap="050">
              <Text size="s" color="text-tertiary">
                Paste a sample of your most complex schema and we will handle
                the schema generation
              </Text>
              <JsonEditor
                localJSONContent={sample}
                onBlur={(value: string | undefined) => {
                  onBlur(value ?? "{}");
                }}
                onChange={(value: string | undefined) => {
                  setSample(value ?? "{}");
                }}
                height="200px"
                disabled={false}
              />
            </Stack>
          )}
          {tab === SchemaQuestionTabs.SCHEMA && schema && (
            <Box overflow="auto" style={{ minHeight: "200px" }}>
              <ConfigurationQuestionBlock
                style={{ width: "100%" }}
                showReorder
                question={schema}
                mode={ConfigurationFormMode.VIEW}
                path={["test"]}
                onClick={() => {}} // todo: handleSelectQuestion
              />
            </Box>
          )}
          {tab === SchemaQuestionTabs.PREVIEW && schema && (
            <Box overflow="auto">
              <OTAIFormField question={schema} id={id} />
            </Box>
          )}
        </Box>
      </Box>
    </Stack>
  );
};
