import React, { useEffect } from "react";

import { Box, Label, Stack } from "@oneteam/onetheme";
import { useFormContext } from "react-hook-form";

import { QuestionListAnswer } from "@pages/collection/forms/questions/CollectionQuestionBlock/QuestionListAnswer";

import { useDictionary } from "@src/hooks/useDictionary";
import { Question } from "@src/types/Question";
import { ListQuestionProperties } from "@src/types/QuestionProperties";
import { FormAnswer, ListAnswer } from "@src/types/collection/CollectionForm";

import { OTAIFormFieldCommonProps } from "../OTAIFormType";
import "./OTAIFormFieldTable.scss";

export const OTAIFormFieldListQuestion = ({
  id,
  question,
  commonProps,
  value
}: {
  id: string;
  question: Question<ListQuestionProperties>;
  commonProps: OTAIFormFieldCommonProps;
  value?: FormAnswer<ListAnswer> | undefined;
}) => {
  const d = useDictionary();
  const { setValue } = useFormContext();

  useEffect(() => {
    setValue(id, undefined);
  }, [id, question, setValue]);

  return (
    <Stack gap="025">
      <Label
        label={commonProps.label ?? ""}
        description={commonProps.description}
        required={commonProps.required}
      />
      <Box width="100" overflow="auto">
        <QuestionListAnswer
          d={d}
          question={question}
          answer={value}
          answerAccessor={id}
        />
      </Box>
    </Stack>
  );
};
