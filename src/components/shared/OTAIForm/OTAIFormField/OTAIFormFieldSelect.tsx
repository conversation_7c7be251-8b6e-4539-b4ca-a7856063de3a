import React, { useEffect, useMemo, useState } from "react";

import {
  FermionProps,
  Form,
  MultiSelectValue,
  SelectOptionType,
  SelectValue
} from "@oneteam/onetheme";

import { optionsAdapterForComponent } from "@components/forms/configuration/question/ConfigurationQuestionModal/QuestionConfigurationFields/Select/SelectHelper.ts";
import OTAIFormFieldSelectOptionsFromDatabase from "@components/shared/OTAIForm/OTAIFormField/OTAIFormFieldSelectOptionsFromDatabase.tsx";
import { OTAIFormFieldCommonProps } from "@components/shared/OTAIForm/OTAIFormType.ts";

import { DynamicOptionsReturnType } from "@src/types/DynamicSelectOptions.ts";
import { Question, QuestionTypes } from "@src/types/Question.ts";
import {
  DynamicOptionTags,
  SelectQuestionProperties
} from "@src/types/QuestionProperties.ts";

import OTAIFormFieldSelectOptionsFromConfiguration from "./OTAIFormFieldSelectOptionsFromConfiguration.tsx";

export const OTAIFormFieldSelect = ({
  id,
  question,
  answer,
  onChange,
  onFocus,
  onlyTriggerChangeWhenBlur,
  dynamicOptionsCallback,
  className,
  width = "100",
  isLabelExternal,
  autoFocus,
  clearIfOptionMissing,
  error,
  style
}: {
  id: string;
  question: Question<SelectQuestionProperties>;
  answer: SelectValue;
  onChange?: (value: SelectValue | undefined) => void;
  onFocus?: React.FocusEventHandler;
  onlyTriggerChangeWhenBlur?: boolean;
  dynamicOptionsCallback?: (options: SelectOptionType[]) => void;
  className?: string;
  width?: FermionProps["width"];
  isLabelExternal?: boolean;
  autoFocus?: boolean;
  clearIfOptionMissing?: boolean;
  error?: string;
  style?: React.CSSProperties;
}) => {
  const [fetchedOptions, setFetchedOptions] =
    useState<DynamicOptionsReturnType>();

  const handleOptionsFetched = (options: DynamicOptionsReturnType) => {
    setFetchedOptions(options);
  };

  const commonProps: OTAIFormFieldCommonProps = useMemo(() => {
    const defaults = {
      hidden: question.properties?.hidden,
      disabled: question.properties?.disabled,
      autoFocus
    };
    if (isLabelExternal) {
      return defaults;
    }
    return {
      ...defaults,
      label: question.text,
      description: question.description,
      required: question.properties?.required,
      width,
      error,
      className,
      style
    };
  }, [
    isLabelExternal,
    question.text,
    question.description,
    question.properties?.required,
    question.properties?.hidden,
    question.properties?.disabled,
    autoFocus,
    width,
    error,
    className,
    style
  ]);

  const selectDynamicOptions = fetchedOptions?.options;
  const selectDynamicIsFetching = fetchedOptions?.isFetching;
  const options = useMemo(() => {
    if (selectDynamicOptions) {
      return selectDynamicOptions;
    }
    return optionsAdapterForComponent(question.properties?.options);
  }, [question.properties?.options, selectDynamicOptions]);

  useEffect(() => {
    if (dynamicOptionsCallback) {
      dynamicOptionsCallback(options);
    }
  }, [dynamicOptionsCallback, options]);

  const value = useMemo(() => {
    if (
      question.properties?.isMultiSelect &&
      (answer as MultiSelectValue)?.length === 0
    ) {
      return question?.properties?.defaultValue;
    }
    return answer ?? question?.properties?.defaultValue;
  }, [
    question.properties?.defaultValue,
    question.properties?.isMultiSelect,
    answer
  ]);

  const props = {
    ...commonProps,
    name: id,
    placeholder: question.properties?.placeholder ?? "",
    options: options,
    allowClear: !commonProps.required,
    value: value,
    onChange: options?.length ? onChange : undefined,
    onFocus: onFocus,
    isLoading: selectDynamicIsFetching,
    onlyTriggerChangeWhenBlur,
    clearIfOptionMissing
  };

  const dynamicOptionsComponent = useMemo(() => {
    switch (question.properties?.dynamicOptions?.tag) {
      case DynamicOptionTags.FOUNDATION_CONFIGURATION_ID:
      case DynamicOptionTags.FORM_CONFIGURATION_ID:
      case DynamicOptionTags.FORM_CONFIGURATION_KEY:
      case DynamicOptionTags.FORM_CONFIGURATION_QUESTION_ID:
      case DynamicOptionTags.SERIES_CONFIGURATION_ID:
      case DynamicOptionTags.FORM_CONFIGURATION_SERIES_INTERVAL_ID:
      case DynamicOptionTags.SERIES_INTERVAL_ID:
      case DynamicOptionTags.FLOW_CONFIGURATION_ID:
      case DynamicOptionTags.QUESTION_TYPES: {
        return (
          <OTAIFormFieldSelectOptionsFromConfiguration
            dynamicOptions={question.properties?.dynamicOptions}
            onOptionsFetched={handleOptionsFetched}
          />
        );
      }
      case DynamicOptionTags.FOUNDATION_COLLECTION_ID:
        return (
          <OTAIFormFieldSelectOptionsFromDatabase
            dynamicOptions={question.properties?.dynamicOptions}
            onOptionsFetched={handleOptionsFetched}
          />
        );
      case DynamicOptionTags.FOUNDATION_COLLECTION_KEY:
        return (
          <OTAIFormFieldSelectOptionsFromDatabase
            dynamicOptions={question.properties?.dynamicOptions}
            onOptionsFetched={handleOptionsFetched}
            useKeyAsValue
          />
        );
      default:
        return undefined;
    }
  }, [question.properties?.dynamicOptions]);

  if (commonProps.hidden) {
    return null;
  }

  return (
    <>
      {dynamicOptionsComponent}
      {question.type === QuestionTypes.MULTISELECT ? (
        <Form.MultiSelect className={className} width={width} {...props} />
      ) : (
        <Form.Select className={className} width={width} {...props} />
      )}
    </>
  );
};
