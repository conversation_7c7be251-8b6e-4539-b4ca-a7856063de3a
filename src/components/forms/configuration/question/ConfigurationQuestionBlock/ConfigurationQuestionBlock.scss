.question-configuration-block {
  &__actions {
    transition: opacity 0.15s;
    opacity: 0;
    visibility: hidden;

    & .icon-button {
      color: var(--color-text-secondary);
    }
  }

  & .question-configuration-block__header:hover {
    > .question-configuration-block__actions {
      opacity: 1;
      visibility: visible;
    }
  }
}

.whiteboard-block:hover {
  &:has(.whiteboard-block:hover) {
    border: var(--components-whiteboard-block-border-width, 1px) solid
      var(--components-whiteboard-block-color-border, #dae3ed);
  }
}
