import React, { useEffect, useMemo, useState } from "react";

import { Doc } from "@automerge/automerge-repo";
import { Breadcrumbs, BreadcrumbsItem, Stack } from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import {
  createQuestion,
  getByPath,
  getChildQuestionTypeOptions,
  getQuestionTypeOptionsForForms
} from "@helpers/configurationFormHelper.ts";

import { QuestionType } from "@components/forms/QuestionType/QuestionType.tsx";
import { FloatingModal } from "@components/shared/FloatingModal/FloatingModal.tsx";

import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { ConfigurationFormMode } from "@src/types/FormConfiguration.ts";
import { Question, QuestionTypes } from "@src/types/Question.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { QuestionPreview } from "../QuestionPreview/QuestionPreview.tsx";
import { ConfigurationQuestionFields } from "./QuestionConfigurationFields/QuestionConfigurationFields.tsx";
import { QuestionTypeSelect } from "./QuestionTypeSelect/QuestionTypeSelect.tsx";

export const ConfigurationQuestionModal = ({
  mode,
  questionPath,
  onClose,
  onUp,
  isMultiLevelQuestion = false
}: {
  mode: ConfigurationFormMode;
  questionPath: string;
  onClose: () => void;
  onUp?: () => void;
  isMultiLevelQuestion?: boolean;
}) => {
  const [width, setWidth] = useState<string | undefined>("30vw");
  const { document, docChange } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    docChange: DocChange;
  }>();

  const question = useMemo(() => {
    return getByPath<Question>(document, questionPath?.split("."));
  }, [document, questionPath]);

  const parentQuestion = useMemo(() => {
    if (!questionPath || !isMultiLevelQuestion) {
      return null;
    }
    const parentPath = questionPath.split(".").slice(0, -3);
    return getByPath<Question>(document, parentPath);
  }, [document, isMultiLevelQuestion, questionPath]);

  useEffect(() => {
    // path reference is invalid, close modal
    if (questionPath && questionPath !== "none" && !question) {
      console.error("invalid path", questionPath);
      onClose();
    }
  }, [onClose, question, questionPath]);

  const questionSelect = useMemo(
    () => (
      <QuestionTypeSelect
        type={question?.type}
        path={questionPath.split(".")}
        options={
          (parentQuestion?.type
            ? getChildQuestionTypeOptions(parentQuestion?.type)
            : getQuestionTypeOptionsForForms()) as QuestionTypes[]
        }
        handleChange={newType => {
          docChange(d => {
            const q = getByPath<Question>(d, questionPath.split("."));
            if (!q) {
              console.error("Question not found", questionPath);
              return;
            }
            q.type = newType;
            const newQuestion = createQuestion(newType);
            q.properties = newQuestion.properties;
          });
        }}
        disabled={mode !== ConfigurationFormMode.EDIT}
      />
    ),
    [docChange, mode, parentQuestion?.type, question?.type, questionPath]
  );
  if (!questionPath || questionPath === "none") {
    return <></>;
  }

  if (!question) {
    return <></>;
  }

  return (
    <FloatingModal
      key={questionPath}
      className="question-configuration-modal"
      headingOverride={
        <Stack
          gap="075"
          style={{ height: "var(--spacing-300)" }}
          alignment="left"
        >
          {isMultiLevelQuestion ? (
            <Breadcrumbs>
              {parentQuestion && (
                <BreadcrumbsItem
                  text={parentQuestion!.text ?? "< Back"}
                  href={""}
                  leftElement={
                    <QuestionType type={parentQuestion!.type} iconOnly />
                  }
                  onClick={onUp}
                />
              )}
              {questionSelect}
            </Breadcrumbs>
          ) : (
            questionSelect
          )}
        </Stack>
      }
      onClose={onClose}
      width={width}
      setWidth={setWidth}
    >
      <ConfigurationQuestionFields
        question={question}
        path={questionPath.split(".")}
        disabled={mode !== ConfigurationFormMode.EDIT}
        parentQuestion={parentQuestion ?? undefined}
      />
      <QuestionPreview question={question} />
    </FloatingModal>
  );
};
