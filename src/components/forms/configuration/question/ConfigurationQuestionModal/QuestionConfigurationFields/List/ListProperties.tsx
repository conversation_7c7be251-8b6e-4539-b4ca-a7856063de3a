import React from "react";

import { Doc, Prop } from "@automerge/automerge-repo";
import { Stack } from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { Dictionary } from "@src/hooks/useDictionary.tsx";
import { ConfigurationFormMode } from "@src/types/FormConfiguration";
import { Question } from "@src/types/Question.ts";
import { ListQuestionProperties } from "@src/types/QuestionProperties";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { MultiLevelProperties } from "../MultiLevel/MultiLevelProperties";
import { MinMaxLengthFields } from "../Text/TextProperties";

export const ListProperties = ({
  question,
  path,
  d,
  isExpanded = true,
  onChangeExpanded,
  disabled
}: {
  question: Question<ListQuestionProperties>;
  path: Prop[];
  d: Dictionary;
  isExpanded?: boolean;
  onChangeExpanded?: (expanded: boolean) => void;
  disabled?: boolean;
}) => {
  const { docChange } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    docChange: DocChange;
  }>();

  return (
    <Stack gap="100" className="multi-level-properties">
      <MinMaxLengthFields
        question={question}
        path={path}
        disabled={disabled}
        docChange={docChange}
        d={d}
      ></MinMaxLengthFields>
      <MultiLevelProperties
        question={question}
        path={path}
        d={d}
        showReorder={false}
        isExpanded={isExpanded}
        onChangeExpanded={onChangeExpanded}
        onlyContainOneQuestion
        mode={ConfigurationFormMode.EDIT}
      />
    </Stack>
  );
};

ListProperties.displayName = "ListProperties";
