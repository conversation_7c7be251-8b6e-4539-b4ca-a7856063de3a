import React, { useC<PERSON>back, useMemo } from "react";

import { Doc, Prop } from "@automerge/automerge-repo";
import {
  Inline,
  MultiSelect,
  NumberField,
  SelectValue,
  Stack
} from "@oneteam/onetheme";
import { useOutletContext } from "react-router-dom";

import { getByPath } from "@helpers/configurationFormHelper";
import { formatPositiveInteger } from "@helpers/numericalHelper";

import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { Question } from "@src/types/Question.ts";
import {
  FilesFileFormats,
  FilesFileFormatsStrings,
  FilesQuestionProperties
} from "@src/types/QuestionProperties.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import {
  MAX_FILE_SIZE_MB,
  MAX_NUMBER_OF_FILES,
  optionsAdapterForComponent
} from "./FilesHelper";

export const FilesProperties = ({
  question,
  path,
  disabled
}: {
  question: Question<FilesQuestionProperties>;
  path: Prop[];
  disabled?: boolean;
}) => {
  const d = useDictionary();
  const { docChange } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    docChange: DocChange;
  }>();
  const { min, max, maxFileSizeMB, restrictedFileTypes } =
    question.properties ?? {};

  const questionAccessor = useCallback(
    (accessor: string) => {
      return `${path.join(".")}.${accessor}`;
    },
    [path]
  );

  const updateField = useCallback(
    (field: keyof FilesQuestionProperties) => {
      return (value: string | SelectValue[]) => {
        docChange((d: WorkspaceDocument): void => {
          const q = getByPath<Question<FilesQuestionProperties>>(d, path);
          if (!q?.properties) {
            console.error("Question not found", path);
            return;
          }
          if (value === undefined || value === "") {
            delete q.properties[field];
            return;
          }
          switch (field) {
            case "min":
            case "max":
            case "maxFileSizeMB": {
              q.properties[field] = Number(value);
              break;
            }
            case "restrictedFileTypes": {
              q.properties[field] = value as FilesFileFormatsStrings[];
              break;
            }
          }
        });
      };
    },
    [docChange, path]
  );

  const minNumFilesErrors = useMemo(() => {
    if (min === undefined) {
      return undefined;
    }
    if (min < 0) {
      return d("errors.configurationForm.question.files.min.negative");
    }
    if (min > (max ?? Number.POSITIVE_INFINITY)) {
      return d("errors.configurationForm.question.files.min.greaterThanMax");
    }
    return undefined;
  }, [min, max, d]);

  const maxNumFilesErrors = useMemo(() => {
    if (max === undefined) {
      return undefined;
    }
    if (max < 0) {
      return d("errors.configurationForm.question.files.max.negative");
    }
    if (max < (min ?? 0)) {
      return d("errors.configurationForm.question.files.max.lessThanMin");
    }
    if (max > MAX_NUMBER_OF_FILES) {
      return d("errors.configurationForm.question.files.max.upperLimit", {
        max: MAX_NUMBER_OF_FILES
      });
    }
    return undefined;
  }, [min, max, d]);

  const maxFileSizeErrors = useMemo(() => {
    if (maxFileSizeMB === undefined) {
      return undefined;
    }
    if (maxFileSizeMB < 0) {
      return d("errors.configurationForm.question.files.maxFileSize.negative");
    }
    if (maxFileSizeMB > MAX_FILE_SIZE_MB) {
      return d(
        "errors.configurationForm.question.files.maxFileSize.greaterThanMaximum"
      );
    }
    return undefined;
  }, [d, maxFileSizeMB]);

  const restrictedFileTypeOptions = useMemo(() => {
    const orderedFileFormats = [
      FilesFileFormats.pdf,
      FilesFileFormats.document,
      FilesFileFormats.spreadsheet,
      FilesFileFormats.presentation,
      FilesFileFormats.image,
      FilesFileFormats.audio,
      FilesFileFormats.video
    ];
    return optionsAdapterForComponent(orderedFileFormats, d);
  }, [d]);

  return (
    <Stack gap="150">
      <Inline gap="150">
        <NumberField
          width="100"
          label={d("ui.configuration.forms.question.files.minFiles.label")}
          name={questionAccessor("properties.min")}
          value={min ? String(min) : ""}
          disabled={disabled}
          min={0}
          max={max ?? MAX_NUMBER_OF_FILES}
          error={minNumFilesErrors}
          allowClear={true}
          format={formatPositiveInteger}
          onChange={updateField("min")}
          onlyTriggerChangeWhenBlur
        />
        <NumberField
          width="100"
          label={d("ui.configuration.forms.question.files.maxFiles.label")}
          name={questionAccessor("properties.max")}
          value={max ? String(max) : ""}
          disabled={disabled}
          allowClear={true}
          min={min ?? 0}
          max={MAX_NUMBER_OF_FILES}
          error={maxNumFilesErrors}
          onChange={updateField("max")}
          placeholder={`${MAX_NUMBER_OF_FILES}`}
          onlyTriggerChangeWhenBlur
        />
      </Inline>
      <Inline gap="150">
        <NumberField
          width="100"
          label={d("ui.configuration.forms.question.files.maxFileSizeMB.label")}
          name={questionAccessor("properties.maxFileSizeMB")}
          value={maxFileSizeMB ? String(maxFileSizeMB) : ""}
          disabled={disabled}
          allowClear={true}
          min={0}
          max={MAX_FILE_SIZE_MB}
          format={formatPositiveInteger}
          onChange={updateField("maxFileSizeMB")}
          error={maxFileSizeErrors}
          placeholder={`${MAX_FILE_SIZE_MB}`}
          onlyTriggerChangeWhenBlur
        />
        <MultiSelect
          width="100"
          label={d(
            "ui.configuration.forms.question.files.restrictedFileTypes.label"
          )}
          name={questionAccessor("properties.restrictedFileTypes")}
          value={restrictedFileTypes}
          disabled={disabled}
          options={restrictedFileTypeOptions}
          onChange={v => updateField("restrictedFileTypes")(v as SelectValue[])}
          onlyTriggerChangeWhenBlur
        />
      </Inline>
    </Stack>
  );
};
