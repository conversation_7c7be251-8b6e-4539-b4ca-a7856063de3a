import { MimeTypes, mimeTypeToExtension } from "@oneteam/onetheme";

import { Dictionary } from "@src/hooks/useDictionary";
import { FilesFileFormatsStrings } from "@src/types/QuestionProperties";

export const MAX_FILE_SIZE_MB = 100;
export const MAX_NUMBER_OF_FILES = 25;

function extensionsToMimeTypes(extensions: string[]): MimeTypes[] {
  return extensions.map(ext => MimeTypes[ext as keyof typeof MimeTypes]);
}

const imageTypes = extensionsToMimeTypes([
  ".png",
  ".jpeg",
  ".gif",
  ".bmp",
  ".webp",
  ".svg"
]);

const videoTypes = extensionsToMimeTypes([".mp4", ".avi", ".webm"]);
const audioTypes = extensionsToMimeTypes([".mp3", ".wav", ".weba", ".opus"]);
const pdfType = extensionsToMimeTypes([".pdf"]);
const spreadsheetTypes = extensionsToMimeTypes([".xls", ".xlsx", ".csv"]);
const documentTypes = extensionsToMimeTypes([".doc", ".docx"]);
const presentationTypes = extensionsToMimeTypes([".ppt", ".pptx"]);

export const FILE_FORMATS = Object.freeze({
  audio: audioTypes,
  document: documentTypes,
  image: imageTypes,
  pdf: pdfType,
  presentation: presentationTypes,
  spreadsheet: spreadsheetTypes,
  video: videoTypes
});

export const optionsAdapterForComponent = (
  fileFormats: FilesFileFormatsStrings[],
  d: Dictionary
) => {
  return (
    fileFormats?.map(fileFormat => {
      return {
        label: d(
          `ui.configuration.forms.question.files.restrictedFileTypes.formats.${fileFormat}`
        ),
        value: fileFormat,
        description: FILE_FORMATS[fileFormat]
          .map(mimeTypeToExtension)
          .join(", ")
      };
    }) ?? []
  );
};

export const fileFormatsToMimeType = (
  fileFormats?: FilesFileFormatsStrings[]
): MimeTypes[] | undefined => {
  if (!fileFormats?.length) {
    return Object.values(FILE_FORMATS).flat();
  }

  return fileFormats?.map(fileFormat => FILE_FORMATS[fileFormat]).flat();
};
