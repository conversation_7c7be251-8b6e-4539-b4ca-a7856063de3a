import React, { useEffect, useMemo } from "react";

import { Doc, Prop } from "@automerge/automerge-repo";
import { updateText } from "@automerge/automerge/next";
import {
  FloatingContentPanel,
  Form,
  SelectOptionType,
  Stack
} from "@oneteam/onetheme";
import { useFormContext } from "react-hook-form";
import { useOutletContext } from "react-router-dom";

import {
  DocumentErrorHelper,
  ErrorFieldKeyMap
} from "@helpers/DocumentErrorHelper.ts";
import { mapOverResource } from "@helpers/OrderedMapNoState.ts";

import { ConfigurationLabelsSelect } from "@components/shared/ConfigurationLabelsSelect/ConfigurationLabelsSelect";

import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { ConfigurationFormType } from "@src/types/FormConfiguration.ts";
import { LabelAvailableTo } from "@src/types/Label";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

interface ConfigurationFormListModalFieldsProps {
  configurationForm: ConfigurationFormType;
  path: Prop[];
  disabled?: boolean;
}

export const ConfigurationFormContextPanel = ({
  configurationForm,
  path,
  disabled
}: ConfigurationFormListModalFieldsProps) => {
  const d = useDictionary();
  const { document, docChange, docErrorHelper } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    docChange: DocChange;
    docErrorHelper: DocumentErrorHelper;
  }>();

  const { setError, clearErrors } = useFormContext();

  const formKey = useMemo(() => {
    return path[1] as number;
  }, [path]);

  const foundationOptions = mapOverResource(
    document.foundations,
    foundationConfig => {
      return {
        label: foundationConfig.name,
        value: foundationConfig.id
      };
    }
  );

  const series: SelectOptionType[] = Object.entries(document.series ?? {}).map(
    ([seriesId, seriesConfig]) => {
      return { label: seriesConfig.name, value: seriesId };
    }
  );

  const errors: ErrorFieldKeyMap[] = useMemo(() => {
    return (
      docErrorHelper.getDictionaryMessagesForErrors({
        prefix: `$.forms.${formKey}`,
        resourceName: "forms"
      }) ?? []
    );
  }, [docErrorHelper, formKey]);

  useEffect(() => {
    clearErrors();

    errors?.forEach(error => {
      setError(`${error.field}`, { message: d(error.message) });
    });
  }, [clearErrors, d, errors, setError, formKey]);

  return (
    <FloatingContentPanel heading="Details">
      <Stack gap="100" height="100" overflow="auto">
        <Form.TextField
          autoFocus
          withDebounce
          onChange={name =>
            docChange(d => {
              updateText(d, [...path, "name"], name);
            })
          }
          value={configurationForm?.name}
          name="name"
          label={d("ui.configuration.forms.creation.name")}
          debounceTime={500}
          width="100"
        />
        <Form.TextAreaField
          name="description"
          label={d("ui.configuration.forms.creation.description")}
          withDebounce
          value={configurationForm?.description}
          onChange={description =>
            docChange(d => {
              updateText(d, [...path, "description"], description);
            })
          }
          debounceTime={500}
          width="100"
        />
        <ConfigurationLabelsSelect
          isInForm
          label={d("ui.configuration.labels.title")}
          name="labels"
          value={configurationForm?.labels}
          labelFor={LabelAvailableTo.FORM_CONFIGURATION}
          onChange={labels => {
            docChange(d => {
              if (!d.forms[formKey].labels) {
                d.forms[formKey].labels = labels ?? [];
              } else {
                d.forms[formKey].labels = labels ?? [];
              }
            });
          }}
        />
        <Form.Select
          name="foundationId"
          label={d("ui.terminology.foundationConfigurationLevel")}
          value={configurationForm?.foundationId}
          options={foundationOptions}
          disabled={disabled}
          description={d(
            "ui.configuration.forms.creation.foundation.description"
          )}
          onChange={foundation => {
            docChange(d => {
              if (!d.forms[formKey].foundationId) {
                d.forms[formKey].foundationId = foundation?.toString() ?? "";
              } else {
                updateText(
                  d,
                  [...path, "foundationId"],
                  foundation?.toString() ?? ""
                ); // "seriesId" hardcoded, should be a constant...
              }
            });
          }}
        />
        <Form.Select
          name="series"
          label={d("ui.terminology.series")}
          value={configurationForm?.seriesId}
          options={series}
          disabled={disabled}
          onChange={series => {
            docChange(d => {
              if (!d.forms[formKey].seriesId) {
                // if seriesId is undefined / seriesId not a property of form config
                // TODO: add to other updateText usages too?
                d.forms[formKey].seriesId = series?.toString() ?? "";
              } else {
                updateText(d, [...path, "seriesId"], series?.toString() ?? ""); // "seriesId" hardcoded, should be a constant...
              }
            });
          }}
        />
      </Stack>
    </FloatingContentPanel>
  );
};

ConfigurationFormContextPanel.displayName = "ConfigurationFormContextPanel";
