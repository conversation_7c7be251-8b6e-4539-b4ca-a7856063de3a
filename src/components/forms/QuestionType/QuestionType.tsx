import React, { useMemo } from "react";

import {
  ColorText,
  Icon,
  IconSize,
  Inline,
  Text,
  TextSize,
  Tooltip
} from "@oneteam/onetheme";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { QuestionTypeOptions, questionTypeIcon } from "@src/types/Question.ts";

export const QuestionType = ({
  type,
  size = "small",
  color = undefined,
  iconOnly = false,
  withTooltip = false
}: {
  type: QuestionTypeOptions;
  size?: "regular" | "small";
  color?:
    | `${ColorText.PRIMARY}`
    | `${ColorText.SECONDARY}`
    | `${ColorText.TERTIARY}`;
  iconOnly?: boolean;
  withTooltip?: boolean;
}) => {
  const d = useDictionary();
  const icon = useMemo(() => {
    const iconProps = questionTypeIcon[type];
    if (!iconProps) {
      return <></>;
    }
    return (
      <Icon
        {...iconProps}
        size={size === "regular" ? IconSize.REGULAR : IconSize.SMALL}
        color={color}
      />
    );
  }, [size, type, color]);

  const content = useMemo(
    () => (
      <>
        {icon}
        {!iconOnly && (
          <Text
            size={size === "regular" ? TextSize.M : TextSize.XS}
            color={color}
          >
            {d(`ui.configuration.forms.question.type.${type}.label`)}
          </Text>
        )}
      </>
    ),
    [color, d, icon, iconOnly, size, type]
  );

  return (
    <Inline key={`question-${type}`} gap="025" alignment="left">
      {withTooltip ? (
        <Tooltip
          content={d(`ui.configuration.forms.question.type.${type}.label`)}
        >
          {content}
        </Tooltip>
      ) : (
        content
      )}
    </Inline>
  );
};
