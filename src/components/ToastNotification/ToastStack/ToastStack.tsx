import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState
} from "react";

import { Box, Inline, Stack } from "../../../fermions";
import { CloseIconButton } from "../../Button";
import { IconSize } from "../../Icon";
import { Tooltip } from "../../Tooltip";
import {
  ToastNotification,
  ToastNotificationProps
} from "../ToastNotification";
import "./ToastStack.scss";

export interface ToastStackProps {
  initialToasts?: ToastNotificationProps[];
  position?: "top-left" | "top-right" | "bottom-left" | "bottom-right";
  style?: React.CSSProperties;
  slideDirection?: "left" | "right";
  handleClearAll?: (toasts: ToastItem[]) => ToastItem[];
}

export interface ToastStackHandle {
  pushToast: (toast: ToastNotificationProps & { duration?: number }) => void;
}

interface ToastItem {
  id: number;
  toast: ToastNotificationProps & { duration?: number };
  visible: boolean;
}

let toastId = 0;

export const ToastStack = forwardRef<ToastStackHandle, ToastStackProps>(
  (
    {
      initialToasts = [],
      position = "bottom-right",
      style = {},
      slideDirection = "right",
      handleClearAll = undefined
    },
    ref
  ) => {
    const [toasts, setToasts] = useState<ToastItem[]>(() => {
      return initialToasts.map(t => ({
        id: toastId++,
        toast: t,
        visible: true
      }));
    });

    useImperativeHandle(ref, () => ({
      pushToast(toast) {
        if (toasts.length > 1) {
          setIsHidden(false);
        } else {
          setIsHidden(true);
        }
        const newToast: ToastItem = { id: toastId++, toast, visible: false };
        setToasts(prev => [...prev, newToast]);

        // need to for the component to mount, and THEN animate it in
        requestAnimationFrame(() => {
          setToasts(prev =>
            prev.map(t => (t.id === newToast.id ? { ...t, visible: true } : t))
          );
        });

        const duration = toast.duration || 3000;
        // after the duration, animate it out
        setTimeout(() => {
          setToasts(prev =>
            prev.map(t => (t.id === newToast.id ? { ...t, visible: false } : t))
          );
          setIsClearing(false);
        }, duration);
      }
    }));

    const [isHidden, setIsHidden] = useState(true);
    const [isClearing, setIsClearing] = useState(false);

    useEffect(() => {
      if (isClearing) {
        return;
      }
      if (toasts.length > 1) {
        setIsHidden(false);
      } else {
        setIsHidden(true);
      }
    }, [toasts, isClearing]);

    return (
      <Inline
        classNames={[
          "toast-stack__container",
          `toast-stack__container--${position}`
        ]}
        alignment={position}
        style={style}
      >
        <div className="toast-stack__close-all">
          {handleClearAll && (
            <Box className={isHidden ? "toast-stack-close-all--hidden" : ""}>
              <Tooltip content="Clear All Toasts">
                <CloseIconButton
                  onClick={() => {
                    setToasts(prev => handleClearAll(prev));
                    setIsClearing(true);
                    setIsHidden(true);
                  }}
                  size={IconSize.X_LARGE}
                />
              </Tooltip>
            </Box>
          )}
        </div>
        <Stack className={"toast-stack"} gap="100">
          {toasts.map((item, i) => (
            <Box
              key={item.id}
              className={`toast-stack__item toast-stack__item--slide-${slideDirection} ${
                item.visible ? "visible" : "hidden"
              }`}
              style={{ zIndex: toasts.length - i }}
              width="100"
              onTransitionEnd={() => {
                if (!item.visible) {
                  setToasts(prev => prev.filter(t => t.id !== item.id));
                }
              }}
            >
              <ToastNotification
                {...item.toast}
                width="100"
                handleClose={() => {
                  setToasts(prev =>
                    prev.map(t =>
                      t.id === item.id ? { ...t, visible: false } : t
                    )
                  );
                }}
              />
            </Box>
          ))}
        </Stack>
      </Inline>
    );
  }
);

ToastStack.displayName = "ToastStack";
