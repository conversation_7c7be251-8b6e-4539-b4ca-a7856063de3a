.toast-stack {
  display: flex;
  gap: 10px;
}

.toast-stack-close-all--hidden {
  opacity: 0;
  pointer-events: none;
  transition: 0.4s ease;
  display: none;
}

.toast-stack__item {
  opacity: 0;
  transition:
    transform 0.4s ease,
    opacity 0.4s ease;
}

.toast-stack--top-left {
  top: var(--spacing-200);
  left: var(--spacing-200);
  flex-direction: column;
}

.toast-stack--top-right {
  top: var(--spacing-200);
  right: var(--spacing-200);
  flex-direction: column;
}

.toast-stack--bottom-left {
  bottom: var(--spacing-200);
  left: var(--spacing-200);
  flex-direction: column-reverse;
}

.toast-stack--bottom-right {
  bottom: var(--spacing-200);
  right: var(--spacing-200);
  flex-direction: column-reverse;
}

/* for slide direction right */
.toast-stack__item--slide-right.hidden {
  transform: translateX(100%);
}

.toast-stack__item--slide-right.visible {
  transform: translateX(0);
  opacity: 1;
}

/* for slide direction left */
.toast-stack__item--slide-left.hidden {
  transform: translateX(-100%);
}

.toast-stack__item--slide-left.visible {
  transform: translateX(0);
  opacity: 1;
}
