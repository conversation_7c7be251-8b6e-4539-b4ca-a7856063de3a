import crypto from "crypto";
import { NextFunction, Request, Response } from "express";
import jwt, { JwtPayload } from "jsonwebtoken";
import { appConfig } from "src/helpers/appConfig.ts";
import { Logger } from "src/helpers/logger.ts";

const log = Logger("otai:SessionCookie");
const jwtPublicKey = appConfig.jwtPublicKey;
export interface AuthenticatedRequest extends Request {
  user?: string | JwtPayload;
}

export function decrypt(cookie: string): string {
  const [rawIV, cipherAndSig] = cookie.split("/");
  const [cipher] = cipherAndSig.split(":"); // ignoring signature for now
  const iv = Buffer.from(rawIV, "hex");
  const keyBuffer = Buffer.from(appConfig.sessionEncrypt, "hex");

  const decipher = crypto.createDecipheriv("aes-128-cbc", keyBuffer, iv);
  let dec = decipher.update(cipher, "hex", "utf-8");
  dec += decipher.final();
  return dec;
}

export function getUser(cookieHeader?: string): User | null {
  if (!cookieHeader) {
    return null;
  }
  const cookies = cookieHeader.split(";");
  for (const cookie of cookies) {
    if (cookie.includes("user_session=")) {
      const [, rawValue] = cookie.split("=");
      const value = decodeURIComponent(rawValue);
      const session = JSON.parse(decrypt(value));
      log.info("[checkCookie] session: ", session.user);
      return session?.user;
    }
  }
  return null;
}

export function sessionAuthentication(
  req: Request,
  res: Response,
  next: NextFunction
) {
  if (req.header("Authorization")) {
    sessionJWTMiddleware(req, res, next);
  } else if (req?.cookies?.user_session) {
    sessionCookieMiddleware(req, res, next);
  } else {
    log.warn(`[sessionJWTMiddleware] error: no token or cookie`);
    res.status(401).json({ message: "Unauthorized" });
  }
}

export function sessionCookieMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
) {
  if (req?.cookies?.user_session) {
    log.info(
      `[sessionCookieMiddleware] found a cookie: ${req.cookies.user_session}`
    );
    try {
      req.session = JSON.parse(decrypt(req.cookies.user_session));
      next();
      return;
    } catch (e) {
      log.error(`[sessionCookieMiddleware] error: ${e}`);
    }
  }
  res.status(401).json({ message: "Unauthorized" });
}

export function sessionJWTMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
) {
  const token = req.header("Authorization")?.replace("Bearer ", "").trim();

  if (!token) {
    log.warn(`[sessionJWTMiddleware] error: missing token`);
    return res.status(401).json({ message: "Unauthorized" });
  }

  try {
    (req as AuthenticatedRequest).user = jwt.verify(token, jwtPublicKey);
    next();
  } catch (err) {
    log.warn(`[sessionJWTMiddleware] error: ${err}`);
    return res.status(401).json({ message: "Unauthorized" });
  }
}
