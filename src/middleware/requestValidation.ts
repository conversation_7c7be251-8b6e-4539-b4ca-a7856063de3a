import { NextFunction, Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Logger } from "src/helpers/logger.ts";
import { ZodError, ZodIssue, z } from "zod";

const log = Logger("otai:server:requestValidationMiddleware");

/**
 * Middleware to validate request data against a schema
 * @param schema
 */
export function requestValidationMiddleware<T extends z.ZodTypeAny>(schema: T) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse(req.body);
      next();
    } catch (error) {
      if (error instanceof ZodError) {
        const errorMessages = error.errors.map((issue: ZodIssue) => ({
          message: `${issue.path.join(".")} is ${issue.message}`
        }));
        log.error("[validationMiddleware] error", errorMessages);
        res
          .status(StatusCodes.BAD_REQUEST)
          .json({ error: "Invalid data", details: errorMessages });
      } else {
        log.error("[validationMiddleware] error", error);
        res
          .status(StatusCodes.INTERNAL_SERVER_ERROR)
          .json({ error: "Internal Server Error" });
      }
    }
  };
}
