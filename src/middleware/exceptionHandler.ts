import { Handler, NextFunction, Request, Response } from "express";
import { Logger } from "src/helpers/logger.ts";

const log = Logger("otai:server:exceptionHandler");

type AsyncHandler = (
  req: Request,
  res: Response,
  next: NextFunction
) => Promise<void>;

function eh(fn: AsyncHandler): Handler {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(err => {
      if (err) {
        log.error("[eh]", err);
      }
      next(err);
    });
  };
}

export default eh;
