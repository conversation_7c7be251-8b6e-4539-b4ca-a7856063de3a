import react from "@vitejs/plugin-react-swc";
import wasm from "vite-plugin-wasm";
import viteTsConfigPaths from "vite-tsconfig-paths";
import { coverageConfigDefaults, defineConfig } from "vitest/config";

// https://vitejs.dev/config/
export default defineConfig({
  base: "/ai",
  css: {
    preprocessorOptions: {
      scss: {
        api: "modern-compiler"
      }
    }
  },
  test: {
    coverage: {
      provider: "v8",
      reporter: ["text", "lcov", "html"],
      exclude: [...coverageConfigDefaults.exclude, "scripts/**"]
    },
    environment: "jsdom",
    setupFiles: "./src/tests/setup.ts"
  },
  resolve: {
    alias: {
      "@helpers": "/src/helpers",
      "@fermions": "/src/components/fermions/index.ts",
      "@atoms": "/src/components/atoms",
      "@molecules": "/src/components/molecules",
      "@organisms": "/src/components/organisms",
      "@templates": "/src/components/templates",
      "@components": "/src/components",
      "@pages": "/src/pages",
      "@src": "/src",
      "@assets": "/assets"
    }
  },
  build: {
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          onetheme: ["@oneteam/onetheme"],
          react: ["react", "react-dom"]
        }
      }
    }
  },
  esbuild: {
    supported: {
      "top-level-await": true //browsers can handle top-level-await features
    }
  },
  optimizeDeps: {
    exclude: ["@oneteam/onetheme"]
  },
  server: {
    open: "http://localhost:8000/ai",
    port: 4000,
    hmr: {
      overlay: false
    }
  },
  plugins: [react(), viteTsConfigPaths(), wasm()]
});
