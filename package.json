{"name": "@automerge/automerge-repo-sync-server", "version": "0.2.8", "main": "src/index.js", "license": "MIT", "type": "module", "engines": {"node": ">=22.12.0 <23", "npm": ">=10.9.2 <11"}, "scripts": {"start": "tsx ./src/index.ts", "start:dev": "tsx --env-file=.env.local ./src/index.ts", "build": "tsc", "prettier": "prettier -c .", "test": "vitest", "test:ci": "vitest run --coverage", "prepare": "husky"}, "dependencies": {"@automerge/automerge": "^2.2.8", "@automerge/automerge-repo": "2.0.6", "@automerge/automerge-repo-network-websocket": "2.0.6", "@automerge/automerge-repo-storage-nodefs": "2.0.6", "@azure/monitor-opentelemetry": "^1.9.0", "@types/cookie-parser": "^1.4.7", "@types/jsonwebtoken": "^9.0.7", "@types/lodash": "^4.17.15", "cookie-parser": "^1.4.7", "debug": "^4.3.7", "dotenv": "^16.4.5", "express": "^5.0.0", "http-status-codes": "^2.3.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "nanoid": "^5.1.0", "pg": "^8.13.1", "tsx": "^4.19.2", "ws": "^8.18.0", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.19.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/debug": "^4.1.12", "@types/express": "^5.0.0", "@types/node": "^22.9.0", "@types/pg": "^8.11.10", "@types/supertest": "^6.0.2", "@types/ws": "^8.5.14", "@vitest/coverage-v8": "^3.0.3", "eslint": "^9.19.0", "globals": "^16.0.0", "googleapis": "^148.0.0", "husky": "^9.1.7", "mocha": "^11.0.0", "prettier": "^3.3.3", "supertest": "^7.0.0", "ts-node": "^10.9.2", "typescript": "^5.6.3", "typescript-eslint": "^8.22.0", "vitest": "^3.0.3"}, "lint-staged": {"*.{js,ts,tsx,jsx,json,html}": ["prettier --write"], "*.{js,ts,tsx,jsx,}": ["eslint --fix --max-warnings 0"]}}