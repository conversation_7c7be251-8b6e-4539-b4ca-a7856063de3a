{"name": "@oneteam/onetheme", "private": true, "version": "0.0.105", "type": "module", "types": "dist/index.d.ts", "files": ["dist"], "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.umd.cjs"}, "./index.css": {"import": "./dist/index.css", "require": "./dist/index.css"}}, "engines": {"node": ">=22.12.0 <23", "npm": ">=10.9.2"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "tsc:watch": "tsc --noEmit --watch", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest watch", "test:ci": "vitest run --coverage", "postinstall": "npm outdated || :", "storybook": "npm run storybook:otai", "storybook:otai": "cross-env VITE_APP_THEME=otai storybook dev -p 6006", "storybook:oneteam": "cross-env VITE_APP_THEME=oneteam storybook dev -p 6006", "build-storybook": "storybook build", "build-storybook:otai": "VITE_APP_THEME=otai storybook build -o storybook-static/otai", "build-storybook:oneteam": "VITE_APP_THEME=oneteam storybook build -o storybook-static/oneteam", "test-storybook": "test-storybook", "prepare": "npm run build && husky", "build:otai": "cross-env VITE_APP_THEME=otai npm run build", "build:oneteam": "cross-env VITE_APP_THEME=oneteam npm run build", "pack:otai": "cross-env VITE_APP_THEME=otai npm pack", "pack:oneteam": "cross-env VITE_APP_THEME=oneteam npm pack"}, "dependencies": {"@atlaskit/pragmatic-drag-and-drop": "^1.4.0", "@atlaskit/pragmatic-drag-and-drop-hitbox": "^1.0.3", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "lodash": "^4.17.21", "react-date-range": "^2.0.1", "react-loader-spinner": "^6.1.6", "use-onclickoutside": "^0.4.1"}, "peerDependencies": {"@hookform/resolvers": "^3.10.0", "@monaco-editor/react": "^4.6.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.1", "zod": "^3.23.8"}, "devDependencies": {"@chromatic-com/storybook": "^3.0.0", "@eslint/compat": "^1.2.4", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.13.0", "@monaco-editor/react": "^4.6.0", "@storybook/addon-coverage": "^1.0.4", "@storybook/addon-docs": "^8.3.6", "@storybook/addon-essentials": "^8.3.6", "@storybook/addon-interactions": "^8.3.6", "@storybook/addon-links": "^8.3.6", "@storybook/blocks": "^8.3.5", "@storybook/react": "^8.3.6", "@storybook/react-vite": "^8.3.5", "@storybook/test": "^8.3.6", "@storybook/test-runner": "^0.23.0", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@trivago/prettier-plugin-sort-imports": "^5.0.0", "@types/eslint__js": "^9.0.0", "@types/lodash": "^4.17.13", "@types/node": "^22.7.7", "@types/react": "^18.3.23", "@types/react-date-range": "^1.4.9", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^8.10.0", "@typescript-eslint/parser": "^8.10.0", "@vitejs/plugin-react-swc": "^3.7.1", "@vitest/coverage-v8": "^3.1.4", "eslint": "^9.0.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.13", "eslint-plugin-storybook": "^0.12.0", "global-jsdom": "^26.0.0", "globals": "^16.0.0", "husky": "^9.1.6", "jsdom": "^26.1.0", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "rimraf": "^6.0.1", "sass": "^1.80.3", "storybook": "^8.3.6", "typescript": "^5.6.3", "typescript-eslint": "^8.10.0", "vite": "^6.0.2", "vite-plugin-dts": "^4.2.4", "vite-tsconfig-paths": "^5.0.1", "vitest": "^3.1.4"}, "overrides": {"eslint-plugin-storybook@^0.9.0": {"@typescript-eslint/utils@5.62.0": {"eslint": "^9.0.0"}}}, "lint-staged": {"*.{js,ts,tsx,jsx,json,html}": ["prettier --write"], "*.{js,ts,tsx,jsx,}": ["eslint --fix --max-warnings 0"]}}