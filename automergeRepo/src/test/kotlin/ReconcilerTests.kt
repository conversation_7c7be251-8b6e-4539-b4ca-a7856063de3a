import automergeRepo.src.main.kotlin.printDoc
import automergeRepo.src.main.kotlin.reconciler.reconcile
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import org.automerge.Document
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration.Identifier
import services.oneteam.ai.shared.domains.workspace.OrderedMap
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.helpers.CustomNanoId
import java.time.Instant
import kotlin.test.Test
import kotlin.test.assertEquals

class ReconcilerTests {

    @Serializable
    data class ZataClazz(val foo: String = "bar")

    @Serializable
    data class TestClass(
        val someValue: String = "test",
        val someOtherValue: String = "test2",
        val someNestedValue: ZataClazz = ZataClazz(),
        val someList: MutableList<String> = mutableListOf("item1", "item2", "item3"),
        val someNestedList: MutableList<MutableList<String>> = mutableListOf(
            mutableListOf("nested1", "nested2"), mutableListOf("nested3", "nested4")
        ),
        val someListOfClass: MutableList<ZataClazz> = mutableListOf(
            ZataClazz("item1"), ZataClazz("item2"), ZataClazz("item3")
        )
    )

    val testSerializer = Json { encodeDefaults = true; prettyPrint = true }

    @Test
    fun `basic init reconcile`() {
        val test = TestClass()

        val doc = Document()

        doc.reconcile(test)

        val expected = testSerializer.encodeToJsonElement(test)
        val actual = Json.parseToJsonElement(printDoc(doc))

        assertEquals(expected, actual)
    }

    @Test
    fun `workspace init reconcile`() {
        val workspaceForJson = createDefaultWorkspaceDocument()

        val doc = Document()

        doc.reconcile(workspaceForJson)

        val expected = testSerializer.encodeToJsonElement(workspaceForJson)
        val actual = Json.parseToJsonElement(printDoc(doc))

        assertEquals(expected, actual)
    }

    @Test
    fun `workspace change reconcile`() {
        val workspaceForJson = createDefaultWorkspaceDocument()

        val doc = Document()

        doc.reconcile(workspaceForJson)

        assertEquals(testSerializer.encodeToJsonElement(workspaceForJson), Json.parseToJsonElement(printDoc(doc)))

        workspaceForJson.flows.entities[FlowConfiguration.Id("key")] = FlowConfiguration.ForJson(
            id = FlowConfiguration.Id("test"),
            name = FlowConfiguration.Name("test"),
            description = FlowConfiguration.Description("desc"),
            steps = mutableMapOf(
                FlowConfiguration.Step.Id(CustomNanoId.generate()) to FlowConfiguration.Step(
                    id = FlowConfiguration.Step.Id(CustomNanoId.generate()),
                    variant = FlowConfiguration.Step.Variant.ACTION,
                    name = "Test Step",
                    properties = FlowConfiguration.Step.Properties()
                )
            ),
            triggers = emptyMap(),
            metadata = EntityMetadata(Instant.now(), Instant.now())
        )

        doc.reconcile(workspaceForJson, Workspace.ForJson::class)

        assertEquals(testSerializer.encodeToJsonElement(workspaceForJson), Json.parseToJsonElement(printDoc(doc)))
    }
}


fun createDefaultWorkspaceDocument(): Workspace.ForJson {
    val rootFoundationConfiguration = createRootFoundationConfiguration()
    val workspaceForJson = Workspace.ForJson(
        id = Workspace.Id(0),
        name = Workspace.Name(""),
        key = Workspace.Key(""),
        description = Workspace.Description(""),
        foundations = OrderedMap(listOf(rootFoundationConfiguration)),
        forms = emptyMap(),
        flows = OrderedMap<FlowConfiguration.Id, FlowConfiguration.ForJson>(emptyList()),
        series = emptyMap(),
        labels = emptyMap(),
        errors = emptyList(),
        metadata = EntityMetadata(Instant.now(), Instant.now())
    )
    return workspaceForJson
}

fun createRootFoundationConfiguration(): FoundationConfiguration.ForApi {
    return create(
        FoundationConfiguration.ForCreate(
            name = FoundationConfiguration.Name("root foundation"),
            description = null,
            relationship = FoundationConfiguration.Relationship.OneToMany,
            Identifier("IDENTIFIER1"),
        )
    )
}

fun create(foundationConfiguration: FoundationConfiguration.ForCreate): FoundationConfiguration.ForApi {
    val now = Instant.now()
    return FoundationConfiguration.ForApi(
        id = FoundationConfiguration.Id(CustomNanoId.generate()),
        name = foundationConfiguration.name,
        description = foundationConfiguration.description,
        relationship = foundationConfiguration.relationship,
        metadata = EntityMetadata(
            createdAt = now, updatedAt = now
        ),
        Identifier("IDENTIFIER1"),
    )
}