package services.oneteam.ai.shared.domains.tenant

import io.kotest.common.runBlocking
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.Fixtures
import services.oneteam.ai.shared.testing.TestPostgresDatabase
import kotlin.test.assertNotNull

class TenantRepositoryPostgresTest {
    val database = TestPostgresDatabase

    @Test
    fun `should find by origin`() {
        runBlocking {
            Fixtures(database).initialise()
            // prepare
            val tenantRepository = TenantRepository(TestPostgresDatabase.database)
            // perform
            val tenant = tenantRepository.getByOriginUrl("http://tenant1")
            // verify
            assertNotNull(tenant)
            tenant.name shouldBe "tenant1"
            println("Tenant: $tenant")
        }
    }
}