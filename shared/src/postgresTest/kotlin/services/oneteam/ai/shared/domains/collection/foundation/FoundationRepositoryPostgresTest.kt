package services.oneteam.ai.shared.domains.collection.foundation

import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.withContext
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.Fixtures
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.testing.TestPostgresDatabase
import services.oneteam.ai.shared.withTenantTransactionScope

class FoundationRepositoryPostgresTest {
    val database = TestPostgresDatabase

    @Test
    fun testGetFoundationHierarchy() = runTest {
        // prepare
        val fixtures = Fixtures(database).initialise()

        withContext(RequestContext(tenant = fixtures.tenant1, principal = fixtures.tenant1User1Session)) {
            withTenantTransactionScope {
                val workspace = fixtures.workspaceRepository.getByKey(fixtures.Workspace1_Key)!!
                val grandParent = fixtures.foundationRepository.getRoot(workspace.id.value)
                val hierarchies = mutableListOf<List<Long>>()
                repeat(3) { parentIndex ->
                    val parent = fixtures.foundationRepository.create(
                        fixtures.tenant1,
                        Foundation.ForCreate(
                            workspaceId = Workspace.Id(workspace.id.value),
                            name = Foundation.Name("foundation-$parentIndex"),
                            key = Foundation.Key("foundation-$parentIndex"),
                            foundationConfigurationId = FoundationConfiguration.Id("config-$parentIndex"),
                            parentId = Foundation.Id(grandParent.id.value),
                        )
                    )
                    repeat(2) { index ->
                        val child = fixtures.foundationRepository.create(
                            fixtures.tenant1,
                            Foundation.ForCreate(
                                workspaceId = Workspace.Id(workspace.id.value),
                                name = Foundation.Name("foundation-$parentIndex-$index"),
                                key = Foundation.Key("foundation-$parentIndex-$index"),
                                foundationConfigurationId = FoundationConfiguration.Id("config-$parentIndex-$index"),
                                parentId = Foundation.Id(parent.id.value),
                            )
                        )
                        hierarchies.add(listOf(grandParent.id.value, parent.id.value, child.id.value))
                    }
                }

                hierarchies.size.shouldBe(6)
                hierarchies.forEachIndexed { index, hierarchy ->
                    hierarchies[index].size.shouldBe(3)
                    val result = fixtures.foundationRepository.getHierarchyForFoundation(
                        hierarchies[index][2]
                    )

                    result.shouldNotBeNull()
                    result[0].id.value.shouldBe(grandParent.id.value)
                    result[1].id.value.shouldBe(hierarchies[index][1])
                    result[2].id.value.shouldBe(hierarchies[index][2])
                }
            }
        }
    }
}