package services.oneteam.ai.shared

import kotlinx.coroutines.*
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.testing.TestPostgresDatabase
import kotlin.random.Random
import kotlin.test.assertEquals
import kotlin.time.measureTime

class TenantScopeConcurrencyTest {
    private val database = TestPostgresDatabase

    @Test
    fun `test withTenantScope isolates tenant contexts across coroutines`() = runTest {
        val fixtures = Fixtures(database).initialise()

        val tenant1 = fixtures.tenant1
        val tenant2 = fixtures.tenant2

        coroutineScope {
            val job1 = async {
                withContext(RequestContext(tenant = tenant1)) {
                    assertThat(TransactionManager.currentOrNull()).isNull() // Ensure no transaction exists at the start
                    withTenantTransactionScope { tenant ->
                        assertThat(TransactionManager.currentOrNull()).isNotNull() // Ensure no transaction exists at the start
                        assertEquals(tenant1.id, tenant.id)
                        delay(Random.nextLong(10, 100))
                        withTenantTransactionScope { tenant ->
                            delay(Random.nextLong(10, 100))
                            assertEquals(tenant1.id, tenant.id)
                            withTenantTransactionScope { tenant ->
                                delay(Random.nextLong(10, 100))
                                assertEquals(tenant1.id, tenant.id)
                            }
                        }
                    }
                    assertThat(TransactionManager.currentOrNull()).isNull() // Ensure no transaction exists at the end
                }
            }

            val job2 = async {
                withContext(RequestContext(tenant = tenant2)) {
                    assertThat(TransactionManager.currentOrNull()).isNull() // Ensure no transaction exists at the start
                    withTenantTransactionScope { tenant ->
                        assertEquals(tenant2.id, tenant.id)
                        delay(Random.nextLong(10, 100))
                        withTenantTransactionScope { tenant ->
                            assertEquals(tenant2.id, tenant.id)
                            delay(Random.nextLong(10, 100))
                            withTenantTransactionScope { tenant ->
                                assertEquals(tenant2.id, tenant.id)
                                delay(Random.nextLong(10, 100))
                            }
                        }
                    }
                    assertThat(TransactionManager.currentOrNull()).isNull() // Ensure no transaction exists at the end
                }
            }

            job1.await()
            job2.await()
        }

    }


    @Test
    fun `test withTenantScope under high concurrency`() = runTest {
        val fixtures = Fixtures(database).initialise()

        val tenant1 = fixtures.tenant1
        val tenant2 = fixtures.tenant2

        val startedList = mutableListOf<String>()
        val finishedList = mutableListOf<String>()
        val stoppedList = mutableListOf<String>()
        val timerList = mutableListOf<String>()


        val iterations = 100 // Number of coroutines to run
        val jobs = mutableListOf<Deferred<Unit>>()

        repeat(iterations) { i ->
            jobs.add(async {
                val tenant = if (i % 2 == 0) tenant1 else tenant2
                withContext(RequestContext(tenant = tenant)) {
                    val timeTaken = measureTime {
                        assertThat(TransactionManager.currentOrNull()).isNull() // Ensure no transaction exists at the start
                        delay(Random.nextLong(10, 100)) // Simulate some processing time
                        startedList.add("Coroutine $i for tenant ${tenant.id} starting")

                        withTenantTransactionScope { currentTenant ->
                            assertThat(currentTenant.id).isEqualTo(tenant.id)
                            delay(Random.nextLong(10, 100)) // Simulate some processing time
                            withTenantTransactionScope { currentTenant ->
                                assertThat(currentTenant.id).isEqualTo(tenant.id)
                                delay(Random.nextLong(10, 100)) // Simulate some processing time
                                withTenantTransactionScope { currentTenant ->
                                    finishedList.add("Coroutine $i for tenant ${tenant.id} inside nested scope")
                                    assertThat(currentTenant.id).isEqualTo(tenant.id)
                                    delay(Random.nextLong(10, 100)) // Simulate some processing time
                                }
                            }
                        }
                        assertThat(TransactionManager.currentOrNull()).isNull() // Ensure no transaction exists at the end
                        stoppedList.add("Coroutine $i for tenant ${tenant.id} completed") // this doesn't print
                    }
                    // Log the time taken for each coroutine
                    timerList.add("Coroutine $i for tenant ${tenant.id} took $timeTaken ms") // this doesn't print
                }
                return@async
            })
        }

        jobs.awaitAll()

        // Verify results - asserts done in loops above
        println("All coroutines completed successfully")
        assertThat(startedList).hasSize(iterations)
        assertThat(finishedList).hasSize(iterations)
        assertThat(stoppedList).hasSize(iterations)
        assertThat(timerList).hasSize(iterations)
    }

}