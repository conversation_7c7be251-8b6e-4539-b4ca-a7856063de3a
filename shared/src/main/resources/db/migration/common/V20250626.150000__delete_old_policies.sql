DROP POLICY IF EXISTS workspace_isolation_policy_select ON "foundations";
DROP POLICY IF EXISTS workspace_isolation_policy_update ON "foundations";
DROP POLICY IF EXISTS workspace_isolation_policy_delete ON "foundations";
DROP POLICY IF EXISTS workspace_isolation_policy_select ON "workspaces";
DROP POLICY IF EXISTS workspace_isolation_policy_update ON "workspaces";
DROP POLICY IF EXISTS workspace_isolation_policy_delete ON "workspaces";
DROP POLICY IF EXISTS workspace_isolation_policy_select ON "workspace_versions";
DROP POLICY IF EXISTS workspace_isolation_policy_update ON "workspace_versions";
DROP POLICY IF EXISTS workspace_isolation_policy_delete ON "workspace_versions";
DROP POLICY IF EXISTS workspace_isolation_policy_select ON "forms";
DROP POLICY IF EXISTS workspace_isolation_policy_update ON "forms";
DROP POLICY IF EXISTS workspace_isolation_policy_delete ON "forms";
DROP POLICY IF EXISTS workspace_isolation_policy_select ON "form_versions";
DROP POLICY IF EXISTS workspace_isolation_policy_update ON "form_versions";
DROP POLICY IF EXISTS workspace_isolation_policy_delete ON "form_versions";
DROP POLICY IF EXISTS workspace_isolation_policy_select ON "flow_executions";
DROP POLICY IF EXISTS workspace_isolation_policy_update ON "flow_executions";
DROP POLICY IF EXISTS workspace_isolation_policy_delete ON "flow_executions";
