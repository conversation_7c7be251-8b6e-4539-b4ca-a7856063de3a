{"email": {"type": "text", "properties": {"regex": "/^\\S+@\\S+$/"}}, "formConfiguration.minimal": {"type": "json", "properties": {"items": [{"identifier": "id", "type": "text", "properties": {"required": true}}, {"identifier": "seriesId", "type": "text"}, {"identifier": "name", "type": "text", "properties": {"required": true}}, {"identifier": "key", "type": "text", "properties": {"required": true}}]}}, "form.minimal": {"type": "json", "items": [{"identifier": "id", "type": "number", "properties": {"required": true}}, {"identifier": "formConfiguration", "type": "formConfiguration.minimal", "properties": {"required": true}}, {"identifier": "foundationId", "type": "number", "properties": {"required": true}}, {"identifier": "foundation", "type": "foundation.minimal", "properties": {"required": true}}, {"identifier": "documentId", "type": "text"}, {"identifier": "intervalId", "type": "text"}]}, "seriesInterval": {"type": "json", "items": [{"identifier": "id", "type": "text"}, {"identifier": "name", "type": "text"}]}, "seriesConfiguration": {"type": "json", "properties": {"items": [{"identifier": "id", "type": "text", "properties": {"required": true}}, {"identifier": "name", "type": "text", "properties": {"required": true}}, {"identifier": "intervals", "type": "list", "properties": {"items": {"type": "json", "properties": {"items": {"type": "seriesInterval", "properties": {"required": true}}}}}}]}}, "seriesInterval.minimal": {"type": "json", "items": [{"identifier": "id", "type": "text"}, {"identifier": "name", "type": "text"}, {"identifier": "next", "type": "seriesInterval"}, {"identifier": "previous", "type": "seriesInterval"}]}, "foundationConfiguration.minimal": {"type": "json", "properties": {"items": [{"identifier": "id", "type": "text", "properties": {"required": true}}, {"identifier": "name", "type": "text", "properties": {"required": true}}, {"identifier": "description", "type": "text", "properties": {"required": true}}, {"identifier": "relationship", "type": "select", "properties": {"required": true, "options": [{"value": "OneToMany", "label": "OneToMany"}]}}, {"identifier": "parentId", "type": "text", "properties": {"required": false}}]}}, "foundation.minimal": {"type": "json", "items": [{"identifier": "id", "type": "number", "properties": {"required": true}}, {"identifier": "name", "type": "text", "properties": {"required": true}}, {"identifier": "key", "type": "text", "properties": {"required": true}}, {"identifier": "foundationConfiguration", "type": "foundationConfiguration.minimal", "properties": {"required": true}}, {"identifier": "parentId", "type": "text"}]}, "question": {"type": "json", "properties": {"items": [{"identifier": "id", "type": "number"}, {"identifier": "type", "type": "text"}, {"identifier": "text", "type": "text"}, {"identifier": "identifier", "type": "text"}, {"identifier": "properties", "type": "json", "properties": {"isUnstructured": true}}, {"identifier": "answer", "type": "unknown"}]}}}