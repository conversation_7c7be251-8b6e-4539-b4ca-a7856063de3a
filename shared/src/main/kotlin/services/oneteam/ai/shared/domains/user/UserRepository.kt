package services.oneteam.ai.shared.domains.user

import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.json.extract
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.SortableFields
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.extensions.paginate

data class UserSearchCriteria(
    val searchTerm: String?
) {
    companion object {
        // empty companion object so we can add extension functions
    }
}

class UserRepository(private val checks: Checks) {
    companion object {
        val SORTABLE_FIELDS = SortableFields(
            mapOf(
                "email" to UserSchema.email
            ),
        )
    }

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    fun findOne(id: Long): UserEntity {
        return checks.exists(getById(id)) { "User $id not found" }
    }

    fun getById(id: Long): UserEntity? {
        return UserEntity.findById(id)
    }

    fun create(tenant: Tenant, user: User.ForCreate): UserEntity {
        return UserEntity.new {
            email = user.email
            tenantId = tenant.id
            properties = user.properties
        }
    }

    fun updateOrCreate(tenant: Tenant, user: User.ForCreate): UserEntity {
        val existingUser = UserEntity.findById(user.id.value)
        if (existingUser == null) {
            return UserEntity.new(user.id.value) {
                email = user.email
                tenantId = tenant.id
                properties = user.properties
            }
        }
        if (existingUser.email != user.email || existingUser.properties == null || existingUser.properties?.firstName != user.properties?.firstName || existingUser.properties?.lastName != user.properties?.lastName) {
            return UserEntity.findByIdAndUpdate(existingUser.id.value) {
                existingUser.email = user.email
                existingUser.properties = user.properties
            }!!
        }
        return existingUser
    }

    fun getAll(): List<UserEntity> {
        return UserEntity.all().toList()
    }

    fun searchByCriteria(pageRequest: PageRequest, userSearchCriteria: UserSearchCriteria): Page<UserEntity> {
        return userSearchCriteria.toQuery()
            .paginate(pageRequest, UserRepository.SORTABLE_FIELDS) {
                UserEntity.wrapRow(it)
            }
    }

}

fun UserSearchCriteria.toQuery(): Query {
    val query = UserSchema.selectAll()
    if (searchTerm?.isNotBlank() == true) {
        val lowercaseSearchTerm = "%${searchTerm.lowercase()}%"
        query.andWhere {
            (UserSchema.email.lowerCase() like lowercaseSearchTerm) or
                    (UserSchema.properties.extract<String>("firstName").lowerCase() like lowercaseSearchTerm) or
                    (UserSchema.properties.extract<String>("lastName").lowerCase() like lowercaseSearchTerm)
        }
    }

    return query
}
