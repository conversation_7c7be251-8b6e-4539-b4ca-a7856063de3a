package services.oneteam.ai.shared.domains.workspace

import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.selectAll
import services.oneteam.ai.shared.domains.*
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.extensions.paginate

class WorkspaceVersionRepository() {
    companion object {
        val SORTABLE_FIELDS = SortableFields(
            mapOf(
                "id" to WorkspaceVersions.id
            )
        )
    }

    fun search(
        pageRequest: PageRequest,
        workspaceId: Workspace.Id,
        versionId: WorkspaceVersion.Id? = null,
    ): Page<WorkspaceVersionEntity> {
        val query = WorkspaceVersions.selectAll()
        query.andWhere { WorkspaceVersions.workspaceId eq workspaceId.value }
        if (versionId != null) {
            query.andWhere { WorkspaceVersions.id eq versionId.value }
        }
        return query.paginate(pageRequest, SORTABLE_FIELDS) {
            WorkspaceVersionEntity.wrapRow(
                it
            )
        }
    }

    fun searchLatestVersion(
        workspaceId: Workspace.Id
    ): WorkspaceVersionEntity? {
        return search(PageRequest(0, 1, "", Sort(listOf(SortField("id", Sort.DESC)))), workspaceId, null)
            .items.firstOrNull()
    }

    fun create(tenant: Tenant, workspaceVersion: WorkspaceVersion.ForCreate): WorkspaceVersionEntity {
        val dao = WorkspaceVersionEntity.new {
            workspaceId = workspaceVersion.workspaceId.value
            configuration = workspaceVersion.configuration
            tenantId = tenant.id
        }
        return dao
    }

}