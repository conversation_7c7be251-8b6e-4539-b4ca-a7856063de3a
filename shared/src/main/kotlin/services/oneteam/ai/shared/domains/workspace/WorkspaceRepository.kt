package services.oneteam.ai.shared.domains.workspace

import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.database.DatabaseUtils.nextValueOf
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.SortableFields
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.domains.user.UserRepository
import services.oneteam.ai.shared.extensions.paginate

data class WorkspaceSearchCriteria(
    val searchTerm: String? = "", // search by name
    val workspaceId: Workspace.Id? = null
) {
    companion object {
        fun byId(id: Workspace.Id): WorkspaceSearchCriteria {
            return WorkspaceSearchCriteria(workspaceId = id)
        }
    }
}

class WorkspaceRepository(private val checks: Checks, private val userRepository: UserRepository) {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    companion object {
        val SORTABLE_FIELDS = SortableFields(
            mapOf(
                "key" to Workspaces.key,
                "name" to Workspaces.name,
                "id" to Workspaces.id
            )
        )
    }

    fun findOne(id: Workspace.Id): Workspace.ForApi {
        return checks.exists(getById(id)) { "Workspace $id not found" }
    }

    fun findOne(key: Workspace.Key): Workspace.ForApi {
        return checks.exists(getByKey(key)) { "Workspace $key not found" }
    }

    fun getById(id: Workspace.Id): Workspace.ForApi? {
        return WorkspaceEntity.find { Workspaces.id eq id.value and (Workspaces.deleted eq false) }.singleOrNull()
            ?.toDTO()
    }

    fun getByKey(key: Workspace.Key): Workspace.ForApi? {
        return WorkspaceEntity.find { Workspaces.key eq key.value and (Workspaces.deleted eq false) }.singleOrNull()
            ?.toDTO()
    }

    fun create(tenant: Tenant, workspace: Workspace.ForCreate, forUserId: User.Id): Workspace.ForApi {
        val user = userRepository.findOne(forUserId.value)

        // get the next ID for the workspace so we can provide it in the insert - then we don't need to do a select after insert to get the ID for the workspace_users insert
        val wId = TransactionManager.current().nextValueOf("workspaces_id_seq")

        // insert the workspace_user row first so the insert into workspaces will not fail due to RLS
        // foreign key has been removed for now, we can add it back later if we find a way to avoid insert using returning *
        val workspaceUserMapping = WorkspaceUserEntity.new {
            // use the ID we got from the sequence
            workspaceId = EntityID(wId, Workspaces)
            userId = user.id
            tenantId = tenant.id
            accessLevel = WorkspaceUser.AccessLevel.entries // add all access levels for creator
            status = WorkspaceUser.Status.ACTIVE
        }.apply {
            flush()
        }
        logger.debug(
            "inserted workspace_user {} for user {} in workspace {}",
            workspaceUserMapping.accessLevel,
            user.id,
            wId
        )

        // now, with the workspace_uses row flushed the workspace insert can succeed
        val dao = WorkspaceEntity.new(wId) { // provide the ID
            name = workspace.name.value
            key = workspace.key.value
            description = workspace.description?.value
            documentId = workspace.documentId?.value
            tenantId = tenant.id
        }.apply {
            flush()
        }
        logger.debug("inserted workspace {} for tenant {} with name {}", wId, tenant.id, workspace.name.value)
        return dao.toDTO()


    }


    fun update(workspace: Workspace.ForUpdate): Workspace.ForApi {
        return WorkspaceEntity.findByIdAndUpdate(workspace.id.value) {
            it.name = workspace.name.value
            it.documentId = workspace.documentId?.value
            it.description = workspace.description?.value
        }!!.toDTO()
    }

    fun updateDetails(workspace: Workspace.ForUpdateDetails): Workspace.ForApi {
        return WorkspaceEntity.findByIdAndUpdate(workspace.id.value) {
            it.name = workspace.name.value
            it.description = workspace.description?.value
        }!!.toDTO()
    }

    fun delete(id: Workspace.Id) {
        WorkspaceEntity.findByIdAndUpdate(id.value) {
            it.deleted = true
        }
    }

    fun getAll(): List<Workspace.ForApi> {
        //make the iterator non-lazy
        return WorkspaceEntity.find { Workspaces.deleted eq false }.map { it.toDTO() }
    }

    fun searchByCriteria(
        pageRequest: PageRequest,
        searchCriteria: WorkspaceSearchCriteria
    ): Page<Workspace.ForApi> {
        searchCriteria.toQuery().paginate(pageRequest, SORTABLE_FIELDS) {
            WorkspaceEntity.wrapRow(it)
        }.let {
            return Page(
                pageRequest,
                it.total,
                it.items.map { workspaceEntity -> workspaceEntity.toDTO() }
            )
        }
    }
}

/**
 * Workspaces table is restricted by RLS to only those who are linked to a workspace via workspace_users.
 */
fun WorkspaceSearchCriteria.toQuery(): Query {
    val query = Workspaces.selectAll().andWhere { Workspaces.deleted eq false }
    if (searchTerm?.isNotBlank() == true) {
        query.andWhere { Workspaces.name.lowerCase() like "%${searchTerm.lowercase()}%" }
    }

    return query
}
