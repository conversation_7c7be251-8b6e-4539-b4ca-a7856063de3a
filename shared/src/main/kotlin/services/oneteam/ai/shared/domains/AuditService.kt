package services.oneteam.ai.shared.domains

import org.jetbrains.exposed.dao.EntityChangeType
import org.jetbrains.exposed.dao.EntityHook
import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.database.PRINCIPAL_KEY
import services.oneteam.ai.shared.database.RLS_TENANT_KEY
import services.oneteam.ai.shared.database.currentUtc

class AuditService(val enabled: Boolean) {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    init {
        if (enabled) {
            logger.info("AuditService enabled")
            EntityHook.subscribe { action ->
                recordChange(action.entityId.table.tableName, action.entityId.value as Long, action.changeType)
            }
        } else {
            logger.info("AuditService disabled")
        }
    }

    fun recordChange(tableName: String, entityId: Long, changeType: EntityChangeType) {
        recordAuditLog(
            tableName,
            entityId,
            changeType.name,
        )
    }

    /**
     * Requires the current principal and tenant to be set in the RequestContext
     */
    fun recordAuditLog(
        tableName: String,
        entityId: Long,
        action: String,
    ) {
        val conn = TransactionManager.current().connection
        val query = """
            INSERT INTO audits (table_name, table_id, action, user_id, tenant_id, created_at, "value")
            VALUES (?, ?, ?, 
            CASE WHEN LENGTH(CURRENT_SETTING('$PRINCIPAL_KEY', TRUE)) > 0 THEN CURRENT_SETTING('$PRINCIPAL_KEY', TRUE)::int ELSE null END, 
            CURRENT_SETTING('$RLS_TENANT_KEY', TRUE)::int,
            ?, 
            (SELECT row_to_json(x) FROM $tableName x WHERE id = ?))
        """

        logger.trace("Recording audit log: {}", query)
        logger.trace("tableName: {}, entityId: {}, action: {}", tableName, entityId, action)

        val statement = conn.prepareStatement(query, false)
        statement[1] = tableName
        statement[2] = entityId
        statement[3] = action
        statement[4] = currentUtc()
        statement[5] = entityId
        statement.executeUpdate()
    }

}