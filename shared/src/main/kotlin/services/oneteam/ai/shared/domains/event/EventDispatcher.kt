package services.oneteam.ai.shared.domains.event

import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import kotlin.coroutines.CoroutineContext

class EventDispatcher(private val eventQueue: EventQueue, dispatcher: CoroutineDispatcher) : CoroutineScope {
    override val coroutineContext = dispatcher

    private val listeners: MutableSet<EventListener> = mutableSetOf()
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    suspend fun enqueueEvent(event: Event.ForJson, context: CoroutineContext) {
        eventQueue.push(event)
        dispatchEvents(context)
    }

    fun register(listener: EventListener) {
        listeners.add(listener)
    }

    fun unregister(listener: EventListener) {
        listeners.remove(listener)
    }

    private fun dispatchEvents(context: CoroutineContext) {
        launch(coroutineContext + context) {
            while (eventQueue.size() > 0) {
                val event = eventQueue.poll()
                    ?: return@launch

                logger.info("Dispatching event: {}", event)

                listeners.forEach {
                    logger.info("Notifying listener: {}", it::class)
                    it.onEvent(event)
                }
            }
        }
    }
}