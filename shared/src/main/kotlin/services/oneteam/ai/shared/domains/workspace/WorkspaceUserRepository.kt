package services.oneteam.ai.shared.domains.workspace

import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.json.extract
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.SortableFields
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.domains.user.UserRepository
import services.oneteam.ai.shared.domains.user.UserSchema
import services.oneteam.ai.shared.extensions.paginate

data class WorkspaceUserSearchCriteria(
    val workspaceId: Workspace.Id,
    val searchTerm: String?
) {
    companion object {
        // empty companion object so we can add extension functions
    }
}

class WorkspaceUserRepository(
    private val checks: Checks,
    val workspaceRepository: WorkspaceRepository,
    val userRepository: UserRepository
) {

    companion object {
        val SORTABLE_FIELDS = SortableFields(
            mapOf(
                "status" to WorkspaceUsersSchema.status,
                "userId" to WorkspaceUsersSchema.userId
            )
        )
    }

    fun create(tenant: Tenant, workspaceUser: WorkspaceUser.ForCreate): WorkspaceUserEntity {
        val workspace = workspaceRepository.findOne(workspaceUser.workspaceId)
        val user = userRepository.findOne(workspaceUser.userId.value)
        val dao = WorkspaceUserEntity.new {
            workspaceId = WorkspaceEntity[workspace.id.value].id
            userId = user.id
            tenantId = tenant.id
            accessLevel = workspaceUser.accessLevel
            status = workspaceUser.status
        }
        return dao
    }

    fun searchByCriteria(
        pageRequest: PageRequest,
        workspaceUserSearchCriteria: WorkspaceUserSearchCriteria
    ): Page<WorkspaceUserEntity> {
        return workspaceUserSearchCriteria.toQuery().paginate(pageRequest, SORTABLE_FIELDS) {
            WorkspaceUserEntity.wrapRow(
                it
            )
        }
    }

    fun getById(id: Long): WorkspaceUserEntity? {
        return WorkspaceUserEntity.findById(id)
    }

    fun findOne(id: Long): WorkspaceUserEntity {
        return checks.exists(getById(id)) { "WorkspaceUser $id not found" }
    }

    fun update(workspaceUser: WorkspaceUser.ForUpdate): WorkspaceUserEntity {
        val dao = WorkspaceUserEntity.findByIdAndUpdate(workspaceUser.id.value) {
            it.status = workspaceUser.status ?: it.status
            it.accessLevel = workspaceUser.accessLevel ?: it.accessLevel
        }
        return dao!!
    }

    fun findByWorkspaceAndUserId(workspaceId: Workspace.Id, userId: User.Id): WorkspaceUserEntity {
        return WorkspaceUserEntity.find {
            (WorkspaceUsersSchema.workspaceId eq workspaceId.value) and
                    (WorkspaceUsersSchema.userId eq userId.value)
        }.singleOrNull() ?: throw IllegalArgumentException("Workspace user not found")
    }


}

fun WorkspaceUserSearchCriteria.toQuery(): Query {
    val query = WorkspaceUsersSchema
        .join(UserSchema, JoinType.INNER) { WorkspaceUsersSchema.userId eq UserSchema.id }
        .selectAll()
        .andWhere { WorkspaceUsersSchema.workspaceId eq workspaceId.value }

    if (searchTerm?.isNotBlank() == true) {
        val lowerSearchTerm = "%${searchTerm.lowercase()}%"
        query.andWhere {
            (UserSchema.email.lowerCase() like lowerSearchTerm) or
                    (UserSchema.properties.extract<String>("firstName").lowerCase() like lowerSearchTerm) or
                    (UserSchema.properties.extract<String>("lastName").lowerCase() like lowerSearchTerm)
        }
    }

    return query
}