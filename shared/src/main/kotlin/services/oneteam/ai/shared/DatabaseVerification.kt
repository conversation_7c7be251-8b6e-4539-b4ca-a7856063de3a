package services.oneteam.ai.shared

import org.jetbrains.exposed.sql.transactions.transaction
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.database.DBInterface
import services.oneteam.ai.shared.database.DatabaseUtils
import services.oneteam.ai.shared.database.RLSPolicy
import services.oneteam.ai.shared.domains.workspace.WorkspaceEntity

/**
 * Checks to ensure state of database is correct.
 */
class DatabaseVerification(val schemaName: String, private val database: DBInterface) {
    private val excludeTableFromTenantRLS = listOf("flyway_schema_history", "tenants")

    // to avoid infinite recursion, we exclude workspace_users from workspace RLS policies
    private val excludeTableFromWorkspaceRLS = listOf("workspace_users")

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    /**
     * Adds tenant_isolation_policy to tables that don't have it (excluding those in excludeTableFromRLS).
     */
    fun ensureTenantIsolation() {
        logger.info("Verifying database schema {}", schemaName)
        transaction(database.connectSuperUser()) {
            DatabaseUtils.listTables("public").filter { !excludeTableFromTenantRLS.contains(it) }.forEach { table ->
                // create tenant policy
                DatabaseUtils.isPolicySetOnTable(table, RLSPolicy.TENANT_ISOLATION.policyName).let { hasPolicy ->
                    if (!hasPolicy) {
                        logger.info("tenant_isolation_policy not set on table {}. Setting...", table)
                        DatabaseUtils.enableRowLevelSecurityTenantPolicyForSchema(table)
                    } else {
                        logger.info("{} is already set on table {}", RLSPolicy.TENANT_ISOLATION.policyName, table)
                    }
                }
                /*
                 * Enable workspace isolation policy for tables that have workspace_id column.
                 */
                if (!excludeTableFromWorkspaceRLS.contains(table)) {
                    if (table == WorkspaceEntity.table.tableName || DatabaseUtils.listColumns(table)
                            .contains("workspace_id")
                    ) {
                        DatabaseUtils.enableRowLevelSecurityWorkspacePolicyForSchema(table)
                    } else {
                        logger.info(
                            "Table {} does not have workspace_id column, skipping workspace policy creation.",
                            table
                        )
                    }
                } else {
                    logger.info("Table {} is excluded from workspace RLS policies.", table)
                }

                // enable RLS
                DatabaseUtils.isRLSEnabled(table).let { rlsEnabled ->
                    if (!rlsEnabled) {
                        logger.info("RLS not enabled on table $table. Enabling...")
                        DatabaseUtils.enableRowLevelSecurityForTable(table)
                    } else {
                        logger.info("RLS is already enabled on table $table")
                    }
                }
            }
            logger.info("Database schema {} status:", schemaName)
            val status = DatabaseUtils.printRLS(schemaName)
            status.filter { it.second }.joinToString("\n").let { logger.info("Tables with RLS: \n{}", it) }
            status.filter { !it.second }.joinToString("\n").let { logger.info("Tables WITHOUT RLS: \n{}", it) }
        }
    }

}