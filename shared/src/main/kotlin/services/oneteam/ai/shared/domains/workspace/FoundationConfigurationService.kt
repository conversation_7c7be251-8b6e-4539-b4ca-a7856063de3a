package services.oneteam.ai.shared.domains.workspace

import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.collection.foundation.FoundationRepository
import services.oneteam.ai.shared.domains.collection.foundation.FoundationSearchCriteria
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration.Identifier
import services.oneteam.ai.shared.helpers.CustomNanoId
import services.oneteam.ai.shared.withTenantTransactionScope
import java.time.Instant
import java.util.*

class FoundationConfigurationService(
    val check: Checks, private val dictionary: ResourceBundle, private val foundationRepository: FoundationRepository
) {

    fun createRootFoundationConfiguration(): FoundationConfiguration.ForApi {
        val name = FoundationConfiguration.Name(dictionary.getString("workspace") ?: "Workspace")
        val identifier = Identifier(name.value.split(" ").joinToString("_"))
        return create(
            FoundationConfiguration.ForCreate(
                name = name,
                description = null,
                relationship = FoundationConfiguration.Relationship.OneToMany,
                identifier = identifier,
            )
        )
    }

    private fun create(foundationConfiguration: FoundationConfiguration.ForCreate): FoundationConfiguration.ForApi {
        val now = Instant.now()
        return FoundationConfiguration.ForApi(
            id = FoundationConfiguration.Id(CustomNanoId.generate()),
            name = foundationConfiguration.name,
            description = foundationConfiguration.description,
            relationship = foundationConfiguration.relationship,
            metadata = EntityMetadata(
                createdAt = now, updatedAt = now
            ),
            identifier = foundationConfiguration.identifier,
        )
    }

    suspend fun canDeleteFoundationLevel(
        workspaceId: Workspace.Id,
        foundationConfigurationId: FoundationConfiguration.Id,
        foundationPageRequest: PageRequest
    ): Boolean =
        withTenantTransactionScope {
            val foundationSearchCriteria = FoundationSearchCriteria(
                workspaceId,
                foundationConfigurationIdList = listOf(foundationConfigurationId),
            )

            val foundation = foundationRepository.search(foundationSearchCriteria, foundationPageRequest)
            return@withTenantTransactionScope foundation.items.isEmpty()
        }
}