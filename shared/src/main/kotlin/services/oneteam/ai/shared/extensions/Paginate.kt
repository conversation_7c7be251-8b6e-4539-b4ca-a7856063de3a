package services.oneteam.ai.shared.extensions

import org.jetbrains.exposed.dao.Entity
import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.ResultRow
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.SortableFields

fun <T : Entity<Long>> Query.paginate(
    pageRequest: PageRequest, sortableFields: SortableFields, wrap: (ResultRow) -> T
): Page<T> {
    val total = count()

    val order = pageRequest.sort.fields.map { sf ->
        val col = sortableFields[sf.field]
        col to org.jetbrains.exposed.sql.SortOrder.valueOf(sf.direction.uppercase())
    }.toTypedArray()

    val results =
        orderBy(*order)
            .limit(pageRequest.pageSize)
            .offset((pageRequest.pageNumber.toLong() - 1) * pageRequest.pageSize)
            .toList()

    return Page(pageRequest, total, results.map(wrap))
}
