package services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration

import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.SqlExpressionBuilder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.deleteWhere
import services.oneteam.ai.shared.domains.tenant.Tenant

class FlowStepTypeConfigurationRepository {
    fun getById(id: Long): FlowStepTypeConfigurationEntity {
        return FlowStepTypeConfigurationEntity.findById(id)
            ?: throw IllegalArgumentException("FlowStepTypeConfiguration with id $id not found")
    }

    fun getByPrimaryIdentifier(primaryId: String): FlowStepTypeConfigurationEntity? {
        val entities = get {
            FlowStepTypeConfigurationSchema.primaryIdentifier eq primaryId
        }
        return entities.firstOrNull()
    }

    fun get(predicate: SqlExpressionBuilder.() -> Op<Boolean>): List<FlowStepTypeConfigurationEntity> {
        return FlowStepTypeConfigurationEntity.find { SqlExpressionBuilder.predicate() }.toList()
    }

    fun create(tenant: Tenant, flowStepTypeConfiguration: FlowStepTypeConfiguration): FlowStepTypeConfigurationEntity {
        val dao = FlowStepTypeConfigurationEntity.new {
            type = flowStepTypeConfiguration.type
            name = flowStepTypeConfiguration.name
            description = flowStepTypeConfiguration.description
            properties = flowStepTypeConfiguration.properties
            primaryIdentifier = flowStepTypeConfiguration.primaryIdentifier
            tenantId = tenant.id
        }
        return dao
    }

    fun delete(id: Long): JsonObject {
        val deletedCount = FlowStepTypeConfigurationSchema.deleteWhere { FlowStepTypeConfigurationSchema.id eq id }
        return buildJsonObject {
            if (deletedCount > 0) {
                put("message", "FlowStepTypeConfiguration with id $id has been deleted")
            } else {
                put("message", "FlowStepTypeConfiguration with id $id was not found")
            }
        }
    }

    fun getAll(): List<FlowStepTypeConfigurationEntity> {
        return FlowStepTypeConfigurationEntity.all().map { it }
    }

    fun getAllByQuery(query: Map<String, List<String>>): List<FlowStepTypeConfigurationEntity> {
        return FlowStepTypeConfigurationEntity.find {
            query.entries.fold(Op.TRUE as Op<Boolean>) { acc, entry ->
                val (key, values) = entry
                val column = when (key) {
                    "type" -> FlowStepTypeConfigurationSchema.type
                    "name" -> FlowStepTypeConfigurationSchema.name
                    "primaryIdentifier" -> FlowStepTypeConfigurationSchema.primaryIdentifier
                    else -> null
                }

                if (column != null && values.isNotEmpty()) {
                    acc and (column inList values)
                } else acc
            }
        }.toList()
    }

}