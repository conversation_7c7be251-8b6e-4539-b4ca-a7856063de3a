package services.oneteam.ai.shared.domains.workspace

import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.withTenantTransactionScope

class WorkspaceUserService(private val workspaceUserRepository: WorkspaceUserRepository) {

    suspend fun search(
        pageRequest: PageRequest, workspaceUserSearchCriteria: WorkspaceUserSearchCriteria
    ): Page<WorkspaceUser.ForApi> = withTenantTransactionScope {
        val searchResult = workspaceUserRepository.searchByCriteria(
            pageRequest, workspaceUserSearchCriteria
        )
        Page(
            searchResult.page,
            searchResult.total,
            searchResult.items.map { it.toDTO() }
        )
    }

    suspend fun create(
        workspaceUser: WorkspaceUser.ForCreate,
    ): WorkspaceUser.ForApi = withTenantTransactionScope { tenant ->
        workspaceUserRepository.create(tenant, workspaceUser).toDTO()
    }

    suspend fun update(workspaceUser: WorkspaceUser.ForUpdate): WorkspaceUser.ForApi = withTenantTransactionScope {
        workspaceUserRepository.findOne(workspaceUser.id.value)
        workspaceUserRepository.update(workspaceUser).toDTO()
    }

    suspend fun findByWorkspaceAndUserId(workspaceId: Workspace.Id, userId: User.Id): WorkspaceUser.ForApi =
        withTenantTransactionScope {
            workspaceUserRepository.findByWorkspaceAndUserId(workspaceId, userId).toDTO()
        }
}
