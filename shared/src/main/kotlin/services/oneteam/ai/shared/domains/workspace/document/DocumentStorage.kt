package services.oneteam.ai.shared.domains.workspace.document

import kotlinx.datetime.toKotlinInstant
import org.automerge.AmValue
import org.automerge.Document
import org.automerge.ObjectId
import org.jetbrains.exposed.dao.Entity
import org.jetbrains.exposed.dao.EntityClass
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.dao.id.IdTable
import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.Op
import services.oneteam.ai.shared.domains.tenant.TenantSchema
import services.oneteam.ai.shared.withTenantTransactionScope

typealias StorageKey = Array<String>
typealias DocumentId = String

abstract class StringEntityClass<out E : Entity<String>>(table: IdTable<String>, entityType: Class<E>? = null) :
    EntityClass<String, E>(table, entityType)

open class StringIdTable(name: String = "", columnName: String = "id", columnLength: Int = 10) : IdTable<String>(name) {
    override val id: Column<EntityID<String>> = varchar(columnName, columnLength).entityId()
    override val primaryKey by lazy { super.primaryKey ?: PrimaryKey(id) }
}

object AutomergeDatum : StringIdTable("automerge_data", "id", 255) {
    val data = blob("data")
    val tenantId = long("tenant_id").references(TenantSchema.id)
}

class AutomergeDataEntity(key: EntityID<String>) : Entity<String>(key) {
    companion object : StringEntityClass<AutomergeDataEntity>(AutomergeDatum)

    var data by AutomergeDatum.data
    var tenantId by AutomergeDatum.tenantId
}

class AutomergeDataRepository {

    suspend fun getByKeyPrefix(key: String): Iterable<AutomergeDataEntity> {
        return withTenantTransactionScope {
            return@withTenantTransactionScope AutomergeDataEntity
                .find(Op.build { AutomergeDatum.id like "${key}%" })
                .sortedBy { it.id.value }.asIterable()
        }
    }
}

class DocumentStorage(val repository: AutomergeDataRepository) {
    private data class Chunk(val key: StorageKey, val data: ByteArray?) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as Chunk

            if (!key.contentEquals(other.key)) return false
            if (data != null) {
                if (other.data == null) return false
                if (!data.contentEquals(other.data)) return false
            } else if (other.data != null) return false

            return true
        }

        override fun hashCode(): Int {
            var result = key.contentHashCode()
            result = 31 * result + (data?.contentHashCode() ?: 0)
            return result
        }
    }

    private enum class ChunkType {
        SNAPSHOT, INCREMENTAL
    }

    private fun chunkTypeFromKey(key: StorageKey): ChunkType? {
        if (key.size < 2) {
            return null
        }

        val chunkTypeStr = key[key.size - 2] // next-to-last element in key
        return when (chunkTypeStr) {
            "snapshot" -> ChunkType.SNAPSHOT
            "incremental" -> ChunkType.INCREMENTAL
            else -> null
        }
    }

    private suspend fun loadData(storageKey: StorageKey): Array<Chunk> {
        return repository.getByKeyPrefix(storageKey.joinToString("/")).map { entity ->
            Chunk(entity.id.value.split("/").toTypedArray(), entity.data.bytes)
        }.toTypedArray()
    }

    private fun mergeArrays(myArrays: List<ByteArray>): ByteArray {
        var mergedArray = ByteArray(0)
        myArrays.forEach { item ->
            mergedArray += item
        }
        return mergedArray
    }

    suspend fun loadDoc(documentId: DocumentId): Document? {
        val chunks = loadData(arrayOf(documentId))
        val binaries = mutableListOf<ByteArray>()
        for (chunk in chunks) {
            if (chunk.data == null) {
                continue
            }
            val chunkType = chunkTypeFromKey(chunk.key)
            if (chunkType == null) {
                continue
            }
            binaries.add(chunk.data)
        }
        val binary = mergeArrays(binaries)
        if (binary.isEmpty()) {
            return null
        }
        return Document.load(binary)
    }

    private fun valueToJson(doc: Document, value: AmValue): String {
        return when (value) {
            is AmValue.Bool -> "${value.value}"
            is AmValue.Counter -> "${value.value}"
            is AmValue.Int -> "${value.value}"
            is AmValue.Bytes -> "[${value.value.joinToString(",")}]"
            is AmValue.F64 -> "${value.value}"
            is AmValue.List -> listToJson(doc, value.id)
            is AmValue.Map -> mapToJson(doc, value.id)
            is AmValue.Null -> "null"
            is AmValue.Str -> "\"${value.value}\""
            is AmValue.Text -> "\"${doc.text(value.id).get()}\""
            is AmValue.Timestamp -> "\"${value.value.toInstant().toKotlinInstant()}\""
            is AmValue.UInt -> "${value.value}"
            else -> "\"other: $value\""
        }
    }

    private fun mapToJson(doc: Document, objectId: ObjectId): String {
        val mapEntries = doc.mapEntries(objectId)
        if (mapEntries.isEmpty) {
            return "empty map"
        }
        return "{" + mapEntries.get().map { "\"${it.key}\": ${valueToJson(doc, it.value)}" }.joinToString(",\n") + "}"
    }

    private fun listToJson(doc: Document, objectId: ObjectId): String {
        val listItems = doc.listItems(objectId)
        if (listItems.isEmpty) {
            println("empty map")
            return ""
        }
        return "[" + listItems.get().map { valueToJson(doc, it) }.joinToString(",") + "]"
    }
}