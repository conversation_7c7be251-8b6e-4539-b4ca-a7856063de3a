package services.oneteam.ai.shared.domains.workspace

import kotlinx.serialization.EncodeDefault
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.jetbrains.exposed.dao.id.EntityID
import services.oneteam.ai.shared.JsonValue.valueOrNull
import services.oneteam.ai.shared.database.BaseLongEntity
import services.oneteam.ai.shared.database.BaseLongEntityClass
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.NotFoundException
import services.oneteam.ai.shared.domains.ValidationErrors
import services.oneteam.ai.shared.domains.collection.form.FormAnswer
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.workspace.BaseSection.BaseQuestion
import services.oneteam.ai.shared.domains.workspace.BaseSection.Section
import services.oneteam.ai.shared.domains.workspace.validation.*

enum class CollaborationDocumentType {
    WORKSPACE_CONFIGURATION, FORM_ANSWER, FORM_ANNOTATION, FLOW_EXECUTION
}

interface CollaborationDocument {
    val type: CollaborationDocumentType
    fun descriptor(): String
}

interface HasId<T> {
    val id: T
}

@Serializable
@JvmInline
value class RowId(val value: String)

@Serializable
data class TableAnswerRow(
    override val id: RowId, val columns: Map<BaseSection.Id, FormAnswer<*>>
) : HasId<RowId> {
    fun findAnswerByQuestionId(id: BaseSection.Id): FormAnswer<*>? {
        return columns[id]
    }

}

@Serializable
@JvmInline
value class ItemId(val value: String)

@Serializable
data class ListAnswerItem(
    override val id: ItemId, val item: Map<BaseSection.Id, FormAnswer<*>>
) : HasId<ItemId> {

}

@Serializable
data class FileAnswerValue(
    val path: String, val name: String
)

@Serializable
enum class AlertType {
    @SerialName("blocker")
    BLOCKER,

    @SerialName("warning")
    WARNING,

    @SerialName("info")
    INFO,

    @SerialName("success")
    SUCCESS
}

@Serializable
enum class AlertOperation {
    @SerialName("add")
    ADD,

    @SerialName("remove")
    REMOVE,
}

@Serializable
data class ResolvedType(
    val status: Boolean,
    val comment: String? = null,
    val resolvedBy: String,
    val resolvedAt: String,
)

@Serializable
data class AlertLocation(
    val questionId: String,
    val columnId: String? = null,
    val rowId: String? = null,
)

@Serializable
data class FormAlert(
    val type: AlertType,
    val operation: AlertOperation? = AlertOperation.ADD,
    val groupIdentifier: String? = null,
    val message: String? = null,
    val resolved: ResolvedType? = null,
    val location: AlertLocation,
)

@Serializable
sealed class Workspace {
    abstract val name: Name
    abstract val key: Key
    abstract val description: Description?

    @Serializable
    @JvmInline
    value class Id(val value: Long)

    @Serializable
    @JvmInline
    value class DocumentId(val value: String)

    @Serializable
    @JvmInline
    value class Name(val value: String)

    @Serializable
    @JvmInline
    value class Key(val value: String)

    @Serializable
    @JvmInline
    value class Description(val value: String)

    @Serializable
    data class ForCreate(
        override val name: Name,
        override val key: Key,
        override val description: Description? = null,
        val documentId: DocumentId? = null,
    ) : Workspace()

    @Serializable
    data class ForUpdate(
        val id: Id,
        override val name: Name,
        override val key: Key,
        override val description: Description? = null,
        val documentId: DocumentId?,
        val configuration: ForJson
    ) : Workspace()

    @Serializable
    data class ForUpdateDetails(
        val id: Id,
        override val name: Name,
        override val key: Key,
        override val description: Description? = null,
    ) : Workspace()

    @Serializable
    data class ForApi(
        val id: Id,
        override val name: Name,
        override val key: Key,
        override val description: Description? = null,
        val documentId: DocumentId?,
        val metadata: EntityMetadata
    ) : Workspace()

    @Serializable
    @OptIn(ExperimentalSerializationApi::class)
    data class ForJson(
        val id: Id,
        override val name: Name,
        override val key: Key,
        override val description: Description? = null,
        @EncodeDefault val foundations: OrderedMap<FoundationConfiguration.Id, FoundationConfiguration.ForApi>,
        @EncodeDefault val forms: Map<FormConfiguration.Id, FormConfiguration.ForJson> = mapOf(),
        @EncodeDefault val flows: OrderedMap<FlowConfiguration.Id, FlowConfiguration.ForJson> = OrderedMap(emptyList()),
        @EncodeDefault val series: Map<SeriesConfiguration.Id, SeriesConfiguration.ForApi> = mapOf(),
        @EncodeDefault val labels: Map<LabelConfiguration.Id, LabelConfiguration.ForApi> = mapOf(),
        val metadata: EntityMetadata,
        val errors: List<ConstraintError>? = emptyList()
    ) : Workspace(), CollaborationDocument, BaseConfigValidator {
        @EncodeDefault
        override val type = CollaborationDocumentType.WORKSPACE_CONFIGURATION

        override fun descriptor(): String {
            return name.value
        }

        fun validateConfiguration(path: String, context: WorkspaceValidationContext): Errors {
            val errors = super.validateConfiguration(path)
            forms.forEach { (id, form) ->
                form.validateConfiguration("$.forms.${id.value}").let { formErrors ->
                    errors.addAll(formErrors)
                }
            }
            flows.entities.forEach { (id, flow) ->
                flow.validateConfiguration("$.flows.entities.${id.value}", context).let { flowErrors ->
                    errors.addAll(flowErrors)
                }
            }
            return errors
        }

        fun findFlow(id: FlowConfiguration.Id): FlowConfiguration.ForJson {
            return flows.entities[id] ?: throw NotFoundException("No flow configuration found with ID ${id.value}")
        }

        fun findForm(id: FormConfiguration.Id): FormConfiguration.ForJson {
            return forms[id] ?: throw NotFoundException("No form configuration found with ID ${id.value}")
        }

        fun findInterval(seriesId: SeriesConfiguration.Id, intervalId: IntervalId): Interval {
            return series[seriesId]?.intervals?.entities?.get(intervalId)
                ?: throw NotFoundException("No interval found for ID ${intervalId.value} in series configuration ${seriesId.value}")
        }

        fun findSeriesAndIntervalByIntervalId(intervalId: IntervalId): Pair<SeriesConfiguration.ForApi, Interval> {
            series.forEach { (seriesId, seriesConfig) ->
                seriesConfig.intervals.entities[intervalId]?.let { interval ->
                    return Pair(seriesConfig, interval)
                }
            }
            throw NotFoundException("No series configuration found with interval ID ${intervalId.value}")
        }

        fun findSeries(id: SeriesConfiguration.Id): SeriesConfiguration.ForApi {
            return series[id] ?: throw NotFoundException("No series configuration found with ID ${id.value}")
        }
    }


    fun validate(): ValidationErrors {
        val errors = Validator<Workspace>(
            listOf(
                NameValidator.build { obj: Workspace -> obj.name.value },
                KeyValidator.build { obj: Workspace -> obj.key.value },
                Validator.conditionalConstraint(
                    (this is ForUpdate), IdValidator.build<Workspace> { obj -> (obj as ForUpdate).id.value })
            )
        ).validate(this)

        return ValidationErrors.from(errors)
    }
}

@Serializable
data class OrderedMap<ID, T : HasId<ID>> constructor(val order: MutableList<ID>, val entities: MutableMap<ID, T>) {

    companion object {
        fun <ID, T : HasId<ID>> empty(): OrderedMap<ID, T> {
            return OrderedMap(mutableListOf(), mutableMapOf())
        }

        fun <ID, T : HasId<ID>> of(
            order: List<ID>, entities: Map<ID, T>
        ): OrderedMap<ID, T> {
            return OrderedMap(order.toMutableList(), entities.toMutableMap())
        }

    }

    constructor(values: List<T>) : this(
        order = values.map { it.id }.toMutableList(),
        entities = values.associateBy { it.id }.toMutableMap()
    )

    fun add(entity: T) {
        order += entity.id
        entities[entity.id] = entity
    }

    fun remove(entity: T) {
        order -= entity.id
        entities.remove(entity.id)
    }

    fun remove(id: ID) {
        order -= id
        entities.remove(id)
    }

    fun toList(): List<T> {
        return order.map { entities[it]!! }
    }

    fun findNext(id: ID): T? {
        order.indexOf(id).let {
            require(it != -1) { "ID not found in ordered list" }
            if (it == order.size - 1) {
                return null
            }
            val idOfNext = order[it + 1]
            return entities[idOfNext] ?: throw NotFoundException("ID not found in entities")
        }
    }

    fun isValid(): Boolean {
        // check that order matches all of the values in entities
        return entities.keys.size == order.size && entities.keys.containsAll(order) && order.containsAll(entities.keys)
    }

}

class WorkspaceEntity(id: EntityID<Long>) : BaseLongEntity(id, Workspaces) {
    companion object : BaseLongEntityClass<WorkspaceEntity>(Workspaces)

    var name by Workspaces.name
    var key by Workspaces.key
    var description by Workspaces.description
    var tenantId by Workspaces.tenantId
    var documentId by Workspaces.documentId
    var configuration by Workspaces.configuration
    var deleted by Workspaces.deleted

}

@Serializable
sealed class FoundationConfiguration {

    abstract val identifier: Identifier
    abstract val name: Name
    abstract val description: Description?
    abstract val relationship: Relationship

    @Serializable
    enum class Relationship {
        OneToOne, OneToMany, ManyToOne, ManyToMany
    }

    @Serializable
    @JvmInline
    value class Identifier(val value: String)

    @Serializable
    @JvmInline
    value class Id(val value: String)

    @Serializable
    @JvmInline
    value class Name(val value: String)

    @Serializable
    @JvmInline
    value class Description(val value: String?)

    @Serializable
    @JvmInline
    value class ObjectId(val value: String)

    @Serializable
    data class ForCreate(
        override val name: Name,
        override val description: Description? = null,
        override val relationship: Relationship,
        override val identifier: Identifier,
    ) : FoundationConfiguration()

    @Serializable
    data class ForUpdate(
        val id: Id,
        override val name: Name,
        override val description: Description? = null,
        override val relationship: Relationship,
        val objectId: ObjectId,
        override val identifier: Identifier
    ) : FoundationConfiguration()

    @Serializable
    data class ForApi(
        override val id: Id,
        override val name: Name,
        override val description: Description? = null,
        override val relationship: Relationship,
        val metadata: EntityMetadata,
        override val identifier: Identifier
    ) : FoundationConfiguration(), HasId<Id> {
        fun toMap(): JsonObject {
            return JsonObject(
                mapOf(
                    "id" to valueOrNull(id.value),
                    "name" to valueOrNull(name.value),
                    "relationship" to valueOrNull(relationship.name),
                    "description" to valueOrNull(description?.value),
                )
            )
        }
    }

    fun validate(): ValidationErrors {
        val errors = Validator<FoundationConfiguration>(
            listOf(
                NameValidator.build { obj: FoundationConfiguration -> obj.name.value },
            )
        ).validate(this)

        return ValidationErrors.from(errors)
    }
}

/**
 * Form Configuration
 */

@Serializable
sealed class FormConfiguration {
    abstract val name: Name
    abstract val key: Key
    abstract val description: Description?
    abstract val status: Status?
    abstract val labels: List<LabelConfiguration.Id>?

    @Serializable
    @JvmInline
    value class Id(val value: String)

    @Serializable
    @JvmInline
    value class Name(val value: String)

    @Serializable
    @JvmInline
    value class Key(val value: String)

    @Serializable
    @JvmInline
    value class Description(val value: String?)

    @Serializable
    @JvmInline
    value class Status(val value: String)

    @Serializable
    data class ForCreate(
        override val name: Name,
        override val key: Key,
        override val description: Description? = null,
        override val status: Status? = null,
        override val labels: List<LabelConfiguration.Id>? = null,
    ) : FormConfiguration()

    @Serializable
    data class ForUpdate(
        val id: Id,
        override val name: Name,
        override val key: Key,
        override val description: Description? = null,
        override val status: Status? = null,
        override val labels: List<LabelConfiguration.Id>? = null,

        ) : FormConfiguration()

    @Serializable
    data class ForApi(
        val id: Id,
        override val name: Name,
        override val key: Key,
        override val description: Description? = null,
        override val status: Status? = null,
        override val labels: List<LabelConfiguration.Id>? = null,
        val metadata: EntityMetadata
    ) : FormConfiguration()

    @Serializable
    // TODO these fields should be using domain types
    data class ForJson(
        val content: List<BaseSection>? = null,
        val description: String? = null,
        val id: String,
        val key: String,
        val foundationId: String,
        val seriesId: String? = null,
        val name: String,
        val level: Int,
        val labels: List<LabelConfiguration.Id>? = listOf(),
        val metadata: EntityMetadata,
        val properties: JsonObject? = null
    ) : BaseConfigValidator {
        override fun validateConfiguration(path: String): Errors {
            val errors = super.validateConfiguration(path)
            content?.forEachIndexed { index, baseSection ->
                baseSection.validateConfiguration("$path.content.$index").let { sectionErrors ->
                    errors.addAll(sectionErrors)
                }
            }

            QuestionDuplicateValidator.build(this).validate(path, errors)

            return errors
        }

        fun findQuestion(questionId: BaseSection.Id): BaseQuestion? {
            return content?.mapNotNull {
                when (it) {
                    is BaseQuestion -> it
                    is Section -> it.findQuestion(questionId)
                }
            }?.firstOrNull { it.id == questionId }
        }

        fun findQuestionByIdentifier(string: String): BaseQuestion? {
            return content?.mapNotNull {
                when (it) {
                    is BaseQuestion -> it
                    is Section -> it.findQuestionByIdentifier(string)
                }
            }?.firstOrNull { it.identifier == string }
        }

        fun listAllQuestions(): List<BaseQuestion> {
            return content?.mapNotNull {
                when (it) {
                    is BaseQuestion -> listOf(it)
                    is Section -> it.listAllQuestions()
                }
            }?.flatten() ?: emptyList()
        }

        fun toMap(): JsonObject {
            return JsonObject(
                mapOf(
                    "id" to valueOrNull(id),
                    "key" to valueOrNull(key),
                    "name" to valueOrNull(name),
                    "seriesId" to valueOrNull(seriesId),
                )
            )
        }
    }

    fun validate(): ValidationErrors {
        val errors = Validator<FormConfiguration>(
            listOf(
                NameValidator.build { obj: FormConfiguration -> obj.name.value },
            )
        ).validate(this)

        return ValidationErrors.from(errors)
    }

}

/**
 * Series Configuration
 */
@Serializable
sealed class SeriesConfiguration {
    abstract val name: Name
    abstract val description: Description?
    abstract val intervals: OrderedMap<IntervalId, Interval>

    @Serializable
    @JvmInline
    value class Id(val value: String)

    @Serializable
    @JvmInline
    value class Name(val value: String)

    @Serializable
    @JvmInline
    value class Description(val value: String?)

    @Serializable
    data class ForCreate(
        override val name: Name,
        override val description: Description? = null,
        override val intervals: OrderedMap<IntervalId, Interval> = OrderedMap(emptyList())
    ) : SeriesConfiguration()

    @Serializable
    data class ForUpdate(
        val id: Id,
        override val name: Name,
        override val description: Description? = null,
        override val intervals: OrderedMap<IntervalId, Interval> = OrderedMap(emptyList())
    ) : SeriesConfiguration()

    @Serializable
    data class ForApi(
        val id: Id,
        override val name: Name,
        override val description: Description? = null,
        override val intervals: OrderedMap<IntervalId, Interval> = OrderedMap(emptyList()),
        val metadata: EntityMetadata
    ) : SeriesConfiguration() {
        fun toMap(): JsonElement {
            return JsonObject(
                mapOf(
                    "id" to valueOrNull(id.value),
                    "name" to valueOrNull(name.value),
                )
            )
        }

        fun findInterval(id: IntervalId): Interval {
            return intervals.entities[id] ?: throw NotFoundException("Interval ${id.value} not found")
        }

        private fun findIntervalOrNull(id: IntervalId): Interval? {
            return intervals.entities[id]
        }

        private fun findIntervalByNameOrNull(name: IntervalName): Interval? {
            return intervals.entities.values.firstOrNull { it.name == name }
        }

        fun findIntervalByIdOrName(intervalIdOrName: String): Interval {
            return findIntervalOrNull(IntervalId(intervalIdOrName)) ?: findIntervalByNameOrNull(
                IntervalName(
                    intervalIdOrName
                )
            )
            ?: throw NotFoundException("No interval found with ID or name ${intervalIdOrName} in series configuration ${id.value}")
        }

        fun findPreviousIntervalOrNull(id: IntervalId): Interval? {
            val index = intervals.order.indexOf(id)
            if (index <= 0) {
                return null
            }
            val previousId = intervals.order[index - 1]
            return findIntervalOrNull(previousId)
        }

        fun getIntervals(): JsonArray {
            return JsonArray(intervals.order.map { intervalId ->
                JsonObject(
                    mapOf(
                        "id" to valueOrNull(intervalId.value),
                        "name" to valueOrNull(intervals.entities[intervalId]?.name?.value ?: "")
                    )
                )
            })
        }

        fun findNextIntervalOrNull(id: IntervalId): Interval? {
            val index = intervals.order.indexOf(id)
            if (index == -1 || index == intervals.order.size - 1) {
                return null
            }
            val nextId = intervals.order[index + 1]
            return findIntervalOrNull(nextId)
        }
    }

    fun validate(): ValidationErrors {
        val errors = Validator<SeriesConfiguration>(
            listOf(
                NameValidator.build { obj: SeriesConfiguration -> obj.name.value },
            )
        ).validate(this)

        return ValidationErrors.from(errors)
    }
}

@Serializable
@JvmInline
value class IntervalId(val value: String)

@Serializable
@JvmInline
value class IntervalName(val value: String)

@Serializable
data class Interval(
    override val id: IntervalId, val name: IntervalName
) : HasId<IntervalId> {
    fun toMap(): JsonObject {
        return JsonObject(
            mapOf(
                "id" to valueOrNull(id.value),
                "name" to valueOrNull(name.value),
            )
        )
    }
}

/**
 * Labels Configuration
 */

@Serializable
enum class LabelAvailableTo {
    @SerialName("flowConfiguration")
    FLOW_CONFIGURATION,

    @SerialName("formConfiguration")
    FORM_CONFIGURATION,
}


@Serializable
sealed class LabelConfiguration {
    abstract val name: Name
    abstract val description: Description?
    abstract val color: Color?
    abstract val availableTo: AvailableTo

    @Serializable
    @JvmInline
    value class Id(val value: String)

    @Serializable
    @JvmInline
    value class Name(val value: String)

    @Serializable
    @JvmInline
    value class Description(val value: String?)

    @Serializable
    @JvmInline
    value class Color(val value: String?)

    @Serializable
    @JvmInline
    value class AvailableTo(val value: List<LabelAvailableTo>)

    @Serializable
    data class ForCreate(
        override val name: Name,
        override val description: Description? = null,
        override val color: Color? = null,
        override val availableTo: AvailableTo = AvailableTo(
            listOf(LabelAvailableTo.FLOW_CONFIGURATION, LabelAvailableTo.FORM_CONFIGURATION)
        )
    ) : LabelConfiguration()

    @Serializable
    data class ForUpdate(
        val id: Id,
        override val name: Name,
        override val description: Description? = null,
        override val color: Color? = null,
        override val availableTo: AvailableTo = AvailableTo(
            listOf(LabelAvailableTo.FLOW_CONFIGURATION, LabelAvailableTo.FORM_CONFIGURATION)
        )
    ) : LabelConfiguration()

    @Serializable
    data class ForApi(
        val id: Id,
        override val name: Name,
        override val description: Description? = null,
        override val color: Color? = null,
        override val availableTo: AvailableTo = AvailableTo(
            listOf(LabelAvailableTo.FLOW_CONFIGURATION, LabelAvailableTo.FORM_CONFIGURATION)
        ),
        val metadata: EntityMetadata
    ) : LabelConfiguration() {
        fun toMap(): JsonElement {
            return JsonObject(
                mapOf(
                    "id" to valueOrNull(id.value),
                    "name" to valueOrNull(name.value),
                    "color" to valueOrNull(color?.value),
                    "availableTo" to JsonArray(
                        availableTo.value.map { valueOrNull(it.name) }),
                )
            )
        }
    }

    fun validate(): ValidationErrors {
        val errors = Validator<LabelConfiguration>(
            listOf(
                NameValidator.build { obj: LabelConfiguration -> obj.name.value },
            )
        ).validate(this)

        return ValidationErrors.from(errors)
    }
}

val x = WorkspaceVersionEntity(EntityID(1L, WorkspaceVersions))