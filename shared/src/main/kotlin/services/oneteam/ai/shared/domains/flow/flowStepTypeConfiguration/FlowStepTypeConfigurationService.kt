package services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject
import services.oneteam.ai.shared.withTenantTransactionScope

@Serializable
data class FlowStepTypeConfiguration(
    val id: Long,
    val primaryIdentifier: String,
    val type: String,
    val name: String,
    val description: String?,
    val properties: FlowStepType.Properties?,
    val tenantId: Long
)

class FlowStepTypeConfigurationService(private val repository: FlowStepTypeConfigurationRepository) {

    suspend fun getById(id: Long): FlowStepTypeConfiguration = withTenantTransactionScope {
        return@withTenantTransactionScope formatConfiguration(repository.getById(id).toDTO())
    }

    suspend fun getByPrimaryIdentifier(id: String): FlowStepTypeConfiguration? = withTenantTransactionScope {
        return@withTenantTransactionScope repository.getByPrimaryIdentifier(id)?.toDTO()
    }

    suspend fun create(flowStepTypeConfiguration: FlowStepTypeConfiguration): FlowStepTypeConfiguration =
        withTenantTransactionScope { tenant ->
            return@withTenantTransactionScope repository.create(tenant, flowStepTypeConfiguration).toDTO()
        }

    suspend fun delete(id: Long): JsonObject = withTenantTransactionScope {
        return@withTenantTransactionScope repository.delete(id)
    }

    suspend fun getAll(): List<FlowStepTypeConfiguration> = withTenantTransactionScope {
        return@withTenantTransactionScope repository.getAll().map { formatConfiguration(it.toDTO()) }
    }

    suspend fun getAllByQuery(query: Map<String, List<String>>): List<FlowStepTypeConfiguration> =
        withTenantTransactionScope {
            return@withTenantTransactionScope repository.getAllByQuery(query).map { formatConfiguration(it.toDTO()) }
        }

    private fun formatConfiguration(entity: FlowStepTypeConfiguration): FlowStepTypeConfiguration {
        return entity
    }
}