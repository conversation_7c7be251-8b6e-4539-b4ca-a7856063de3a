package services.oneteam.ai.shared.domains.tenant

import kotlinx.serialization.Serializable
import org.jetbrains.exposed.sql.transactions.transaction

@Serializable
data class Tenant(
    val id: Long,
    val name: String,
    val originUrl: String,
    val internalUrl: String,
    val internalSyncUrl: String
)

class TenantService(private val repository: TenantRepository) {

    private fun getByOriginUrl(originUrl: String): Tenant? {
        return repository.getByOriginUrl(originUrl)
    }

    private fun getByInternalUrl(internalUrl: String): Tenant? {
        return repository.getByInternalUrl(internalUrl)
    }

    fun getByInternalUrlOrOriginUrl(url: String): Tenant? {
        return getByInternalUrl(url) ?: getByOriginUrl(url)
    }

    fun findAll(): List<Tenant> {
        return transaction {
            return@transaction repository.getAll()
        }
    }
}