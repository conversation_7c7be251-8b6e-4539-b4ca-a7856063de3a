package services.oneteam.ai.shared

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.database.DatabaseUtils
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.middlewares.RequestContext
import kotlin.coroutines.coroutineContext

val logger: Logger = LoggerFactory.getLogger("services.oneteam.ai.shared.TenantScope")

/**
 * Executes a block of code within the context of a tenant.
 * Use this INSTEAD of `coroutineContext[RequestContext]?.tenant`
 */
suspend fun <T> withTenantScope(block: suspend (tenant: Tenant) -> T): T {
    val tenant = coroutineContext[RequestContext]?.tenant
    require(tenant != null) { "Tenant is not available in the current coroutine context" }
    /*
       without limited parallelism, this can cause too many concurrent database connections and lead to timeouts.
       the best value to use here is probably related to the size of the database pool, and the length of the connection timeout.
       @see TenantScopeConcurrencyTest
     */
    return withContext(Dispatchers.IO.limitedParallelism(100)) {
        return@withContext block(tenant)
    }
}

/**
 * Executes a block of code within the context of a tenant AND a transaction.
 * If there is no current transaction, it uses [DatabaseUtils.dbQueryWithTenant] to execute the block.
 * Otherwise, it retrieves the tenant from the [RequestContext] and executes the block directly.
 *
 * It is intended that this be used in the service layer to ensure the business methods are wrapped
 * in a transaction. If one service method uses another service method, the transaction will be
 * shared, and the tenant context will be preserved.
 *
 * After the `withTenantTransactionScope` block completes, the transaction will be committed or rolled back and the
 * current transaction should be null, after which subsequent calls to `withTenantTransactionScope` will create a new transaction.
 *
 * In addition to this:
 *
 * - repositories should not create transactions themselves or use DatabaseUtils directly. They should expect a transaction to be provided by the service layer.
 * - repository methods no longer need to be suspend functions, as they will always be called within a coroutine context that provides the tenant.
 *
 * @param block The suspend function to execute with the tenant context.
 * @return The result of the block execution.
 */
suspend fun <T> withTenantTransactionScope(block: suspend (tenant: Tenant) -> T): T {
    return withTenantScope { tenant ->
        return@withTenantScope if (TransactionManager.currentOrNull() == null) {
            logger.trace(
                "withTenantScope: No current transaction, using DatabaseUtils.dbQueryWithTenant for tenant {}",
                tenant.id
            )
            // No existing transaction so start a new one.
            // Configure the database connection with Row Level Security parameters.
            DatabaseUtils.dbQueryWithTenant { tenant ->
                block(tenant)
            }
        } else {
            logger.trace(
                "withTenantScope: Current transaction {} exists, using coroutineContext tenant {}",
                TransactionManager.currentOrNull(),
                tenant.id
            )
            // we already have a transaction so just execute the block
            block(tenant)
        }
    }
}