package services.oneteam.ai.shared.domains.workspace.document

import io.kotest.common.runBlocking
import io.kotest.matchers.shouldBe
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.MockHttpClient
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.proxy.ExternalProxyService
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration.*
import services.oneteam.ai.shared.domains.workspace.OrderedMap
import services.oneteam.ai.shared.domains.workspace.Workspace
import java.time.Instant

class ApiDocumentServiceTest {

    private val logger = LoggerFactory.getLogger(javaClass)
    private val stepTypeConfigurationService = mockFlowStepTypeConfigurationService()
    private val documentService = ApiDocumentService(
        ExternalProxyService("token", MockHttpClient().client), stepTypeConfigurationService
    )

    fun mockFlowStepTypeConfigurationService(): FlowStepTypeConfigurationService {
        val mockService = mockk<FlowStepTypeConfigurationService>()
        coEvery { mockService.getAllByQuery(any()) } returns emptyList()
        return mockService;
    }

    @Test
    fun `should validate document with unknown properties`() = runTest {
        val jsonString = """
            {
                "id": 1,
                "name": "TEST",
                "key": "KEY",
                "description": "Description",
                "unknownProperty": {
                    "unknownKey": "unknownValue"
                },
                "type": "WORKSPACE_CONFIGURATION",
                "foundations": {"order":[], "entities":{}}, 
                "forms": {}, 
                "flows": {"order":[], "entities":{}}, 
                "series": {}, 
                "labels": {},
                "metadata": {"createdAt": "2023-10-01T00:00:00Z", "updatedAt": "2023-10-01T00:00:00Z"}
            }
        """.trimIndent()

        val validationResult = documentService.validate(jsonString)

        // $: property 'unknownProperty' is not defined in the schema and the schema does not allow additional properties
        assertThat(validationResult.errors).extracting("message")
            .contains("$: property 'unknownProperty' is not defined in the schema and the schema does not allow additional properties")
    }

    @Test
    fun `should validate document`() {

        val input = emptyWorkspace().copy(
            foundations = OrderedMap.of(
                listOf(
                    Id("ID1"), Id("ID2")
                ), mapOf(
                    Id("ID1") to ForApi(
                        Id("ID1"),
                        Name("NAME"),
                        Description(""),
                        Relationship.OneToMany,
                        EntityMetadata(Instant.now(), Instant.now()),
                        Identifier("IDENTIFIER1"),
                    ), Id("ID2") to ForApi(
                        Id("ID2"),
                        Name("NAME"),
                        Description(""),
                        Relationship.OneToMany,
                        EntityMetadata(Instant.now(), Instant.now()),
                        Identifier("IDENTIFIER1"),
                    )
                )
            )
        )

        val validationResult = validate(input)

        validationResult.errors.size shouldBe 2

        /*
        Expect:

            [
              {
                "key": "name",
                "type": "duplicate",
                "path": "$.foundations.entities.ID1.name",
                "constraintDetail": "Duplicate value: name",
                "message": "errors.common.name.duplicate"
              },
              {
                "key": "name",
                "type": "duplicate",
                "path": "$.foundations.entities.ID2.name",
                "constraintDetail": "Duplicate value: name",
                "message": "errors.common.name.duplicate"
              }
            ]
         */
    }

    fun emptyWorkspace() = Workspace.ForJson(
        Workspace.Id(1),
        Workspace.Name("TEST"),
        Workspace.Key("KEY"),
        Workspace.Description("Description"),
        OrderedMap(emptyList()),
        emptyMap(),
        OrderedMap(emptyList()),
        emptyMap(),
        emptyMap(),
        EntityMetadata(Instant.now(), Instant.now()),
        errors = emptyList()
    )

    fun validate(input: Workspace.ForJson): ValidationResult = runBlocking {
        val json = Json { encodeDefaults = true }
        val jsonString = json.encodeToString(input)
        logger.debug(jsonString)
        val validationResult = documentService.validate(jsonString)
        logger.debug(json.encodeToString(validationResult.errors))
        return@runBlocking validationResult
    }
}