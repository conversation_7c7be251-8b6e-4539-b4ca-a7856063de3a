module.exports = {
  root: true,
  env: { browser: true, es2020: true },
  extends: [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:react-hooks/recommended",
    "plugin:storybook/recommended"
  ],
  ignorePatterns: ["!.*", "dist", ".eslintrc.cjs"],
  parser: "@typescript-eslint/parser",
  plugins: ["react-refresh"],
  rules: {
    "react-refresh/only-export-components": [
      "warn",
      { allowConstantExport: true }
    ],
    curly: "error",
    "no-restricted-imports": [
      "error",
      {
        paths: [
          {
            name: "nanoid",
            message:
              "Please use customNanoId from customNanoIdHelper instead of importing nanoid directly."
          }
        ]
      }
    ]
  },
  overrides: [
    {
      files: ["src/helpers/customNanoIdHelper.ts"],
      rules: {
        "no-restricted-imports": "off"
      }
    }
  ]
  // https://github.com/eslint/eslint/discussions/17047
};
