meta {
  name: Create Execution For SGPIT "iterator-foreach"
  type: http
  seq: 5
}

post {
  url: {{Origin}}{{prefix}}/workspaces/{{workspaceId}}/flow
  body: json
  auth: none
}

body:json {
  {
   "flowConfigurationId": "iteratoraforeach",    
    "workspaceVersionId": {{workspaceVersion.id}},
   "settings": {
     "timeoutMins": 1
   },  "event": {
      "workspaceId": {{workspaceId}},
      "id": "eventid",
      "tenantId": 1,
      "eventProperties": {
        "key": "START_flow_manually_from_form",
        "buttonLabel": "Run form flow",
        "form": {
          "id": {{form.id}},
          "foundationId": {{form.foundationId}},
          "formConfiguration": {
            "id": "{{form.formConfigurationId}}",
            "key": "unknown",
            "name": "Name",
            "seriesId": "{{form.seriesId}}"
          },
        "documentId": "{{form.documentId}}",
        "intervalId": "{{form.intervalId}}"
        }
      }
    },
    "variables": [
        {
          "type": "form.{{form.formConfigurationId}}", 
          "identifier": "form",
          "value": "{{form.id}}" 
        }
    ],
    "trigger": {
      "id": "trigger1",
      "primaryIdentifier": "manualTriggerFromForm"
    }
  }
}

script:pre-request {
  const { customNanoId } = require("./customNanoId");
  const body = req.getBody();
  
  var newBodyString = body.replaceAll(/"null"/g, ("null", ()=>null));
  
  if (!bru.getVar("form.seriesId")) {
    newBodyString = newBodyString.replaceAll(/"{{form.seriesId}}"/g, ("null", ()=>null));
  }
  if (!bru.getVar("form.intervalId")) {
    newBodyString = newBodyString.replaceAll(/"{{form.intervalId}}"/g, ("null", ()=>null));
  }
  console.log(newBodyString)
  
  req.setBody(
    newBodyString
  )
}

script:post-response {
  const flowExecutionId = res.body.data.id
  bru.setVar("flowExecutionId",flowExecutionId)
}
