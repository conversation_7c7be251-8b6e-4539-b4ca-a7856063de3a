package services.oneteam.ai.app.domains.auth

import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.AuthenticatedUser
import services.oneteam.ai.shared.AutoLogout
import services.oneteam.ai.shared.UserPrincipal
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.domains.user.UserRepository
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.withTenantTransactionScope
import kotlin.coroutines.coroutineContext


@Serializable
data class OneTeamVerifyResponse(
    val oneTeamAI: <PERSON>ole<PERSON>? = false,
    val verified: Boolean,
    val success: <PERSON>olean
)

@Serializable
data class OneTeamTokenUser(
    val id: Long,
    val firstname: String,
    val lastname: String,
    val email: String,
    val hostId: Int,
    val autoLogoutIdleTimer: AutoLogout,
)

val jsonSerializer = Json { ignoreUnknownKeys = true }

class AuthService(private val userRepository: UserRepository) {
    private val logger: Logger = LoggerFactory.getLogger(javaClass)
    suspend fun login(token: String): UserPrincipal {
        logger.debug("login {}", coroutineContext[RequestContext])
        val tenant = coroutineContext[RequestContext]!!.tenant
        val client = HttpClient() {
            install(ContentNegotiation) {
                json()
            }
        }
        val response = client.post("${tenant.originUrl}/api/auth/verify") {
            headers {
                append(HttpHeaders.Authorization, "Bearer $token")
                append(HttpHeaders.Origin, tenant.originUrl)
            }
        }

        if (response.status.value != 200) {
            client.close()
            throw Exception("Invalid token")
        }
        client.close()

        val verifyResponse: OneTeamVerifyResponse = response.body()
        val canAccessOneTeamAI = verifyResponse.oneTeamAI ?: false;
        if (!canAccessOneTeamAI) {
            throw Exception("Invalid access")
        }

        //return user object to caller
        val data = token.split(".")[1]
        val jsonString = String(java.util.Base64.getDecoder().decode(data))
        val tokenUser = jsonSerializer.decodeFromString<OneTeamTokenUser>(jsonString)
        val user = User.ForCreate(
            User.Id(tokenUser.id),
            tokenUser.email,
            tenant.id,
            User.Properties(tokenUser.firstname, tokenUser.lastname)
        )
        withTenantTransactionScope {
            userRepository.updateOrCreate(tenant, user)
        }
        return UserPrincipal(
            AuthenticatedUser(
                tokenUser.id,
                tokenUser.firstname,
                tokenUser.lastname,
                tokenUser.email,
                tenant.id,
                tokenUser.hostId,
                tokenUser.autoLogoutIdleTimer
            )
        )
    }
}